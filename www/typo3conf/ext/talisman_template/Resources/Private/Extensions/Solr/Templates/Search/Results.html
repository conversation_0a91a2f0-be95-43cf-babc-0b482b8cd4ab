<html xmlns="http://www.w3.org/1999/xhtml" lang="en"
	  xmlns:f="http://typo3.org/ns/TYPO3/Fluid/ViewHelpers"
	  xmlns:s="http://typo3.org/ns/ApacheSolrForTypo3/Solr/ViewHelpers/">

<div class="tx-solr">
	<div class="tx-solr-search">
		<div class="row searchinfo">
			<div class="col-md-12">
				<f:if condition="{resultSet.usedQuery.query}">
					<h2>
						{resultSet.allResultCount} <f:translate key="LLL:EXT:talisman_template/Resources/Private/Language/Frontend/Solr.xlf:searchResults" /> <em>"{resultSet.usedQuery.query}"</em>
					</h2>
				</f:if>
			</div>
		</div>

		<div class="row">
			<div class="tx-solr-search-form col-lg-8">
				<f:render partial="Search/Form" section="Form" arguments="{search:search, additionalFilters:additionalFilters, pluginNamespace: pluginNamespace, resultSet: resultSet}" />
			</div>
		</div>

		<f:if condition="{resultSet.hasSearched}">
			<f:if condition="{resultSet.usedSearchRequest.contextTypoScriptConfiguration.searchFaceting}">
				<f:render partial="Result/Facets" section="Facets" arguments="{resultSet:resultSet}" />
			</f:if>
		</f:if>

		<div class="row searchinfo">
			<div class="col-md-12">
				<f:if condition="{resultSet.hasSpellCheckingSuggestions}">
					<f:then>
						<s:translate key="didYouMean">Did you mean</s:translate>
						<f:for each="{resultSet.spellCheckingSuggestions}" as="suggestion">
							<f:link.page additionalParams="{q:suggestion.suggestion}" noCacheHash="1">{suggestion.suggestion}</f:link.page>
						</f:for> ?
					</f:then>
				</f:if>
			</div>
		</div>

		<f:if condition="{resultSet.hasSearched}">
			<hr>
			<s:widget.resultPaginate resultSet="{resultSet}" configuration="{insertAbove: false, maximumNumberOfLinks: 4}">
				<ol class="results-list">
					<f:for each="{documents}" as="document">
						<f:render partial="Result/Document" section="Document" arguments="{resultSet:resultSet, document:document}" />
					</f:for>
				</ol>
			</s:widget.resultPaginate>
		</f:if>
	</div>
</div>