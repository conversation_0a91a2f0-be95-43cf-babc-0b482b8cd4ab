@mixin image-hover-zoom-effect {
    opacity: 1;
    background: $black;

    img {
        opacity: 0.7;
        transition: all 0.4s ease 0s;
    }

    @media (min-width: $screen-lg-min) {

        &:hover {

            img {
                opacity: 1;
                transform: scale(1.05);
            }
        }
    }
}

@mixin responsive-font-property($baseSize, $property: 'font-size') {
    @media (min-width: $screen-lg-min) {
        #{$property}: $baseSize + px;
    }

    @media (min-width: $screen-md-min) and (max-width: $screen-md-max) {
        #{$property}: round($baseSize * 0.826666666) + px;
    }

    @media (min-width: $screen-sm-min) and (max-width: $screen-sm-max) {
        #{$property}: round($baseSize * 0.64) + px;
    }

    @media (max-width: $screen-xs-max) {
        #{$property}: round($baseSize * 0.64) + px;
    }
}

// @see http://stackoverflow.com/questions/20772451/fixed-position-delayed-on-ios
@mixin disable-ios-flickering() {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
}

@mixin custom-font-family($font, $weight: 400, $fallback: $font-family-sans-serif) {
    font-family: $font, $fallback;
    font-weight: $weight;
}

@keyframes heartbeat {
    0%  {
        transform: scale(.75);
    }
    20% {
        transform: scale(1);
    }
    40% {
        transform: scale(.75);
    }
    60% {
        transform: scale(1);
    }
    80% {
        transform: scale(.75);
    }
    100% {
        transform: scale(1);
    }
}
