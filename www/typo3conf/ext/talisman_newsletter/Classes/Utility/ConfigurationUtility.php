<?php

namespace Red<PERSON>wi\TalismanNewsletter\Utility;

use TYPO3\CMS\Core\Configuration\ExtensionConfiguration;
use TYPO3\CMS\Core\Utility\GeneralUtility;

/**
 * Utility: Talisman newsletter configuration
 *
 * @package Redkiwi\TalismanNewsletter\Utility
 */
class ConfigurationUtility
{
    /**
     * @return boolean
     */
    public static function isValid()
    {
        $settings = static::getApiConfiguration();
        return (is_array($settings)
            && !empty($settings['key'])
            && !empty($settings['url'])
        );
    }
    /**
     * @return array
     */
    public static function getApiConfiguration()
    {
        $configuration = static::getConfiguration();
        return (isset($configuration['api']) ? $configuration['api'] : $configuration['api.']);
    }

    /**
     * @return array
     */
    public static function getConfiguration()
    {
        static $configuration;
        if ($configuration === null) {
            $data = GeneralUtility::makeInstance(ExtensionConfiguration::class)->get('talisman_newsletter');

            if (!is_array($data)) {
                $configuration = (array) unserialize($data);
            } else {
                $configuration = $data;
            }
        }
        return $configuration;
    }
}
