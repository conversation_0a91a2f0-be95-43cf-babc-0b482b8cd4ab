<?php
namespace Redkiwi\TalismanEsbConnector\Command;

use Redkiwi\TalismanEsbConnector\Utility\ConfigurationUtility;

/**
 * Command: Geo tree
 *
 * @package Redkiwi\TalismanEsbConnector\Command
 */
class GeoTreeCommandController extends \TYPO3\CMS\Extbase\Mvc\Controller\CommandController
{
    /**
     * @var \Redkiwi\TalismanEsbConnector\Service\Esb\GeoTreeService
     * @inject
     */
    protected $esbGeoTreeService;

    /**
     * @var \Redkiwi\TalismanEsbConnector\Service\TYPO3\GeoTreeService
     * @inject
     */
    protected $typo3GeoTreeService;

    /**
     * Synchronises the remote geo tree with the local page tree
     *
     * @return bool
     * @throws \Exception
     */
    public function syncCommand()
    {
        if (ConfigurationUtility::isValid()) {
            $configuration = ConfigurationUtility::getEsbSettings();
            $geoTree = $this->esbGeoTreeService->makeRequest([]);
            $this->typo3GeoTreeService->sync($geoTree, $configuration['destinationRootPageUid']);
            return true;
        } else {
            throw new \Exception('Configuration invalid. Check Extension Manager');
        }
    }
}
