<?php
namespace Redkiwi\TalismanNewsletter\Domain\Session;

/**
 * Session: Subscriber
 *
 * @package Redkiwi\TalismanNewsletter\Domain\Session
 */
class Subscriber implements \Keizer\KoningLibrary\Domain\Session\SessionInterface
{
    /** @var \Redkiwi\TalismanNewsletter\Domain\Model\Subscriber */
    protected $subscriber;

    /**
     * @return \Redkiwi\TalismanNewsletter\Domain\Model\Subscriber
     */
    public function getSubscriber()
    {
        return $this->subscriber;
    }

    /**
     * @param \Redkiwi\TalismanNewsletter\Domain\Model\Subscriber $subscriber
     * @return void
     */
    public function setSubscriber($subscriber)
    {
        $this->subscriber = $subscriber;
    }
}
