#
# Table structure for table 'tt_content'
#
CREATE TABLE tt_content (
    talismanexampletrip_slides int(11) DEFAULT '0' NOT NULL,
);

#
# Table structure for table 'tx_talismanexampletrip_domain_model_slide'
#
CREATE TABLE tx_talismanexampletrip_domain_model_slide (
    uid int(11) NOT NULL auto_increment,
    pid int(11) DEFAULT '0' NOT NULL,
    tstamp int(11) DEFAULT '0' NOT NULL,
    crdate int(11) DEFAULT '0' NOT NULL,
    cruser_id int(11) DEFAULT '0' NOT NULL,
    sorting int(10) DEFAULT '0' NOT NULL,
    editlock tinyint(4) DEFAULT '0' NOT NULL,
    deleted tinyint(4) DEFAULT '0' NOT NULL,
    hidden tinyint(4) DEFAULT '0' NOT NULL,
    starttime int(11) DEFAULT '0' NOT NULL,
    endtime int(11) DEFAULT '0' NOT NULL,
    content_id int(11) DEFAULT '0' NOT NULL,
    title_backend tinytext,
    title tinytext,
    description text,
    extra_description text,
    subtitle text,
    day_text varchar(255) DEFAULT '' NOT NULL,
    type int(11) DEFAULT '0' NOT NULL,
    visual int(11) DEFAULT '0' NOT NULL,
    visual_position varchar(255) DEFAULT '' NOT NULL,
    timeline int(11) DEFAULT '0' NOT NULL,
    price int(11) DEFAULT '0' NOT NULL,
    page_content int(11) DEFAULT '0' NOT NULL,

    PRIMARY KEY (uid),
    KEY parent (pid),
    KEY content_id (content_id)
);

#
# Table structure for table 'tx_talismanexampletrip_domain_model_timeline'
#
CREATE TABLE tx_talismanexampletrip_domain_model_timeline (
    uid int(11) NOT NULL auto_increment,
    pid int(11) DEFAULT '0' NOT NULL,
    tstamp int(11) DEFAULT '0' NOT NULL,
    crdate int(11) DEFAULT '0' NOT NULL,
    cruser_id int(11) DEFAULT '0' NOT NULL,
    sorting int(10) DEFAULT '0' NOT NULL,
    editlock tinyint(4) DEFAULT '0' NOT NULL,
    deleted tinyint(4) DEFAULT '0' NOT NULL,
    hidden tinyint(4) DEFAULT '0' NOT NULL,
    starttime int(11) DEFAULT '0' NOT NULL,
    endtime int(11) DEFAULT '0' NOT NULL,
    title tinytext,
    slide_id int(11) DEFAULT '0' NOT NULL,
    enable_popup tinyint(4) DEFAULT '0' NOT NULL,
    description text,
    icon varchar(255) DEFAULT '' NOT NULL,
    style varchar(255) DEFAULT '' NOT NULL,

    PRIMARY KEY (uid),
    KEY parent (pid),
    KEY slide_id (slide_id)
);

#
# Table structure for table 'tx_talismanexampletrip_domain_model_price'
#
CREATE TABLE tx_talismanexampletrip_domain_model_price (
    uid int(11) NOT NULL auto_increment,
    pid int(11) DEFAULT '0' NOT NULL,
    tstamp int(11) DEFAULT '0' NOT NULL,
    crdate int(11) DEFAULT '0' NOT NULL,
    cruser_id int(11) DEFAULT '0' NOT NULL,
    sorting int(10) DEFAULT '0' NOT NULL,
    editlock tinyint(4) DEFAULT '0' NOT NULL,
    deleted tinyint(4) DEFAULT '0' NOT NULL,
    hidden tinyint(4) DEFAULT '0' NOT NULL,
    starttime int(11) DEFAULT '0' NOT NULL,
    endtime int(11) DEFAULT '0' NOT NULL,
    title tinytext,
    slide_id int(11) DEFAULT '0' NOT NULL,
    promotional_package int(11) DEFAULT '0' NOT NULL,
    description text,

    PRIMARY KEY (uid),
    KEY parent (pid),
    KEY slide_id (slide_id)
);
