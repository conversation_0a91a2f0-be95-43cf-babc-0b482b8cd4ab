<ul class="list-tree list-tree-root">
    <li class="list-tree-control-open">
        <span class="list-tree-group">
            <f:link.action action="show" arguments="{rootFolder: 'root'}" class="list-tree-control list-tree-control-open">
                <i class="fa"></i>
            </f:link.action>
        </span>
        <span class="list-tree-icon dragIcon">
            <span class="t3js-icon icon icon-size-small icon-state-default icon-apps-filetree-mount">
                <span class="icon-markup">
                    <img width="16" height="16" src="/typo3/sysext/core/Resources/Public/Icons/T3Icons/apps/apps-filetree-mount.svg">
                </span>
            </span>
        </span>
        <span class="list-tree-title">
            <f:translate id="show.elvis_dam" arguments="{0: elvisDamBaseUrl}" />
        </span>
        <f:render partial="Folders" arguments="{folders: session.folders.root, rootFolder: session.rootFolder, rteId: rteId}" />
    </li>
</ul>
