import {toggleAriaExpanded} from '../_utilities/aria';

const toggles = document.querySelectorAll('[aria-controls][aria-expanded]');
toggles.forEach((toggle) => {
    toggle.addEventListener('click', () => {
        toggleAriaExpanded(toggle as HTMLElement);
    });
    
    if (toggle.id.includes('accordion-trigger-')) {
        toggle.parentNode?.addEventListener('keydown', (event) => {
            if (event.code === 'Space') {
                toggleAriaExpanded(toggle as HTMLElement);
            }
        });
    }

    if (toggle.id.includes('has-submenu-')) {
        toggle.parentNode?.addEventListener('mouseenter', () => {
            toggle.setAttribute('aria-expanded', 'true');
        });
        toggle.parentNode?.addEventListener('mouseleave', () => {
            toggle.setAttribute('aria-expanded', 'false');
        });
    }
});

document.addEventListener('keydown', (event) => {
    if (event.code === 'Escape') {
        toggles.forEach((toggle) => {
            if (toggle.id.includes('has-submenu-')) {
                toggle.setAttribute('aria-expanded', 'false');
            }
        });
    }
});
