<?php
if (!defined('TYPO3_MODE')) {
    die('Access denied.');
}

\TYPO3\CMS\Extbase\Utility\ExtensionUtility::configurePlugin(
    'Redkiwi.' . $_EXTKEY,
    'Widget',
    ['Widget' => 'show, handle'],
    ['Widget' => 'show, handle'],
    \TYPO3\CMS\Extbase\Utility\ExtensionUtility::PLUGIN_TYPE_CONTENT_ELEMENT
);

\TYPO3\CMS\Extbase\Utility\ExtensionUtility::configurePlugin(
    'Redkiwi.' . $_EXTKEY,
    'Form',
    ['Form' => 'show, create, finished'],
    ['Form' => 'show, create, finished'],
    \TYPO3\CMS\Extbase\Utility\ExtensionUtility::PLUGIN_TYPE_CONTENT_ELEMENT
);

$GLOBALS['TYPO3_CONF_VARS']['SC_OPTIONS']['extbase']['commandControllers'][] = \Redkiwi\TalismanNewsletter\Command\NewsletterCommandController::class;
