<div class="talisman-inspiration widget">
    <h2>
        <f:translate id="widget.show.header"/>
    </h2>
    <f:form action="setFilters" objectName="filter" method="POST">
        <div class="row">
            <div class="col-sm-12 col-md-10">
                <div class="row">
                    <f:for each="{filters}" as="category">
                        <div class="col-sm-3">
                            <f:form.select
                                class="select-picker"
                                property="{category.uid}"
                                options="{category.children}"
                                prependOptionLabel="{category.title}"
                                optionLabelField="title"/>
                        </div>
                    </f:for>
                </div>
            </div>
            <div class="col-sm-12 col-md-2 submit-container">
                <button type="submit" class="btn btn-default btn-rubine">
                    <f:translate id="widget.show.submit"/>
                </button>
            </div>
        </div>
    </f:form>
    <h3>
        <f:translate id="widget.show.footer"/>
    </h3>
</div>
