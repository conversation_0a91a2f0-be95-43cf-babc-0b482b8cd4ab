<?php
namespace <PERSON><PERSON>wi\TalismanInspiration\Install\Updates;

use Red<PERSON>wi\TalismanInspiration\Seeder\CategorySeeder;
use Redkiwi\TalismanTemplate\Domain\Model\Category;
use TYPO3\CMS\Core\Database\ConnectionPool;
use TYPO3\CMS\Core\Database\Query\QueryBuilder;
use TYPO3\CMS\Core\Utility\GeneralUtility;
use TYPO3\CMS\Install\Updates\UpgradeWizardInterface;

/**
 * Install Tool Migration: Category Seeder
 *
 * @package Redkiwi\TalismanInspiration\Install\Updates
 */
class CategorySeederMigration implements UpgradeWizardInterface
{
    /**
     * Return the identifier for this wizard
     * This should be the same string as used in the ext_localconf class registration
     *
     * @return string
     */
    public function getIdentifier(): string {
        return 'InspirationCategorySeederMigration';
    }

    /**
     * Return the speaking name of this wizard
     *
     * @return string
     */
    public function getTitle(): string {
        return 'Import inspiration initial data';
    }

    /**
     * Return the description for this wizard
     *
     * @return string
     */
    public function getDescription(): string {
        return 'Import inspiration initial data';
    }

    /**
     * Execute the update
     *
     * Called when a wizard reports that an update is necessary
     *
     * @return bool
     */
    public function executeUpdate(): bool {
        $loop = 0;
        foreach (CategorySeeder::getCategories() as $storage => $category) {
            foreach ($category as $uid => $data) {
                $this->insertOrUpdateCategory($uid, $storage, 0, $data['title'], $loop);
                $loop++;
                foreach ($data['options'] as $optionId => $optionTitle) {
                    $loop++;
                    $this->insertOrUpdateCategory($optionId, $storage, $uid, $optionTitle, $loop);
                }
            }
        }
        return true;
    }

    /**
     * Is an update necessary?
     *
     * Is used to determine whether a wizard needs to be run.
     * Check if data for migration exists.
     *
     * @return bool
     */
    public function updateNecessary(): bool {
        // @todo Figure out why this always returns true (before Upgrade to TYPO3 v9, this used to be checkForUpdate() which also returned true)
        return true;
    }

    /**
     * Returns an array of class names of Prerequisite classes
     *
     * This way a wizard can define dependencies like "database up-to-date" or
     * "reference index updated"
     *
     * @return string[]
     */
    public function getPrerequisites(): array {
        return [];
    }

    /**
     * Update or insert category
     *
     * @param integer $uid
     * @param integer $storage
     * @param integer $parent
     * @param string $title
     * @param integer $sorting
     */
    protected function insertOrUpdateCategory($uid, $storage, $parent, $title, $sorting = 0)
    {
        /** @var QueryBuilder $selectQueryBuilder */
        $selectQueryBuilder = GeneralUtility::makeInstance(ConnectionPool::class)->getQueryBuilderForTable(Category::TABLE);
        $row = $selectQueryBuilder->select('*')
            ->from(Category::TABLE)
            ->where(
                $selectQueryBuilder->expr()->eq('uid', $queryBuilder->createNamedParameter($uid, \PDO::PARAM_INT))
            )->execute()
            ->fetch();

        if($row && ($row['title'] !== $title || (int)$row['pid'] !== $storage || (int)$row['parent'] !== $parent)) {

            /** @var QueryBuilder $updateQueryBuilder */
            $updateQueryBuilder = GeneralUtility::makeInstance(ConnectionPool::class)->getQueryBuilderForTable(Category::TABLE);
            $updateQueryBuilder
                ->update(Category::TABLE)
                ->where($updateQueryBuilder->expr()->eq('uid', $queryBuilder->createNamedParameter($uid, \PDO::PARAM_INT)))
                ->set('tstamp', time())
                ->set('pid', $storage)
                ->set('parent', $parent)
                ->set('title', trim($title))
                ->set('sorting', $sorting)
                ->execute();

        } elseif(!$row) {

            GeneralUtility::makeInstance(ConnectionPool::class)->getQueryBuilderForTable(Category::TABLE)
                ->insert(Category::TABLE)
                ->values(
                    [
                        'uid' => $uid,
                        'pid' => $storage,
                        'crdate' => time(),
                        'tstamp' => time(),
                        'parent' => $parent,
                        'title' => $title,
                        'sorting' => $sorting,
                    ]
                )
                ->execute();

        }
    }
}
