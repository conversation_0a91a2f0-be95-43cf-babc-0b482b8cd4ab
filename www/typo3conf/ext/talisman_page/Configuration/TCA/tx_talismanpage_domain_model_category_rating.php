<?php

use Redkiwi\TalismanTemplate\Domain\Model\Category;
use Redkiwi\TalismanPage\Domain\Model\CategoryRating;
use TYPO3\CMS\Core\Category\CategoryRegistry;

if (!defined('TYPO3_MODE')) {
    die ('Access denied.');
}

$languageFile = 'LLL:EXT:talisman_page/Resources/Private/Language/locallang_be.xlf';

return [
    'ctrl' => [
        'title' => $languageFile . ':' . CategoryRating::TABLE,
        'label' => 'category',
        'label_userFunc' => 'Redkiwi\\TalismanPage\\Utility\\TableConfigurationUtility->categoryRatingTitle',
        'hideAtCopy' => true,
        'tstamp' => 'tstamp',
        'crdate' => 'crdate',
        'cruser_id' => 'cruser_id',
        'editlock' => 'editlock',
        'dividers2tabs' => true,
        'hideTable' => true,
        'sortby' => 'sorting',
        'delete' => 'deleted',
        'iconfile' => 'EXT:talisman_page/Resources/Public/Icons/tx_talismanpage_domain_model_category_rating.png',
    ],
    'interface' => [
        'showRecordFieldList' => 'category, rating'
    ],
    'types' => [
        0 => [
            'showitem' => 'title;;access, category, rating'
        ]
    ],
    'columns' => [
        'category' => [
            'exclude' => false,
            'label' => $languageFile . ':tx_talismanpage_domain_model_category_rating.category',
            'config' => CategoryRegistry::getTcaFieldConfiguration(
                CategoryRating::TABLE,
                'category',
                [
                    'foreign_table_where' => ' AND sys_category.pid = ' . Category::FOLDER_RATEABLE
                        . ' AND sys_category.sys_language_uid IN (-1, 0) ORDER BY sys_category.sorting ASC',
                    'treeConfig' => [
                        'appearance' => [
                            'nonSelectableLevels' => '0,1'
                        ]
                    ],
                ]
            ),
        ],
        'rating' => [
            'exclude' => false,
            'label' => $languageFile . ':tx_talismanpage_domain_model_category_rating.rating',
            'config' => [
                'type' => 'input',
                'size' => 3,
                'eval' => 'trim, int',
                'default' => 3,
                'range' => [
                    'lower' => CategoryRating::RATING_MINIMUM,
                    'upper' => CategoryRating::RATING_MAXIMUM,
                ],

                'wizards' => [
                    'rating' => [
                        'type' => 'slider',
                        'step' => 1,
                        'width' => 200
                    ]
                ]
            ],
        ],
    ],
];
