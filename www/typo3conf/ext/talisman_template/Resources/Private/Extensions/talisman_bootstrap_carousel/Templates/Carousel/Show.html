<f:layout name="Default" />

<f:section name="Content">
    <div
        id="{identifier}"
        class="carousel slide"
        data-ride="carousel"
        data-interval="{settings.data.interval}"
        data-wrap="{f:if(condition: '{settings.data.wrap}', then: 'true', else: 'false')}"
        data-keyboard="{f:if(condition: '{settings.data.keyboard}', then: 'true', else: 'false')}">

        <f:if condition="{settings.data.indicators}">
            <f:if condition="{f:count(subject: '{slides}')} > 1">
                <ol class="carousel-indicators">
                    <f:for each="{slides}" as="slide" iteration="i">
                        <li data-target="#{identifier}" data-slide-to="{i.index}" class="{f:if(condition: '{i.isFirst}', then: 'active')}"></li>
                    </f:for>
                </ol>
            </f:if>
        </f:if>

        <div class="carousel-inner header-image" role="listbox">
            <f:for each="{slides}" as="slide" iteration="i">
                <f:if condition="{slide.visual.uid}">
                    <div class="item {f:if(condition: '{i.isFirst}', then: 'active')} {slide.visualPosition} {slide.visualSize}" style="background-image: url({f:uri.image(image: slide.visual)});">
                        <f:if condition="{slide.linkText}">
                            <f:then>
                                <f:if condition="{slide.link}">
                                    <f:then>
                                        <div class="container">
                                            <div class="carousel-caption">
                                                <h2>
                                                    <f:format.nl2br>{slide.title}</f:format.nl2br>
                                                </h2>
                                                <f:link.typolink parameter="{slide.link}" class="btn btn-default btn-gold">
                                                    {slide.linkText}
                                                </f:link.typolink>
                                            </div>
                                        </div>
                                        <div class="gradient"></div>
                                    </f:then>
                                    <f:else>
                                        <div class="container">
                                            <h2>
                                                <f:format.nl2br>{slide.title}</f:format.nl2br>
                                            </h2>
                                            <h3>
                                                <f:format.nl2br>{slide.linkText}</f:format.nl2br>
                                            </h3>
                                        </div>
                                        <div class="gradient"></div>
                                    </f:else>
                                </f:if>
                            </f:then>
                            <f:else>
                                <div class="carousel-caption">
                                    <h3>
                                        <f:format.nl2br>{slide.title}</f:format.nl2br>
                                    </h3>
                                </div>
                            </f:else>
                        </f:if>
                    </div>
                </f:if>
            </f:for>
        </div>

        <f:if condition="{settings.data.controls}">
            <f:if condition="{f:count(subject: '{slides}')} > 1">
                <a class="left carousel-control" href="#{identifier}" role="button" data-slide="prev">
                    <span class="sr-only"><f:translate id="previous" extensionName="TalismanBootstrapCarousel" /></span>
                </a>
                <a class="right carousel-control" href="#{identifier}" role="button" data-slide="next">
                    <span class="sr-only"><f:translate id="next" extensionName="TalismanBootstrapCarousel" /></span>
                </a>
            </f:if>
        </f:if>
    </div>
</f:section>
