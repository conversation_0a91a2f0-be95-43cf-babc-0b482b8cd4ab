<?php
namespace Red<PERSON>wi\TalismanQuoteForm\Controller;

use Keizer\KoningLibrary\Service\MailService;
use Red<PERSON>wi\TalismanQuoteForm\Domain\Model\DataTransfer\Form\Brochure;
use TYPO3\CMS\Core\Utility\GeneralUtility;

/**
 * Controller: Brochure form
 *
 * @package Redkiwi\TalismanQuoteForm\Controller
 */
class BrochureFormController extends AbstractFormController
{
    static protected $sessionKey = 'TALISMAN_BROCHURE_FORM';

    /**
     * @param Brochure|null $brochure [optional]
     */
    public function showAction(Brochure $brochure = null)
    {
        if ($brochure === null) {
            $brochure = new Brochure();
        }

        $this->view->assignMultiple([
            'destinations' => $this->destinationService->getDestinations($this->settings['destinationRoot']),
            'heardOfTalismanAnswers' => $this->getHeardOfTalismanAnswers(),
            'brochure' => $brochure,
        ]);
    }

    /**
     * @param Brochure $brochure
     * @throws \TYPO3\CMS\Extbase\Mvc\Exception\StopActionException
     * @throws \TYPO3\CMS\Extbase\Mvc\Exception\UnsupportedRequestTypeException
     * @throws \TYPO3\CMS\Extbase\Persistence\Exception\IllegalObjectTypeException
     */
    public function processAction(Brochure $brochure)
    {
        $brochure->loadTrackingValues($_COOKIE);

        $this->formService->saveBrochure($brochure);
        $this->sessionService->saveSession(self::$sessionKey, $brochure);

        $this->sendBrochureNotificationMail($brochure);
        if ($brochure->getNewsletter() === 0) {
            $this->addToSubscription($brochure);
        }

        $this->redirect('finished');
    }

    /**
     * @param Brochure $brochure
     */
    private function sendBrochureNotificationMail(Brochure $brochure)
    {
        $recipients = GeneralUtility::trimExplode(',', $this->settings['data']['recipientEmail']);
        $recipients = array_fill_keys($recipients, $this->settings['data']['recipientName']);

        /** @var MailService $mailerService */
        $mailerService = GeneralUtility::makeInstance(MailService::class);
        $mailerService
            ->setTemplateRootPaths([
                'EXT:talisman_quote_form/Resources/Private/Templates/Email'
            ])
            ->setLayoutRootPaths([
                'EXT:talisman_template/Resources/Private/Layouts'
            ])
            ->sendMail(
                $recipients,
                [$this->settings['data']['senderEmail'] => $this->settings['data']['senderName']],
                $this->settings['data']['recipientSubject'],
                'BrochureNotification',
                [$this->settings['data']['bccEmail'] => $this->settings['data']['bccName']],
                [
                    'fields' => $brochure,
                    'subject' => $this->settings['data']['recipientSubject']
                ]
            );

        $mailerService->sendMail(
            [$brochure->getEmail() => $brochure->getName()],
            [$this->settings['data']['senderEmail'] => $this->settings['data']['senderName']],
            $this->settings['data']['senderSubject'],
            'BrochureConfirmation',
            [$this->settings['data']['bccEmail'] => $this->settings['data']['bccName']],
            [
                'fields' => $brochure,
                'subject' => $this->settings['data']['senderSubject']
            ]
        );
    }

    /**
     * @return void
     */
    public function finishedAction()
    {
        $brochure = $this->sessionService->getSession(self::$sessionKey, Brochure::class);
        $this->view->assign('fields', $brochure);
    }
}
