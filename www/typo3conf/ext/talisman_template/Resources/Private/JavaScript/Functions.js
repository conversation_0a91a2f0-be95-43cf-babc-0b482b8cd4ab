Talisman.Functions = {

    /**
     * Formats string/integer as currency for website
     *
     * @param amount Example: 10000
     * @param decimal boolean
     * @returns string 10.000,00 || € 10.000
     */
    formatCurrency: function (amount, decimal) {
        // Return empty when not given correctly
        if (amount.length === 0) {
            return '€ 0,00';
        }

        // Set default values
        if (decimal === null) {
            decimal = false;
        }

        var output = amount;
        // Parse ,00 when configured
        if (decimal === true) {
            output = output.toFixed(2).replace('.', ',');
        } else {
            // Force string cast
            output = "" + output + "";
        }

        // Parse currency regex
        return '€ ' + output.replace(/(\d)(?=(\d\d\d)+(?!\d))/g, '$1.');
    }
};
