plugin.tx_talismanaccommodations {
    features {
        requireCHashArgumentForActionArguments = 0
    }
    view {
        templateRootPaths {
            10 = EXT:talisman_accommodations/Resources/Private/Templates/
        }
        partialRootPaths {
            10 = EXT:talisman_accommodations/Resources/Private/Partials/
            20 = EXT:talisman_content/Resources/Private/Partials/
        }
        layoutRootPaths {
            10 = EXT:talisman_accommodations/Resources/Private/Layouts/
        }
    }
    settings {
        parentCategory = {$categories.interests}
    }
}

AccommodationAjaxResults = PAGE
AccommodationAjaxResults {
    typeNum = 164
    10 = USER_INT
    10 {
        userFunc = TYPO3\CMS\Extbase\Core\Bootstrap->run
        pluginName = List
        vendorName = Redkiwi
        extensionName = TalismanAccommodations
        controller = Accommodation
        switchableControllerActions {
            Accommodation {
                1 = results
            }
        }
    }
    config {
        disableAllHeaderCode = 1
        xhtml_cleaning = 0
        no_cache = 1
        debug = 0
    }
}
