Talisman.Extensions.KoningPostcode = {

    /** @var string */
    url: '/?eID=koning_postcode&postcode={postCode}&houseNumber={houseNumber}&houseNumberAddendum={houseNumberAddendum}',

    /**
     * @var object
     */
    labels: {
        streetNotFound: 'Straat niet gevonden',
        cityNotFound: 'Plaats niet gevonden'
    },

    /**
     * Initialises functionality
     *
     * @return void
     */
    init: function() {
        $('input[data-postcode-nl-check="houseNumber"]').blur(function() {
            Talisman.Extensions.KoningPostcode.fetchAddress();
            return false;
        });

        if ($('input[data-postcode-nl-check="street"]').val() !== '' && $('input[data-postcode-nl-check="city"]').val() !== '') {
            $('input[data-postcode-nl-check="postcode"]').attr('data-previousvalue', $('input[data-postcode-nl-check="postcode"]').val());
            $('input[data-postcode-nl-check="houseNumber"]').attr('data-previousvalue', $('input[data-postcode-nl-check="houseNumber"]').val());
            $('input[data-postcode-nl-check="houseNumberAddendum"]').attr('data-previousvalue', $('input[data-postcode-nl-check="houseNumberAddendum"]').val());
        }
    },

    /**
     * Fetches the address based on the provided postcode and house number
     *
     * @return void
     */
    fetchAddress: function() {
        var postCodeField = $('input[data-postcode-nl-check="postcode"]');
        var houseNumberField = $('input[data-postcode-nl-check="houseNumber"]');
        var houseNumberAddendumField = $('input[data-postcode-nl-check="houseNumberAddendum"]');

        if (
            (postCodeField.val() !== '' && houseNumberField.val() !== '')
            &&
            (
                postCodeField.val() !== postCodeField.attr('data-previousvalue')
                ||
                houseNumberField.val() !== houseNumberField.attr('data-previousvalue')
                ||
                houseNumberAddendumField.val() !== houseNumberAddendumField.attr('data-previousvalue')
            )
        ) {
            var url = Talisman.Extensions.KoningPostcode.url;
            url = url
                .replace('{postCode}', postCodeField.val())
                .replace('{houseNumber}', houseNumberField.val())
                .replace('{houseNumberAddendum}', houseNumberAddendumField.val());

            $.ajax({
                dataType: 'json',
                url: url,
                beforeSend: function() {
                    $('input[data-postcode-nl-check="city"]').parent().find('div.alert').remove();
                    $('input[data-postcode-nl-check="street"]').parent().find('div.alert').remove();
                    $('body').fadeTo('fast', 0.33);
                },
                success: function(response) {
                    if (response.result !== false) {
                        $('input[data-postcode-nl-check="city"]').val(response.result.city);
                        $('input[data-postcode-nl-check="street"]').val(response.result.street);
                    } else {
                        $('input[data-postcode-nl-check="city"]').after('<div class="alert alert-danger"><i class="fa fa-exclamation-triangle"></i> ' + Talisman.Extensions.KoningPostcode.labels.cityNotFound + '</div>');
                        $('input[data-postcode-nl-check="street"]').after('<div class="alert alert-danger"><i class="fa fa-exclamation-triangle"></i> ' + Talisman.Extensions.KoningPostcode.labels.streetNotFound + '</div>');
                    }
                    $('input[data-postcode-nl-check="street"]').parent().removeClass('disabled');
                    $('input[data-postcode-nl-check="city"]').parent().removeClass('disabled');
                },
                complete: function() {
                    $('body').fadeTo('fast', 1);
                    postCodeField.attr('data-previousvalue', postCodeField.val());
                    houseNumberField.attr('data-previousvalue', houseNumberField.val());
                    houseNumberAddendumField.attr('data-previousvalue', houseNumberAddendumField.val());
                }
            })
        }
    }
};

$(document).ready(function() {
    Talisman.Extensions.KoningPostcode.init();
});
