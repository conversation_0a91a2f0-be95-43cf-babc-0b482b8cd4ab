{namespace t=Red<PERSON>wi\TalismanContent\ViewHelpers}
<f:for each="{gallery.rows}" as="row">
    <f:for each="{row.columns}" as="column">
        <div class="embedded-caption">
            <f:if condition="{column.media.link}">
                <f:then>
                    <f:link.typolink parameter="{column.media.link}">
                        <f:render section="Media" arguments="{_all}" />
                    </f:link.typolink>
                </f:then>
                <f:else>
                    <f:render section="Media" arguments="{_all}" />
                </f:else>
            </f:if>
        </div>
    </f:for>
</f:for>

<f:section name="Media">
    <figure>
        <f:media
            file="{column.media}"
            alt="{column.media.alternative}"
            title="{column.media.title}"
            class="img-responsive" />
        <figcaption>
            <p>{column.media.description -> f:format.nl2br()}</p>
        </figcaption>
    </figure>
</f:section>
