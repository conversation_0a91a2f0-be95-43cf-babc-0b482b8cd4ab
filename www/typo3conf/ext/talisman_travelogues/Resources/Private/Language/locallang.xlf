<?xml version="1.0" encoding="UTF-8"?>
<xliff version="1.0">
    <file source-language="en" datatype="plaintext" original="messages" date="2012-10-17T19:30:32Z" product-name="talisman_travelogues">
        <header/>
        <body>
            <trans-unit id="list.author">
                <source>By: %s</source>
            </trans-unit>
            <trans-unit id="list.read_more">
                <source>Read more</source>
            </trans-unit>
            <trans-unit id="list.date">
                <source>Posted</source>
            </trans-unit>

            <trans-unit id="detail.author">
                <source>By: %s</source>
            </trans-unit>

            <trans-unit id="form.step_one.intro">
                <source>Upload your travelogue here.</source>
            </trans-unit>
            <trans-unit id="form.step_one.header">
                <source>How does it work</source>
            </trans-unit>
            <trans-unit id="form.step_one.select">
                <source>Upload a file or enter the text manually.</source>
            </trans-unit>
            <trans-unit id="form.step_one.select.manually">
                <source>Enter manually</source>
            </trans-unit>
            <trans-unit id="form.step_one.select.document">
                <source>Upload document</source>
            </trans-unit>

            <trans-unit id="form.nav.details">
                <source>Details</source>
            </trans-unit>
            <trans-unit id="form.nav.travelogue">
                <source>Travelogue</source>
            </trans-unit>

            <trans-unit id="form.step_two.intro">
                <source><![CDATA[<a href="%s">Login</a> to skip this step.]]></source>
            </trans-unit>
            <trans-unit id="form.step_two.name">
                <source>Your name</source>
            </trans-unit>
            <trans-unit id="form.step_two.email">
                <source>E-mail address</source>
            </trans-unit>
            <trans-unit id="form.step_two.disclaimer">
                <source>Talisman uses your personal data to check the authenticity..</source>
            </trans-unit>
            <trans-unit id="form.step_two.submit">
                <source>Submit</source>
            </trans-unit>
            <trans-unit id="form.step_two.mandatory">
                <source>mandatory</source>
            </trans-unit>
            <trans-unit id="form.step_three.destinations">
                <source>Your destination(s)</source>
            </trans-unit>
            <trans-unit id="form.step_three.title">
                <source>Title of your travelogue</source>
            </trans-unit>
            <trans-unit id="form.step_three.document">
                <source>Travelogue file</source>
            </trans-unit>
            <trans-unit id="form.step_three.agree.title">
                <source>Agree to publish online</source>
            </trans-unit>
            <trans-unit id="form.step_three.agree">
                <source><![CDATA[<strong>YES</strong>, I accept the <a href="%s">terms</a>]]></source>
            </trans-unit>
            <trans-unit id="form.step_three.submit">
                <source>Submit document</source>
            </trans-unit>
            <trans-unit id="form.step_three.upload_document">
                <source>Travelogue file</source>
            </trans-unit>
            <trans-unit id="form.step_three.upload_document.placeholder">
                <source>Choose your travelogue file</source>
            </trans-unit>
            <trans-unit id="form.step_three.upload_images">
                <source>Image(s)</source>
            </trans-unit>
            <trans-unit id="form.step_three.upload_images.placeholder">
                <source>Choose one or more images</source>
            </trans-unit>
            <trans-unit id="form.step_three.travelogue">
                <source>Your travelogue</source>
            </trans-unit>
            <trans-unit id="form.finished.header">
                <target>Add travelogue</target>
            </trans-unit>
            <trans-unit id="form.finished.text">
                <source>Thanks for adding your travelogue</source>
            </trans-unit>
        </body>
    </file>
</xliff>
