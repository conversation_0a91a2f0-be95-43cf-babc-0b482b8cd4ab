<div class="row">
    <div class="col-sm-12 col-md-3">
        <div class="title-container">
            <header>
                <f:link.page pageUid="{blogPost.uid}">
                    {blogPost.title}
                </f:link.page>
            </header>

            <f:if condition="{blogPost.images.0}">
                <div class="hidden-md hidden-lg">
                    <f:link.page pageUid="{blogPost.uid}">
                        <f:image image="{blogPost.images.0}" alt="{blogPost.title}" class="visual img-responsive" height="243c" width="628c" />
                    </f:link.page>
                </div>
            </f:if>

            <f:if condition="{blogPost.blogAuthor}">
                <span class="author">
                    <f:translate key="blogpost.by" />: {blogPost.blogAuthor.title}
                </span>
            </f:if>

            <div class="specs-container">
                <span class="post-specs">
                    <f:translate key="blogpost.posted_on" />: <time><f:format.date format="%e %B, %Y">{blogPost.date}</f:format.date></time><br />
                </span>
            </div>
        </div>
    </div>
    <div class="hidden-xs hidden-sm col-md-9">
        <f:if condition="{blogPost.images.0}">
            <f:link.page pageUid="{blogPost.uid}">
                <f:image image="{blogPost.images.0}" alt="{blogPost.title}" class="visual img-responsive" height="243c" width="628c" />
            </f:link.page>
        </f:if>
    </div>
</div>
