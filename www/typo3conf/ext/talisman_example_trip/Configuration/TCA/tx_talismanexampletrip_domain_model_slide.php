<?php
if (!defined('TYPO3_MODE')) {
    die ('Access denied.');
}

return [
    'ctrl' => [
        'title' => 'LLL:EXT:talisman_example_trip/Resources/Private/Language/locallang_be.xlf:tx_talismanexampletrip_domain_model_slide',
        'label' => 'title_backend',
        'hideAtCopy' => true,
        'tstamp' => 'tstamp',
        'crdate' => 'crdate',
        'cruser_id' => 'cruser_id',
        'editlock' => 'editlock',
        'dividers2tabs' => true,
        'hideTable' => true,
        'sortby' => 'sorting',
        'delete' => 'deleted',
        'searchFields' => 'uid, title',
        'enablecolumns' => [
            'disabled' => 'hidden',
            'starttime' => 'starttime',
            'endtime' => 'endtime',
        ],
        'iconfile' => 'EXT:talisman_example_trip/Resources/Public/Icons/tx_talismanexampletrip_domain_model_slide.png',
        'requestUpdate' => 'type',
    ],
    'interface' => [
        'showRecordFieldList' => 'hidden, title_backend, type, title, subtitle, description, extra_description, day_text, type, visual, visual_position, timeline, price, page_content'
    ],
    'types' => [
        0 => [
            'showitem' => 'title_backend;;access, type, title, subtitle, description, extra_description, day_text, visual, visual_position, timeline, price, page_content'
        ]
    ],
    'palettes' => [
        'access' => [
            'showitem' => 'hidden, starttime, endtime',
            'canNotCollapse' => false
        ]
    ],
    'columns' => [
        'hidden' => [
            'exclude' => true,
            'label' => 'LLL:EXT:core/Resources/Private/Language/locallang_general.xlf:LGL.hidden',
            'config' => [
                'type' => 'check',
            ]
        ],
        'starttime' => [
            'exclude' => true,
            'l10n_mode' => 'mergeIfNotBlank',
            'label' => 'LLL:EXT:core/Resources/Private/Language/locallang_general.xlf:LGL.starttime',
            'config' => [
                'type' => 'input',
                'size' => '10',
                'max' => '20',
                'eval' => 'datetime',
            ]
        ],
        'endtime' => [
            'exclude' => true,
            'l10n_mode' => 'mergeIfNotBlank',
            'label' => 'LLL:EXT:core/Resources/Private/Language/locallang_general.xlf:LGL.endtime',
            'config' => [
                'type' => 'input',
                'size' => '8',
                'max' => '20',
                'eval' => 'datetime',
            ],
        ],
        'title_backend' => [
            'exclude' => false,
            'l10n_mode' => 'mergeIfNotBlank',
            'label' => 'LLL:EXT:talisman_example_trip/Resources/Private/Language/locallang_be.xlf:tx_talismanexampletrip_domain_model_slide.title_backend',
            'config' => [
                'type' => 'input',
                'eval' => 'trim, required',
                'size' => 30,
            ]
        ],
        'title' => [
            'exclude' => false,
            'l10n_mode' => 'mergeIfNotBlank',
            'label' => 'LLL:EXT:talisman_example_trip/Resources/Private/Language/locallang_be.xlf:tx_talismanexampletrip_domain_model_slide.title',
            'config' => [
                'type' => 'text',
                'eval' => 'trim, required',
                'size' => 30,
            ]
        ],
        'description' => [
            'exclude' => false,
            'l10n_mode' => 'mergeIfNotBlank',
            'label' => 'LLL:EXT:talisman_example_trip/Resources/Private/Language/locallang_be.xlf:tx_talismanexampletrip_domain_model_slide.description',
            'config' => [
                'type' => 'text',
                'eval' => 'trim',
                'size' => 30,
            ],
            'defaultExtras' => 'richtext[]:rte_transform[flag=rte_enabled|mode=ts_css]'
        ],
        'extra_description' => [
            'exclude' => false,
            'l10n_mode' => 'mergeIfNotBlank',
            'label' => 'LLL:EXT:talisman_example_trip/Resources/Private/Language/locallang_be.xlf:tx_talismanexampletrip_domain_model_slide.extra_description',
            'displayCond' => 'FIELD:type:=:2',
            'config' => [
                'type' => 'text',
                'eval' => 'trim',
                'size' => 30,
            ],
            'defaultExtras' => 'richtext[]:rte_transform[flag=rte_enabled|mode=ts_css]'
        ],
        'subtitle' => [
            'exclude' => false,
            'l10n_mode' => 'mergeIfNotBlank',
            'label' => 'LLL:EXT:talisman_example_trip/Resources/Private/Language/locallang_be.xlf:tx_talismanexampletrip_domain_model_slide.subtitle',
            'config' => [
                'type' => 'text',
                'eval' => 'trim',
                'size' => 30,
            ]
        ],
        'day_text' => [
            'exclude' => false,
            'l10n_mode' => 'mergeIfNotBlank',
            'label' => 'LLL:EXT:talisman_example_trip/Resources/Private/Language/locallang_be.xlf:tx_talismanexampletrip_domain_model_slide.day_text',
            'config' => [
                'type' => 'text',
                'eval' => 'trim',
            ]
        ],
        'type' => [
            'label' => 'LLL:EXT:talisman_example_trip/Resources/Private/Language/locallang_be.xlf:tx_talismanexampletrip_domain_model_slide.type',
            'config' => [
                'type' => 'select',
                'items' => [
                    ['LLL:EXT:talisman_example_trip/Resources/Private/Language/locallang_be.xlf:tx_talismanexampletrip_domain_model_slide.type.0', 0],
                    ['LLL:EXT:talisman_example_trip/Resources/Private/Language/locallang_be.xlf:tx_talismanexampletrip_domain_model_slide.type.1', 1],
                    ['LLL:EXT:talisman_example_trip/Resources/Private/Language/locallang_be.xlf:tx_talismanexampletrip_domain_model_slide.type.2', 2],
                ],
            ],
        ],
        'visual' => [
            'label' => 'LLL:EXT:talisman_example_trip/Resources/Private/Language/locallang_be.xlf:tx_talismanexampletrip_domain_model_slide.visual',
            'config' => \TYPO3\CMS\Core\Utility\ExtensionManagementUtility::getFileFieldTCAConfig(
                'visual',
                [
                    'appearance' => [
                        'createNewRelationLinkTitle' => 'LLL:EXT:frontend/Resources/Private/Language/locallang_ttc.xlf:media.addFileReference'
                    ],
                    'minitems' => 1,
                    'maxitems' => 1,
                ],
                $GLOBALS['TYPO3_CONF_VARS']['GFX']['imagefile_ext']
            ),
        ],
        'visual_position' => [
            'label' => 'LLL:EXT:talisman_example_trip/Resources/Private/Language/locallang_be.xlf:tx_talismanexampletrip_domain_model_slide.visual_position',
            'config' => [
                'type' => 'select',
                'items' => [
                    ['LLL:EXT:talisman_example_trip/Resources/Private/Language/locallang_be.xlf:tx_talismanexampletrip_domain_model_slide.visual_position.left-top', 'left top'],
                    ['LLL:EXT:talisman_example_trip/Resources/Private/Language/locallang_be.xlf:tx_talismanexampletrip_domain_model_slide.visual_position.left-center', 'left center'],
                    ['LLL:EXT:talisman_example_trip/Resources/Private/Language/locallang_be.xlf:tx_talismanexampletrip_domain_model_slide.visual_position.left-bottom', 'left bottom'],
                    ['LLL:EXT:talisman_example_trip/Resources/Private/Language/locallang_be.xlf:tx_talismanexampletrip_domain_model_slide.visual_position.right-top', 'right top'],
                    ['LLL:EXT:talisman_example_trip/Resources/Private/Language/locallang_be.xlf:tx_talismanexampletrip_domain_model_slide.visual_position.right-center', 'right center'],
                    ['LLL:EXT:talisman_example_trip/Resources/Private/Language/locallang_be.xlf:tx_talismanexampletrip_domain_model_slide.visual_position.right-bottom', 'right bottom'],
                    ['LLL:EXT:talisman_example_trip/Resources/Private/Language/locallang_be.xlf:tx_talismanexampletrip_domain_model_slide.visual_position.center-top', 'center top'],
                    ['LLL:EXT:talisman_example_trip/Resources/Private/Language/locallang_be.xlf:tx_talismanexampletrip_domain_model_slide.visual_position.center-center', 'center center'],
                    ['LLL:EXT:talisman_example_trip/Resources/Private/Language/locallang_be.xlf:tx_talismanexampletrip_domain_model_slide.visual_position.center-bottom', 'center bottom'],
                ],
            ],
        ],
        'timeline' => [
            'exclude' => true,
            'l10n_mode' => 'mergeIfNotBlank',
            'displayCond' => 'FIELD:type:<:2',
            'label' => 'LLL:EXT:talisman_example_trip/Resources/Private/Language/locallang_be.xlf:tx_talismanexampletrip_domain_model_slide.timeline',
            'config' => [
                'type' => 'inline',
                'foreign_table' => 'tx_talismanexampletrip_domain_model_timeline',
                'foreign_field' => 'slide_id',
                'foreign_sortby' => 'sorting',
                'maxitems' => '2',
                'appearance' => [
                    'collapseAll' => true,
                    'expandSingle' => true,
                    'useSortable' => true,
                    'enabledControls' => true,
                    'createNewRelationLinkTitle' => 'LLL:EXT:frontend/Resources/Private/Language/locallang_ttc.xlf:media.addFileReference',
                    'newRecordLinkAddTitle' => true,
                    'levelLinksPosition' => 'top',
                    'showSynchronizationLink' => false,
                    'showAllLocalizationLink' => true,
                    'showPossibleLocalizationRecords' => true,
                    'showRemovedLocalizationRecords' => true,
                ]
            ],
        ],
        'price' => [
            'exclude' => true,
            'l10n_mode' => 'mergeIfNotBlank',
            'displayCond' => 'FIELD:type:=:2',
            'label' => 'LLL:EXT:talisman_example_trip/Resources/Private/Language/locallang_be.xlf:tx_talismanexampletrip_domain_model_slide.price',
            'config' => [
                'type' => 'inline',
                'foreign_table' => 'tx_talismanexampletrip_domain_model_price',
                'foreign_field' => 'slide_id',
                'foreign_sortby' => 'sorting',
                'maxitems' => '3',
                'appearance' => [
                    'collapseAll' => true,
                    'expandSingle' => true,
                    'useSortable' => true,
                    'enabledControls' => true,
                    'createNewRelationLinkTitle' => 'LLL:EXT:frontend/Resources/Private/Language/locallang_ttc.xlf:media.addFileReference',
                    'newRecordLinkAddTitle' => true,
                    'levelLinksPosition' => 'top',
                    'showSynchronizationLink' => false,
                    'showAllLocalizationLink' => true,
                    'showPossibleLocalizationRecords' => true,
                    'showRemovedLocalizationRecords' => true,
                ]
            ],
        ],
        'page_content' => [
            'exclude' => true,
            'l10n_mode' => 'mergeIfNotBlank',
            'displayCond' => 'FIELD:type:=:0',
            'label' => 'LLL:EXT:talisman_example_trip/Resources/Private/Language/locallang_be.xlf:tx_talismanexampletrip_domain_model_slide.page_content',
            'config' => [
                'type' => 'group',
                'internal_type' => 'db',
                'allowed' => 'pages',
                'maxitems' => 1,
                'wizards' => [
                    'suggest' => [
                        'type' => 'suggest'
                    ]
                ]
            ]
        ],
    ]
];
