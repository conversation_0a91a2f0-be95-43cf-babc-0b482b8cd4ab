<?xml version="1.0" encoding="UTF-8"?>
<xliff version="1.0">
    <file source-language="en" target-language="nl" datatype="plaintext" original="messages" date="2012-10-17T19:30:32Z" product-name="talisman_website">
        <header/>
        <body>
            <trans-unit id="gridelements.background_color">
                <source>Background color</source>
            </trans-unit>
            <trans-unit id="gridelements.background_color.white">
                <source>White</source>
            </trans-unit>
            <trans-unit id="gridelements.background_color.black">
                <source>Black</source>
            </trans-unit>
            <trans-unit id="gridelements.background_color.pink">
                <source>Pink</source>
            </trans-unit>
            <trans-unit id="gridelements.spacing">
                <source>Spacing</source>
            </trans-unit>
            <trans-unit id="gridelements.spacing.default">
                <source>Default</source>
            </trans-unit>
            <trans-unit id="gridelements.spacing.no_margin">
                <source>No margin</source>
            </trans-unit>
            <trans-unit id="gridelements.style">
                <source>Style</source>
            </trans-unit>
            <trans-unit id="gridelements.style.default">
                <source>Default</source>
            </trans-unit>
            <trans-unit id="gridelements.style.collection">
                <source>Collection</source>
            </trans-unit>
            <trans-unit id="gridelements.mobile_behaviour">
                <source>Mobile behaviour</source>
            </trans-unit>
            <trans-unit id="gridelements.equal_height">
                <source>Equal height</source>
            </trans-unit>
            <trans-unit id="gridelements.mobile_behaviour.below">
                <source>Columns below each other (default)</source>
            </trans-unit>
            <trans-unit id="gridelements.mobile_behaviour.aside">
                <source>Columns next to each other</source>
            </trans-unit>
            <trans-unit id="gridelements.open">
                <source>Open on page load</source>
            </trans-unit>
            <trans-unit id="gridelements.full_width_buttons">
                <source>Full width buttons</source>
            </trans-unit>
        </body>
    </file>
</xliff>
