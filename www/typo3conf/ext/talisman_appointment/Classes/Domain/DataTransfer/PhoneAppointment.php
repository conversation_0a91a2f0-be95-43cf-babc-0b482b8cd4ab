<?php
namespace Redkiwi\TalismanAppointment\Domain\DataTransfer;

/**
 * Data transfer: phone appointment
 *
 * @package Redkiwi\TalismanAppointment\Domain\DataTransfer
 */
class PhoneAppointment
{
    /**
     * @var string
     * @validate NotEmpty
     */
    protected $name;

    /** @var string */
    protected $description;

    /** @var string */
    protected $known;

    /**
     * @var string
     * @validate NotEmpty
     */
    protected $phone;

    /** @var string */
    protected $preferredReachDateAnswer;

    /** @var string */
    protected $preferredReachTimeAnswer;

    /** @var string */
    protected $preferredReachTimeOtherAnswer;

    /**
     * @return string
     */
    public function getName()
    {
        return $this->name;
    }

    /**
     * @param string $name
     * @return void
     */
    public function setName($name)
    {
        $this->name = $name;
    }

    /**
     * @return string
     */
    public function getDescription()
    {
        return $this->description;
    }

    /**
     * @param string $description
     * @return void
     */
    public function setDescription($description)
    {
        $this->description = $description;
    }

    /**
     * @return string
     */
    public function getKnown()
    {
        return $this->known;
    }

    /**
     * @param string $known
     * @return void
     */
    public function setKnown($known)
    {
        $this->known = $known;
    }

    /**
     * @return string
     */
    public function getPhone()
    {
        return $this->phone;
    }

    /**
     * @param string $phone
     * @return void
     */
    public function setPhone($phone)
    {
        $this->phone = $phone;
    }

    /**
     * @return string
     */
    public function getPreferredReachDateAnswer()
    {
        return $this->preferredReachDateAnswer;
    }

    /**
     * @param string $preferredReachDateAnswer
     */
    public function setPreferredReachDateAnswer($preferredReachDateAnswer)
    {
        $this->preferredReachDateAnswer = $preferredReachDateAnswer;
    }

    /**
     * @return string
     */
    public function getPreferredReachTimeAnswer()
    {
        return $this->preferredReachTimeAnswer;
    }

    /**
     * @param string $preferredReachTimeAnswer
     */
    public function setPreferredReachTimeAnswer($preferredReachTimeAnswer)
    {
        $this->preferredReachTimeAnswer = $preferredReachTimeAnswer;
    }

    /**
     * @return string
     */
    public function getPreferredReachTimeOtherAnswer()
    {
        return $this->preferredReachTimeOtherAnswer;
    }

    /**
     * @param string $preferredReachTimeOtherAnswer
     */
    public function setPreferredReachTimeOtherAnswer($preferredReachTimeOtherAnswer)
    {
        $this->preferredReachTimeOtherAnswer = $preferredReachTimeOtherAnswer;
    }
}
