//
// Talisman bootstrap carousel styling
//
.talisman-bootstrap-carousel {

    .item {
        height: 520px;

        .gradient {
            background: linear-gradient(rgba(0, 0, 0, 0), rgba(0, 0, 0, 0.3));
            height: 520px;
            width: 100%;
        }

        .carousel-caption {
            text-align: left;
            padding-bottom: 58px;

            h3 {
                @include custom-font-family('Nunito', 600);
                font-size: 58px;
                line-height: 54px;
                color: $white;
                text-shadow: none;
                margin-bottom: 24px;
            }
        }

        &.cover {
            background-size: cover;
        }

        &.contain {
            background-size: contain;
        }
    }

    .carousel-indicators {
        left: 20%;
        margin-left: 0;
        text-align: left;
        bottom: 26px;

        li {
            margin: 0;

            &.active {
                width: 10px;
                height: 10px;
            }
        }
    }

    .carousel-control {
        background-size: 21px 35px;
        background-position: center center;
        background-repeat: no-repeat;

        &.left {
            background-image: url(/typo3conf/ext/talisman_template/Resources/Public/Icons/carousel-arrow-left.png);
        }

        &.right {
            background-image: url(/typo3conf/ext/talisman_template/Resources/Public/Icons/carousel-arrow-right.png);
        }

        &:hover, &:focus, &:active {
            opacity: 0.5;
        }
    }

}

//
// In a two column template, make sure the submenu goes up and change the font
//
.two-columns {

    .talisman-bootstrap-carousel {
        margin-bottom: -50px;

        .item {

            .carousel-caption {

                h3 {
                    @include custom-font-family('Nunito', 600);
                    text-transform: uppercase;

                    &::first-line {
                        text-transform: uppercase;
                    }
                }
            }
        }
    }
}

//
// Add hovers to lg
//
@media (min-width: $screen-lg-min) {

    .talisman-bootstrap-carousel {

        .carousel-control {

            &:hover, &:focus, &:active {
                opacity: 0.9;
            }
        }
    }
}

//
// Change positioning on md
//
@media (min-width: $screen-md-min) and (max-width: $screen-md-max) {

    .talisman-bootstrap-carousel {

        .item {
            height: 587px;

            .carousel-caption {
                left: 4%;
                right: 4%;

                h3 {
                    font-size: 50px;
                    line-height: 46px;
                }
            }
        }

        .carousel-indicators {
            left: 4%;
            right: 4%;
        }

        .carousel-control {
            width: 10%;
        }
    }
}

//
// Change positioning on sm > in a two column layout
//
@media (min-width: $screen-sm-min) {

    .two-columns {

        .talisman-bootstrap-carousel {

            .carousel-caption {
                bottom: 82px;
            }

            .carousel-indicators {
                bottom: 88px;
            }
        }
    }
}

//
// Change positioning / font sizes on sm
//
@media (min-width: $screen-sm-min) and (max-width: $screen-sm-max) {

    .talisman-bootstrap-carousel {

        .item {
            height: 620px;

            .carousel-caption {
                left: 3%;
                right: 3%;

                h3 {
                    font-size: 46px;
                    line-height: 42px;
                }
            }
        }

        .carousel-indicators {
            left: 3%;
            right: 3%;
        }

        .carousel-control {
            width: 10%;
        }
    }
}

//
// Change positioning on xs and hide carousel control
//
@media (max-width: $screen-xs-max) {

    .talisman-bootstrap-carousel {

        .item {
            height: 485px;

            .carousel-caption {
                left: 4%;
                right: 4%;

                h3 {
                    font-size: 34px;
                    line-height: 32px;
                }
            }
        }

        .carousel-indicators {
            left: 4%;
            right: 4%;
        }

        .carousel-control {
            display: none;
        }
    }

    .two-columns {

        .talisman-bootstrap-carousel {
            margin-bottom: 50px;
        }
    }
}
