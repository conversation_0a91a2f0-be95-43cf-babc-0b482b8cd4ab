<div class="cards no-background-image no-hover">
    <figure>
        <f:if condition="{tile.visual.uid}">
            <f:image src="{f:uri.image(image: tile.visual)}" alt="" height="240c" width="360"/>
        </f:if>
    </figure>
    <div class="inner-wrapper">
        <div>
            <h3 class="left no-uppercase large">{tile.title}</h3>
            <div class="content">
                <p>
                    {tile.subtitle}
                </p>
                
            </div>
        </div>
        <div class="col-sm-12 btn-container">
            <f:if condition="{tile.link}">
                <f:link.typolink parameter="{tile.link}" class="btn btn-default btn-rubine">
                    {tile.buttonText}
                </f:link.typolink>
            </f:if>
        </div>
    </div>
</div>