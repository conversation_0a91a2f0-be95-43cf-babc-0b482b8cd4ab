plugin.tx_talismanexampletrip {
    view {
        templateRootPaths {
            10 = EXT:talisman_example_trip/Resources/Private/Templates/
        }
        partialRootPaths {
            10 = EXT:talisman_example_trip/Resources/Private/Partials/
            20 = EXT:talisman_experts/Resources/Private/Partials/
        }
        layoutRootPaths {
            10 = EXT:talisman_example_trip/Resources/Private/Layouts/
        }
    }

    settings {
        pages {
            root = {$pages.root}
            brochure = {$pages.brochure}
            quote = {$pages.quote}
            newsletter = {$pages.newsletter}
            phoneAppointment = {$pages.phoneAppointment}
            visit = {$pages.visit}
            askAQuestion = {$pages.askAQuestion}
            storage {
                experts = {$pages.experts}
            }
        }
        telephoneNumber = {$telephoneNumber}
    }
}

module.tx_talismanexampletrip < plugin.tx_talismanexampletrip

