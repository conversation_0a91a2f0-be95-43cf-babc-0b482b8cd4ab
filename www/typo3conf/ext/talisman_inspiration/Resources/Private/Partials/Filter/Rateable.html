<html xmlns:f="http://xsd.helmut-hummel.de/ns/TYPO3/CMS/Fluid/ViewHelpers"
    xmlns="http://www.w3.org/1999/xhtml"

    data-namespace-typo3-fluid="true">
<f:if condition="{category}">
    <div class="talisman-inspiration-filters filter-rating">
        <div class="filter-container panel panel-default" id="filter-{category.uid}">
            <div class="filter-header panel-heading panel-collapse-icons" data-toggle="collapse" data-target="#filter-{category.uid}-options">
                <div class="panel-title">
                    {category.title}
                    <div class="icons">
                        <span class="toggle-close sprite-icon-48 minus{f:if(condition: iteration.isFirst, else: ' hide')}"></span>
                        <span class="toggle-open sprite-icon-48 arrow-down black{f:if(condition: iteration.isFirst, then: ' hide')}"></span>
                    </div>
                </div>
            </div>

            <div class="filter-options panel-collapse panel-body collapse{f:if(condition: iteration.isFirst, then: ' in')}" id="filter-{category.uid}-options">
                <f:for each="{category.children}" as="type">
                    <div class="type-select-ratio filter-category-{type.uid}">
                        <div class="title">{type.title}</div>
                        <div class="ratio">
                            <div class="row">
                                <span class="indicator min">1</span>
                                <input id="filter-{category.uid}-{type.uid}" type="text"
                                    class="form-control ratio-slider"
                                    name="category[rating][{category.uid}][{type.uid}]"
                                    data-provide="slider"
                                    data-label="{type.title}"
                                    data-slider-min="1"
                                    data-slider-max="5"
                                    data-slider-step="1"
                                    data-slider-value="3"
                                    data-slider-tooltip="hide"/>
                                <span class="indicator max">5</span>
                            </div>
                        </div>
                    </div>
                </f:for>
            </div>
        </div>
    </div>
</f:if>
</html>
