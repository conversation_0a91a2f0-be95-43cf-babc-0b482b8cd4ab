<?php
namespace <PERSON><PERSON>wi\TalismanExperts\Controller;

use TYPO3\CMS\Core\Error\Exception;
use Red<PERSON>wi\TalismanExperts\Domain\Repository\ExpertRepository;

/**
 * Controller: List
 *
 * @package Redkiwi\TalismanExperts\Controller
 */
class ListController extends \TYPO3\CMS\Extbase\Mvc\Controller\ActionController
{
    /** @var ExpertRepository */
    protected $expertRepository;

    /**
     * @param ExpertRepository $expertRepository
     */
    public function __construct(ExpertRepository $expertRepository)
    {
        parent::__construct();
        $this->expertRepository = $expertRepository;
    }

    /**
     * @throws Exception
     * @return void
     */
    public function showAction()
    {
        if (!empty($this->settings['pages']['storage']['experts'])) {
            $experts = $this->expertRepository->findByPid($this->settings['pages']['storage']['experts']);
            $this->view->assignMultiple([
                'experts' => $experts,
                'contentUid' => $this->configurationManager->getContentObject()->getFieldVal('uid')
            ]);
        } else {
            throw new Exception('Experts storage page not set.', 1563458518322);
        }
    }
}
