<?php

namespace Redkiwi\TalismanLikes\Domain\Model;

/**
 * Model: File reference
 *
 * @package Redkiwi\TalismanLikes\Domain\Model
 */
class FileReference extends \TYPO3\CMS\Extbase\Domain\Model\FileReference
{
    /** @var \Redkiwi\TalismanPage\Domain\Model\Page */
    protected $country;

    /** @var int */
    protected $type;

    /** @var int */
    protected $icon;

    /**
     * @return \Redkiwi\TalismanPage\Domain\Model\Page
     */
    public function getCountry()
    {
        return $this->country;
    }

    /**
     * @param \Redkiwi\TalismanPage\Domain\Model\Page $country
     * @return void
     */
    public function setCountry($country)
    {
        $this->country = $country;
    }

    /**
     * @return int
     */
    public function getType()
    {
        return $this->type;
    }

    /**
     * @param int $type
     * @return void
     */
    public function setType($type)
    {
        $this->type = $type;
    }

    /**
     * @return int
     */
    public function getIcon()
    {
        return $this->icon;
    }

    /**
     * @param int $icon
     * @return void
     */
    public function setIcon($icon)
    {
        $this->icon = $icon;
    }
}
