<?php
namespace Redkiwi\TalismanPage\Domain\Model;

use TYPO3\CMS\Extbase\DomainObject\AbstractEntity;

/**
 * Model: Category rating
 *
 * @package Redkiwi\TalismanPage\Domain\Model
 */
class CategoryRating extends AbstractEntity
{

    const TABLE = 'tx_talismanpage_domain_model_category_rating';
    const MM_CATEGORY_TABLE = 'sys_category_record_mm';
    const RATING_MINIMUM = 1;
    const RATING_MAXIMUM = 5;

    /** @var int */
    protected $rating;

    /** @var \Redkiwi\TalismanTemplate\Domain\Model\Category */
    protected $category;

    /**
     * @return boolean
     */
    public function isValid()
    {
        return (bool)($this->getRating() > 0 && $this->getCategory());
    }

    /**
     * @return int
     */
    public function getRating()
    {
        return $this->rating;
    }

    /**
     * @param int $rating
     * @return void
     */
    public function setRating($rating)
    {
        $this->rating = $rating;
    }

    /**
     * @return \Redkiwi\TalismanTemplate\Domain\Model\Category
     */
    public function getCategory()
    {
        return $this->category;
    }

    /**
     * @param \Redkiwi\TalismanTemplate\Domain\Model\Category $category
     * @return void
     */
    public function setCategory($category)
    {
        $this->category = $category;
    }
}
