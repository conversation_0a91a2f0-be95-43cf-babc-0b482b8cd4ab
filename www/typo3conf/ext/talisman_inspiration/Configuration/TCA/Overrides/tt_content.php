<?php

use TYPO3\CMS\Extbase\Utility\ExtensionUtility;

defined('TYPO3_MODE') or die ('Access denied.');

call_user_func(function ($extension, $table) {

    ExtensionUtility::registerPlugin(
        $extension,
        'Overview',
        'LLL:EXT:' . $extension . '/Resources/Private/Language/locallang_be.xlf:plugin.overview.title'
    );
    if ($registeredPlugin = 'talismaninspiration_overview') {
        $GLOBALS['TCA']['tt_content']['types']['list']['subtypes_addlist'][$registeredPlugin] = 'pi_flexform';
        \TYPO3\CMS\Core\Utility\ExtensionManagementUtility::addPiFlexFormValue(
            $registeredPlugin,
            'FILE:EXT:' . $extension . '/Configuration/FlexForm/OverviewConfiguration.xml'
        );
    }

    ExtensionUtility::registerPlugin(
        $extension,
        'Widget',
        'LLL:EXT:' . $extension . '/Resources/Private/Language/locallang_be.xlf:plugin.widget.title',
        'EXT:' . $extension . '/Resources/Public/Icons/widget.jpg'
    );
    if ($registeredPlugin = 'talismaninspiration_widget') {
        $GLOBALS['TCA'][$table]['types'][$registeredPlugin] = [
            'showitem' => '--palette--;LLL:EXT:frontend/Resources/Private/Language/locallang_ttc.xml:palette.general;general, pi_flexform,'
                . '--div--;LLL:EXT:frontend/Resources/Private/Language/locallang_ttc.xml:tabs.appearance,'
                . '--palette--;LLL:EXT:frontend/Resources/Private/Language/locallang_ttc.xml:palette.frames;frames,'
                . '--div--;LLL:EXT:frontend/Resources/Private/Language/locallang_ttc.xml:tabs.access,'
                . '--palette--;LLL:EXT:frontend/Resources/Private/Language/locallang_ttc.xml:palette.visibility;visibility,'
                . '--palette--;LLL:EXT:frontend/Resources/Private/Language/locallang_ttc.xml:palette.access;access,'
                . '--div--;LLL:EXT:frontend/Resources/Private/Language/locallang_ttc.xml:tabs.extended'
        ];
        \TYPO3\CMS\Core\Utility\ExtensionManagementUtility::addPiFlexFormValue(
            '*',
            'FILE:EXT:' . $extension . '/Configuration/FlexForm/WidgetConfiguration.xml',
            $registeredPlugin
        );
    }

}, 'talisman_inspiration', 'tt_content');
