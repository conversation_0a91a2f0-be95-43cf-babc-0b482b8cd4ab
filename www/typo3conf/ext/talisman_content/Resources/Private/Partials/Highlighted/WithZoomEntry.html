<html xmlns:f="http://typo3.org/ns/TYPO3/CMS/Fluid/ViewHelpers"
    xmlns:likes="http://typo3.org/ns/Redkiwi/TalismanLikes/ViewHelpers"
    data-namespace-typo3-fluid="true">
<likes:likeableImage reference="{file}">
    <f:link.typolink parameter="{link}">
        <figure>
            <f:if condition="{file}">
                <f:then>
                    <f:if condition="{layout} == 106">
                        <f:then>
                            <f:image
                                    image="{file}"
                                    alt="{file.properties.alt}"
                                    title="{file.properties.title}"
                                    class="img-responsive"/>
                        </f:then>
                        <f:else>
                            <f:image
                                    image="{file}"
                                    alt="{file.properties.alt}"
                                    title="{file.properties.title}"
                                    width="{f:if(condition: '{layout} == 103', then: '750', else: '555c')}"
                                    height="{f:if(condition: '{layout} == 103', then: '430', else: '370c')}"
                                    class="img-responsive"/>
                        </f:else>
                    </f:if>
                </f:then>
                <f:else>
                    <img
                        src="{f:if(condition: '{layout} == 103', then: 'https://placehold.it/750x430', else: 'https://placehold.it/555x370')}"
                        class="img-responsive"/>
                </f:else>
            </f:if>

            <f:if condition="{file.properties.title}">
                <figcaption>
                    <h2>{file.properties.title -> f:format.nl2br()}</h2>
                    <f:if condition="{file.properties.description}">
                    <span>
                        <f:format.nl2br>{file.properties.description}</f:format.nl2br>
                    </span>
                    </f:if>
                </figcaption>
            </f:if>
            <div class="gradient"></div>
            <div class="image-actions">
                <f:if condition="{markerIcon}">
                    <div class="marker-icon hidden-xs hidden-sm" data-action="show-accommodation-on-map" data-marker-uid="{markerUid}">
                        <span class="sprite-icon-48 marker white"></span>
                    </div>
                </f:if>
                {like -> f:format.raw()}
            </div>
        </figure>
    </f:link.typolink>
</likes:likeableImage>
</html>
