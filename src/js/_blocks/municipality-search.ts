import Alpine from 'alpinejs';

export default function initMunicipalitySearch() {
    Alpine.store('municipalitySearch', {
        lang: 'nl-NL',
        key: '',
        host: '',
        searchIndex: '',
        searchTerm: '',
        results: [],
        hasNoResults(): boolean {
            return this.results.length === 0;
        },
        clearResultsWhenEmpty(): void {
            if (this.searchTerm.length === 0) {
                this.results = [];
            }
        },
        async getResults(searchTerm: string): Promise<void> {
            this.searchTerm = searchTerm;
            this.clearResultsWhenEmpty();
            if (this.key === undefined)
                return console.error('No API key found');
            if (searchTerm.length < 3) return;

            const results = await fetch(
                `${this.host}/indexes/${this.searchIndex}/search`,
                {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        Authorization: `Bearer ${this.key}`,
                    },
                    body: JSON.stringify({
                        q: searchTerm,
                        limit: 5,
                    }),
                }
            )
                .then((response) => response.json())
                .then((data) => {
                    return data;
                })
                .catch(() => {
                    return {
                        error: true,
                    };
                });

            if (results.error)
                return console.error(
                    'Something went wrong while fetching the search results'
                );

            this.results = results.hits;
        },
        goToRoute(route: string): void {
            window.location.replace(route);
        },
    });

    Alpine.store('functions', {
        lastScrollPosition: 0,
    });

    document.addEventListener('alpine:init', () => {
        Alpine.data('municipalitySearch', (data) => {
            Alpine.store('municipalitySearch').searchIndex = data.searchIndex;
            Alpine.store('municipalitySearch').lang = data.lang;
            Alpine.store('municipalitySearch').host = data.host;
            Alpine.store('municipalitySearch').key = data.key;
            Alpine.store('municipalitySearch').searchTerm = '';
        });
    });
}
