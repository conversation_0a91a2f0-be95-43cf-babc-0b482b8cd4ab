<?php
namespace Redkiwi\TalismanUserRegistration\Domain\DataTransfer;

/**
 * Data transfer: Activate
 *
 * @package Redkiwi\TalismanUserRegistration\Domain\DataTransfer
 */
class Activate
{
    /**
     * @var string
     * @validate NotEmpty, \Redkiwi\TalismanUserRegistration\Validation\Validator\TrimmedPasswordLengthValidator(minimum=5)
     */
    protected $password;

    /**
     * @var string
     * @validate NotEmpty, \Redkiwi\TalismanUserRegistration\Validation\Validator\MatchingPasswordValidator
     */
    protected $repeatPassword;

    /**
     * @return string
     */
    public function getPassword()
    {
        return $this->password;
    }

    /**
     * @param string $password
     * @return void
     */
    public function setPassword($password)
    {
        $this->password = $password;
    }

    /**
     * @return string
     */
    public function getRepeatPassword()
    {
        return $this->repeatPassword;
    }

    /**
     * @param string $repeatPassword
     * @return void
     */
    public function setRepeatPassword($repeatPassword)
    {
        $this->repeatPassword = $repeatPassword;
    }
}
