<?php
namespace Redkiwi\TalismanUserRegistration\Validation\Validator;

/**
 * Validator: Password length
 *
 * @package Redkiwi\TalismanUserRegistration\Validation\Validator
 */
class TrimmedPasswordLengthValidator extends \TYPO3\CMS\Extbase\Validation\Validator\StringLengthValidator
{
    /**
     * @param mixed $value
     * @return void
     * @throws \TYPO3\CMS\Extbase\Validation\Exception\InvalidValidationOptionsException
     */
    public function isValid($value)
    {
        $value = trim($value);
        parent::isValid($value);
    }
}
