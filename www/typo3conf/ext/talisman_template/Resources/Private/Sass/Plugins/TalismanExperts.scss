//
// Talisman experts styling
//
.talisman-experts {

    &.list {

        .expert-item {

            display: block;
            margin-bottom: 30px;
            max-width: 260px;
            position: relative;

            .gradient {

                background: rgba(0, 0, 0, 0) linear-gradient(transparent, rgba(0, 0, 0, 0.3)) repeat;
                bottom: 0;
                height: 100px;
                position: absolute;
                width: 100%;
                z-index: $zindex-default;

            }

            figure {

                figcaption {

                    bottom: 10px;
                    color: $white;
                    left: 15px;
                    position: absolute;
                    z-index: $zindex-heavy;

                    span {

                        display: block;

                        &.expert-name {

                            @include custom-font-family('Nunito', 600);

                            font-size: 18px;

                        }

                        &.expert-destinations {

                            @include custom-font-family('Nunito', 600);

                            font-size: 14px;
                        }

                    }

                }

            }

        }

        #page-1 & {

            .carousel-control {

                top: 30%;

                &.right {

                    margin-right: 50px;

                    @media (min-width: $screen-sm) {

                        margin-right: 6px;

                    }

                }

                &.left {

                    margin-left: -2px;

                    @media (min-width: $screen-sm) {

                        margin-left: 0;

                    }

                }

            }

            .carousel-inner {

                margin-left: 0;

                @media (min-width: $screen-sm) {

                    margin-left: 20px;

                }

            }

            .slide {

                margin-left: 0;
                width: 100%;

                @media (min-width: $screen-sm) {

                    margin-left: -36px;

                }

            }

            .expert-item {

                margin-bottom: 12px;
                overflow: hidden;
                position: relative;

                @media (min-width: $screen-sm) {

                    margin-bottom: 32px;

                }

                img {

                    opacity: .9;

                }

                figcaption {

                    text-align: left;

                }

            }

            .btn-expert,
            .btn-gold {

                display: inline-block;
                line-height: 22px;

                &:after {

                    content: "›";
                    font-size: 22px;
                    margin-left: 10px;

                }

            }

            .btn-expert {

                display: inline-block;
                font-size: 12px;
                margin-bottom: 24px;
                padding-top: 0;
                width: 74%;

                @media (min-width: $screen-sm) {

                    width: 100%;

                }

                @media (min-width: $screen-md) {

                    width: 76%;
                    padding-left: 24px;
                    font-size: 13px;

                }

            }

            .btn-gold {

                margin: 0;
                width: 90%;

                @media (min-width: $screen-sm) {

                    margin-top: 40px;
                    width: auto;

                }

            }

        }

    }

    &.widget {
        float: right;
        height: 521px;
        margin-bottom: 120px;
        width: 263px;

        .talisman-experts-affix {
            @include disable-ios-flickering();
            position: absolute;
            width: 263px;
            z-index: $zindex-heavy - 1;

            &.affix-bottom {
                position: absolute;
            }
        }

        h2 {
            @include custom-font-family('Nunito', 600);
            color: $black;
            font-size: 18px;
            line-height: 26px;
            margin-bottom: 2px;
            margin-top: 0;
            text-transform: uppercase;
        }

        .expert-carousel {

            .gradient {

                background: rgba(0, 0, 0, 0) linear-gradient(transparent, rgba(0, 0, 0, 0.3)) repeat;
                bottom: 0;
                display: block;
                height: 50px;
                position: absolute;
                width: 100%;
                z-index: $zindex-default;

            }


            .item .carousel-caption h3 {

                opacity: 0;
                transition: all 0.4s ease 0s;

            }

            .item.active .carousel-caption h3 {

                opacity: 1;

            }

            .carousel-caption {

                bottom: 12px;
                display: block;
                padding: 0;
                text-shadow: none;
                z-index: $zindex-heavy;

                .right-column & {

                    display: block;
                    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.6);

                }

                h3 {

                    @include custom-font-family('Nunito', 600);

                    color: $white;
                    font-size: 18px;
                    line-height: 18px;
                    margin: 0;

                }

            }

            .carousel-control {

                background-position: center 205px;
                background-repeat: no-repeat;
                background-size: 7px 12px;

                .right-column & {

                    background-position: center center;

                    position: absolute;
                    top: 0;
                    width: 20px;

                    &:hover {

                        background-color: rgba($gold, .25);

                    }

                }

                &.left {

                    background-image: url(/typo3conf/ext/talisman_template/Resources/Public/Icons/carousel-arrow-left-small.png);

                    .right-column & {

                        bottom: 0;
                        height: 100%;
                        left: 10px;

                    }

                }

                &.right {

                    background-image: url(/typo3conf/ext/talisman_template/Resources/Public/Icons/carousel-arrow-right-small.png);

                    .right-column & {

                        bottom: 0;
                        height: 100%;
                        right: 10px;

                    }

                }

            }

        }

        ul {

            margin: 0;
            padding: 0;
            width: 100%;

            &.contact {

                background-color: $black;
                float: left;
                padding: 16px;

                li {

                    float: left;
                    margin-bottom: 16px;
                    width: 100%;

                    &:last-child {

                        margin-bottom: 0;

                    }

                    .btn-label {

                        @include custom-font-family('Nunito', 600);

                        color: $white;
                        float: left;
                        font-size: 14px;
                        line-height: 18px;
                        margin-bottom: 8px;
                        text-transform: none;
                        width: 100%;

                    }

                    a {

                        @include custom-font-family('Nunito', 600);
                        font-size: 10px;
                        width: 100%;

                        &:after {

                            right: 12px;
                            transform: scale(0.75);

                        }

                        &:hover,
                        &:active,
                        &:focus {

                            padding: 0 100px 0 20px;
                            text-decoration: none;

                            &:after {

                                margin-right: -8px;

                            }
                        }

                    }

                }

            }

            &.cta {

                li {

                    margin-bottom: 8px;

                    a {

                        width: 100%;

                    }

                    &:last-child {

                        margin-bottom: 22px;

                    }

                }

            }

        }

        h4 {

            @include custom-font-family('Nunito', 600);
            clear: both;
            color: $black;
            font-size: 26px;
            margin-bottom: 11px;
            padding-top: 34px;

        }

    }

}

.no-sub-menu {

    .talisman-experts {

        &.widget {

            .talisman-experts-affix {

                &.affix {

                    top: 158px;

                }

            }

        }

    }

}

//
// Expert modal
//
#expert-modal {

    .expert-container {

        background-color: $white;
        left: 45%;
        min-height: 585px;
        padding: 15px;
        position: absolute;
        top: 25%;
        width: 295px;

        .talisman-experts {

            &.widget {

                margin-bottom: 0;

                .btn {

                    height: 40px;
                    line-height: 40px;

                }

            }

        }

    }

}

@media (min-width: $screen-sm-min) and (max-width: $screen-sm-max) {

    #expert-modal {

        .expert-container {

            left: 33%;

        }

    }

}

@media (min-width: $screen-md-min) and (max-width: $screen-md-max) {

    #expert-modal {

        .expert-container {

            left: 38%;

        }

    }

}

//
// Add hovers to lg
//
@media (min-width: $screen-lg-min) {

    .talisman-experts {

        &.widget {

            .expert-carousel {

                a {

                    &.item {

                        overflow: hidden;

                        img {

                            transition: all 0.4s ease 0s;

                        }

                        &:hover {

                            img {

                                opacity: 1;
                                transform: scale(1.05);

                            }

                        }

                    }

                }

            }

            ul {

                &.share {

                    li {

                        a {

                            &:hover, &:active, &:focus {

                                i {

                                    color: $expert-icon-color-hover;

                                }

                            }

                        }

                    }

                }

            }

        }

    }

}

//
// Center elements on xs
//
@media (max-width: $screen-xs-max) {

    .talisman-experts {

        &.widget {

            float: none;
            margin: 0 auto 0 auto;

        }

        &.list {

            .expert-item {

                margin: 0 auto 30px;

            }

        }

    }

    #expert-modal {

        .expert-container {

            left: 0;
            margin: 75px auto 0 auto;
            top: 0;
            position: relative;

        }

    }

}
