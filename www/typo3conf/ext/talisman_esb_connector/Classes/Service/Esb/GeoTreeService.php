<?php
namespace Redkiwi\TalismanEsbConnector\Service\Esb;

/**
 * Service: Geo tree
 *
 * @package Redkiwi\TalismanEsbConnector\Esb\Service
 */
class GeoTreeService extends AbstractConnectorService
{
    /**
     * @param array $parameters
     * @return array
     * @throws \Exception
     */
    public function makeRequest(array $parameters = [])
    {
        $geoTreeRequest = $this->request('data/geotree');
        if ($geoTreeRequest['info']['http_code'] !== 200) {
            throw new \Exception('Request data/geotree failed with status code ' . $geoTreeRequest['info']['http_code']);
        }

        $returnArray = [];
        $response = new \SimpleXMLElement($geoTreeRequest['response']);

        foreach ($response->additionalData->GEOTree->entry as $entry) {
            $returnArray[] = $this->processGeoTreeEntry($entry);
        }
        return $returnArray;
    }

    /**
     * @param \SimpleXMlElement $xmlEntry
     * @return array
     */
    protected function processGeoTreeEntry(\SimpleXMlElement $xmlEntry)
    {
        $entry = [];
        $entry['travelStudioId'] = (int) $xmlEntry->travelStudioId;
        $entry['name'] = (string) $xmlEntry->name;
        $entry['latitude'] = (double) $xmlEntry->latitude;
        $entry['longitude'] = (double) $xmlEntry->longitude;

        if (isset($xmlEntry->children)) {
            foreach ($xmlEntry->children->entry as $child) {
                $entry['children'][] = $this->processGeoTreeEntry($child);
            }
        }
        return $entry;
    }
}
