.image-action-container {
    position: relative;
}

.image-actions {
    position: absolute;
    right: 0;
    top: 0;
    z-index: $zindex-default + 1;
    font-size: 0;

    .action {
        display: inline-block;
        margin-left: 1px;
    }
}

//
// Moodboard buttons in images
//
.moodboard-entry {
    width: 38px;
    height: 38px;
    position: absolute;
    top: 0;
    right: 0;
    background: rgba(0, 0, 0, 0.3);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: $zindex-default + 1;
    cursor: pointer;

    &.active {
        background: $rubine;
        cursor: default;

        span {
            background-position: -24px 0;
        }
    }

    &.has-errors {
        background: $rubine;

        span {
            background-position: -72px -168px !important;
        }
    }

    //
    // Add hovers for lg
    //
    @media (min-width: $screen-lg-min) {
        &:hover, &:focus, &:active {
            //background: $rubine;

            span {
                background-position: -24px 0;
            }
        }
    }
}
