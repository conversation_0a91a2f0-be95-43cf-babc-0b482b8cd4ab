//
// Talisman widget styling
//
.talisman-content-widget {
    margin-bottom: 30px;
    position: relative;

    &.link {
        background: $gold;
    }

    &.video {
        background: $rubine;
    }

    &.audio {
        background: $gray-light;
    }

    &.streetview {
        background: $black-light;

        a {

            .abstract {

                .inner {

                    h2 {
                        color: $white;
                        border-color: $white;
                    }

                    h3 {
                        color: $gray-light;
                    }
                }
            }
        }
    }

    a {
        display: block;
        outline: 0;

        &:hover, &:focus, &:active {
            text-decoration: none;
        }

        .abstract {

            .inner {
                padding: 15px 15px 0 15px;

                h2 {
                    @include custom-font-family('Nunito', 600);
                    font-size: 21px;
                    line-height: 24px;
                    margin: 0 0 6px;
                    text-transform: uppercase;
                    border-bottom: 2px solid $black;
                    display: inline-block;
                }

                h3 {
                    @include custom-font-family('Nunito', 600);
                    font-size: 21px;
                    line-height: 24px;
                    margin: 0;
                    color: $white;
                    text-transform: uppercase;
                }
            }
        }

        .bottom {
            @include custom-font-family('Nunito', 400);
            position: absolute;
            bottom: 10px;
            left: 15px;
            font-size: 15px;
            line-height: 18px;
            color: $white;
            max-width: 230px;
        }

        figure {
            position: relative;
            overflow: hidden;
            min-height: 175px;

            img {
                transition: all 0.4s ease 0s;
            }

            .gradient {
                position: absolute;
                background: linear-gradient(rgba(0, 0, 0, 0), rgba(0, 0, 0, 0.3));
                left: 0;
                right: 0;
                top: 0;
                bottom: 0;
                z-index: $zindex-lighter;
            }

            .icon {
                background: rgba(0, 0, 0, 0.6);
                border-radius: 50%;
                width: 74px;
                height: 74px;
                position: absolute;;
                margin: 0 auto;
                z-index: $zindex-light;
                text-align: center;
                left: 50%;
                top: 50%;
                transform: translate(-50%, -50%);
            }
        }
    }

    .modal {

        .modal-body {

            iframe {
                height: 480px;
                width: 100%;
            }
        }
    }
}

//
// When used in the left column, add block margin
//
.left-column {

    .talisman-content-widget {

        &.block-margin-1 {
            margin-bottom: 30px;
        }
    }
}

//
// Add hovers for lg
//
@media (min-width: $screen-lg-min) {

    .talisman-content-widget {

        a {

            &:hover, &:focus, &:active {

                figure {

                    img {
                        transform: scale(1.05);
                    }

                    .gradient {
                        display: none;
                    }
                }
            }
        }
    }
}

//
// Styling adjustments for md
//
@media (min-width: $screen-md-min) and (max-width: $screen-md-max) {

    .talisman-content-widget {

        a {

            .bottom {
                max-width: 186px;
            }
        }
    }
}

//
// Styling adjustments for sm
//
@media (min-width: $screen-sm-min) and (max-width: $screen-sm-max) {

    .talisman-content-widget {

        a {

            .bottom {
                max-width: 220px;
            }
        }
    }
}

//
// Styling adjustments for xs
//
@media (max-width: $screen-xs-max) {

    .talisman-content-widget {
        max-width: 490px;
        margin: 0 auto 15px auto;

        a {

            .bottom {
                max-width: 100%;
            }

            .abstract {

                .inner {

                    h3 {
                        margin-bottom: 50px;
                    }
                }
            }
        }

        &.has-author {

            a {

                .abstract {

                    .inner {

                        h3 {
                            margin-bottom: 70px;
                        }
                    }
                }
            }
        }

        .modal {

            .modal-body {

                iframe {
                    height: 240px;
                }
            }
        }
    }
}
