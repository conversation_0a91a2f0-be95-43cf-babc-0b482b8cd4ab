<?php
defined('TYPO3_MODE') or die ('Access denied.');

\TYPO3\CMS\Core\Utility\ArrayUtility::mergeRecursiveWithOverrule($GLOBALS['TYPO3_CONF_VARS']['BE'], [
    'debug' => 0,
]);

\TYPO3\CMS\Core\Utility\ArrayUtility::mergeRecursiveWithOverrule($GLOBALS['TYPO3_CONF_VARS']['FE'], [
    'debug' => 0,
]);

\TYPO3\CMS\Core\Utility\ArrayUtility::mergeRecursiveWithOverrule($GLOBALS['TYPO3_CONF_VARS']['SYS'], [
    'clearCacheSystem' => true,
    'cookieSecure' => true,
    'cookieHttpOnly' => false,
    'displayErrors' => 0,
    'exceptionalErrors' => 20480,
    'sqlDebug' => 0,
    'systemLogLevel' => 2
]);

\TYPO3\CMS\Core\Utility\ArrayUtility::mergeRecursiveWithOverrule($GLOBALS['TYPO3_CONF_VARS']['EXT'], [
    'extConf' => [
        'develement_cookies' => [
            'getParamsToCookies' => [
                'gclid' => [
                    'filter' => 513,
                    'setCookieOptions' => [
                        'domain' => '',
                        'expire' => strtotime('+1 month'),
                        'httpOnly' => false,
                        'path' => '',
                        'secure' => false,
                    ],
                ],
                'utm_campaign' => [
                    'filter' => 513,
                    'setCookieOptions' => [
                        'domain' => '',
                        'expire' => strtotime('+1 month'),
                        'httpOnly' => false,
                        'path' => '',
                        'secure' => false,
                    ],
                ],
                'utm_medium' => [
                    'filter' => 513,
                    'setCookieOptions' => [
                        'domain' => '',
                        'expire' => strtotime('+1 month'),
                        'httpOnly' => false,
                        'path' => '',
                        'secure' => false,
                    ],
                ],
                'utm_source' => [
                    'filter' => 513,
                    'setCookieOptions' => [
                        'domain' => '',
                        'expire' => strtotime('+1 month'),
                        'httpOnly' => false,
                        'path' => '',
                        'secure' => false,
                    ],
                ],
            ],
        ],
    ],
]);

if ($GLOBALS['TYPO3_CONF_VARS']['EXTCONF'] == null) $GLOBALS['TYPO3_CONF_VARS']['EXTCONF'] = [];
\TYPO3\CMS\Core\Utility\ArrayUtility::mergeRecursiveWithOverrule($GLOBALS['TYPO3_CONF_VARS']['EXTCONF'], [
    'talisman_template' => [
        'pageNotFoundHandler' => [
            'error' => 10925,
            'forbidden' => null,
            'login' => null,
        ],
    ],
]);