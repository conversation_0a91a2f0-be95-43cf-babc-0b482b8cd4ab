<?php
namespace Keizer\KoningElvisConnector\Controller;

use Keizer\KoningElvisConnector\Utility\ConfigurationUtility;
use Keizer\KoningElvisConnector\Utility\PaginationUtility;
use TYPO3\CMS\Core\Messaging\AbstractMessage;
use TYPO3\CMS\Extbase\Utility\LocalizationUtility;

/**
 * Controller: Wizard
 *
 * @package Keizer\KoningElvisConnector\Controller
 */
class WizardController extends AbstractModuleController
{
    const SESSION_KEY = 'koning_elvis_connector_wizard';

    /**
     * @param string $rteId
     * @param string $rootFolder
     * @param bool $browseAssets
     * @param int $start
     * @param string $containerId
     * @return void
     */
    public function showAction($rteId, $rootFolder = '', $browseAssets = false, $start = 0, $containerId = '')
    {
        if (ConfigurationUtility::isValid()) {
            if ($this->connectionStatus) {
                if ($rootFolder === '') {
                    $rootFolder = $this->session->getSessionContents($this->sessionKey)['rootFolder'];
                }

                $browseFolderResults = $this->elvisService->browseFolders(($rootFolder === 'root') ? '' : $rootFolder);
                $this->saveFoldersInSession($browseFolderResults, $rootFolder, $containerId);

                $assets = [
                    'results' => [],
                    'pagination' => []
                ];

                if ($browseAssets) {
                    $assetsResult = $this->elvisService->searchAssets(
                        '',
                        $rootFolder,
                        $start,
                        $this->configuration['assetsPerPage'],
                        ['text'],
                        $containerId
                    );

                    $assets['results'] = $assetsResult['results'];
                    $assets['pagination'] = PaginationUtility::getAssetsPagination(
                        $assetsResult['count'],
                        (int) $this->configuration['assetsPerPage'],
                        $start
                    );
                }

                $this->view->assignMultiple([
                    'session' => $this->session->getSessionContents($this->sessionKey),
                    'assets' => $assets,
                    'elvisDamBaseUrl' => parse_url($this->configuration['baseUrl'])['host'],
                    'start' => $start,
                    'rteId' => $rteId
                ]);
            } else {
                $this->addFlashMessage(
                    LocalizationUtility::translate(
                        'flash_message.connection_error',
                        'KoningElvisConnector'
                    ),
                    '',
                    AbstractMessage::ERROR
                );
            }
        } else {
            $this->addFlashMessage(
                LocalizationUtility::translate(
                    'flash_message.configuration_error',
                    'KoningElvisConnector'
                ),
                '',
                AbstractMessage::ERROR
            );
        }
    }

    /**
     * @param array $assets
     * @param string $rteId
     * @return void
     */
    public function downloadAssetsAction($assets, $rteId)
    {
        $content = '';
        foreach ($assets as $asset) {
            $explodedAsset = explode('|', $asset);
            $content .= strip_tags($this->elvisService->getAssetContent($explodedAsset[0]));
        }

        $this->view->assignMultiple([
            'content' => $content,
            'rteId' => $rteId
        ]);
    }
}
