<?php
namespace Redkiwi\TalismanQuoteForm\Service;

/**
 * Service: Destination
 *
 * @package Redkiwi\TalismanQuoteForm\Service
 */
class DestinationService
{
    /**
     * @param int $rootPage
     * @return array
     */
    public function getDestinations($rootPage)
    {
        $returnArray = [];
        $continents = $this->getDatabaseConnection()->exec_SELECTgetRows(
            'uid, title',
            'pages',
            'pid = ' . (int) $rootPage . ' AND hidden = 0 AND deleted = 0 AND nav_hide = 0'
        );

        foreach ($continents as $continent) {
            $returnArray[$continent['uid']] = [
                'label' => $continent['title'],
                'children' => []
            ];

            $countries = $this->getDatabaseConnection()->exec_SELECTgetRows(
                'uid, title, nav_title',
                'pages',
                'pid = ' . (int) $continent['uid'] . ' AND hidden = 0 AND deleted = 0 AND nav_hide = 0'
            );

            foreach ($countries as $country) {
                if (!empty($country['nav_title'])) {
                    $returnArray[$continent['uid']]['children'][$country['uid']] = $country['nav_title'];
                } else {
                    $returnArray[$continent['uid']]['children'][$country['uid']] = $country['title'];
                }
            }
        }
        return $returnArray;
    }

    /**
     * @return \TYPO3\CMS\Core\Database\DatabaseConnection
     */
    protected function getDatabaseConnection()
    {
        return $GLOBALS['TYPO3_DB'];
    }
}
