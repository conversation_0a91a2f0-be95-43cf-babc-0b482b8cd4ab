<?php
namespace Redkiwi\TalismanTiles\Domain\Repository;

/**
 * Repository: Tiles
 *
 * @package Redkiwi\TalismanTiles\Domain\Repository
 */
class TileRepository extends \TYPO3\CMS\Extbase\Persistence\Repository
{
    /** @var array */
    protected $defaultOrderings = [
        'sorting' => \TYPO3\CMS\Extbase\Persistence\QueryInterface::ORDER_ASCENDING
    ];

    /**
     * @return void
     */
    public function initializeObject()
    {
        $querySettings = \TYPO3\CMS\Core\Utility\GeneralUtility::makeInstance(\TYPO3\CMS\Extbase\Persistence\Generic\Typo3QuerySettings::class);
        /** @var \TYPO3\CMS\Extbase\Persistence\Generic\Typo3QuerySettings $querySettings */
        $querySettings->setRespectStoragePage(false);
        $this->setDefaultQuerySettings($querySettings);
    }
}
