<html xmlns:f="http://typo3.org/ns/TYPO3/CMS/Fluid/ViewHelpers"
    xmlns:c="http://typo3.org/ns/Redkiwi/TalismanContent/ViewHelpers"
    xmlns:likes="http://typo3.org/ns/Redkiwi/TalismanLikes/ViewHelpers"
    data-namespace-typo3-fluid="true">
<div id="c{data.uid}" class="talisman-content-highlighted with-map {f:if(condition: '{data.layout} == 103', then: 'full-width')} {f:if(condition: '{data.tx_talismancontent_block_margin} != 0', then: 'block-margin-{data.tx_talismancontent_block_margin}')}">
    <f:if condition="{data.header}">
        <h2>{data.header -> f:format.raw()}</h2>
    </f:if>
    <f:if condition="{files}">
        <div class="row">
            <f:for each="{files}" as="images">
                <f:alias map="{file: images.0, hover: images.1}">

                    <div class="col-xs-12 {f:if(condition: '{data.layout} == 103', then: 'col-sm-12', else: 'col-sm-6')}">
                        <likes:likeableImage reference="{file}">
                            <a href="{f:uri.typolink(parameter: file.properties.link)}">
                                <figure>
                                    <f:image
                                        image="{file}"
                                        alt="{file.properties.alt}"
                                        title="{file.properties.title}"
                                        width="{f:if(condition: '{data.layout} == 103', then: '750', else: '555')}"
                                        height="{f:if(condition: '{data.layout} == 103', then: '430', else: '370c')}"
                                        class="img-responsive"/>

                                    <f:if condition="{file.properties.title}">
                                        <figcaption>
                                            <h2>{file.properties.title -> f:format.nl2br()}</h2>
                                            <f:if condition="{file.properties.alternative}">
                                                <span class="alt">
                                                    <f:format.nl2br>{file.properties.alternative}</f:format.nl2br>
                                                </span>
                                            </f:if>
                                            <f:if condition="{file.properties.description}">
                                                <span>
                                                    <f:format.nl2br><c:format.exampleTripPrice description="{file.properties.description}" promotionalPackage="{file.properties.talisman_content_promotional_package}" /></f:format.nl2br>
                                                </span>
                                            </f:if>
                                        </figcaption>
                                    </f:if>

                                    <f:image
                                        image="{hover}"
                                        alt="{hover.properties.alt}"
                                        title="{hover.properties.title}"
                                        width="{f:if(condition: '{data.layout} == 103', then: '750', else: '555')}"
                                        height="{f:if(condition: '{data.layout} == 103', then: '430', else: '370c')}"
                                        class="img-responsive hover"/>

                                    <div class="gradient"></div>

                                    <div class="image-actions">
                                        <div class="action">
                                            {like -> f:format.raw()}
                                        </div>
                                    </div>
                                </figure>
                            </a>
                        </likes:likeableImage>
                    </div>
                </f:alias>

            </f:for>
        </div>
    </f:if>
</div>

</html>
