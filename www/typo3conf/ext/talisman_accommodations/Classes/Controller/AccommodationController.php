<?php
namespace <PERSON><PERSON><PERSON>\TalismanAccommodations\Controller;

use Red<PERSON><PERSON>\TalismanTemplate\Domain\Repository\CategoryRepository;
use Redkiwi\TalismanPage\Domain\Model\Page;
use Redkiwi\TalismanPage\Domain\Repository\PageRepository;
use TYPO3\CMS\Extbase\Mvc\Controller\ActionController;
use TYPO3\CMS\Extbase\Persistence\Exception\InvalidQueryException;
use TYPO3\CMS\Core\LinkHandling\LinkService;

/**
 * Controller: Accommodation
 *
 * @package Redkiwi\TalismanAccommodations\Controller
 */
class AccommodationController extends ActionController
{
    /** @var PageRepository */
    protected $pageRepository;

    /** @var CategoryRepository */
    protected $categoryRepository;

    /** @var LinkService */
    protected $linkService;


    /**
     * AccommodationController constructor.
     * @param PageRepository $pageRepository
     * @param CategoryRepository $categoryRepository
     */
    public function __construct(PageRepository $pageRepository, CategoryRepository $categoryRepository, LinkService $linkService)
    {
        $this->pageRepository = $pageRepository;
        $this->categoryRepository = $categoryRepository;
        $this->linkService = $linkService;
        parent::__construct();
    }

    /**
     * @return void
     */
    public function listAction()
    {
        $pid = $GLOBALS['TSFE']->id;

        if (isset($this->settings['data']['startPoint'])) {
            $resolved =  $this->linkService->resolve($this->settings['data']['startPoint']);
            
            $pid = $resolved['pageuid'] ?: null;
        }

        if (!isset($this->settings['data']['limit'])) {
            $limit = 24;
        } else {
            $limit = $this->settings['data']['limit'];
        }

        $categories = $this->categoryRepository->findCategoryUsageInAccommodationList((int) $pid, $this->settings['parentCategory'], Page::DOKTYPE_ACCOMMODATION);
        $totalAccommodations = $this->pageRepository->countByTypePidAndHideInList(Page::DOKTYPE_ACCOMMODATION, $pid, false);

        $this->view->assignMultiple([
            'categories' => $categories,
            'totalAccommodations' => $totalAccommodations,
            'currentPage' => $GLOBALS['TSFE']->id,
            'limit' => $limit,
            'pid' => $pid,
        ]);
    }

    /**
     * @param int $pid
     * @param int $offset
     * @param int $limit
     * @param array $categories
     * @return string
     * @throws InvalidQueryException
     */
    public function resultsAction(int $pid, int $offset, int $limit, array $categories = [])
    {
        $totalAccommodationsForCurrentFilter = $this->pageRepository->countByTypeAndPidAndHideInListAndCategories(Page::DOKTYPE_ACCOMMODATION, $pid, false, $categories);
        $accommodations = $this->pageRepository->findByTypeAndPidAndHideInListAndCategories(Page::DOKTYPE_ACCOMMODATION, $pid, false, $categories, $offset, $limit);

        $newOffset = $offset + $limit;
        if ($newOffset > $totalAccommodationsForCurrentFilter) {
            $newOffset = $totalAccommodationsForCurrentFilter;
        }

        $content = $this->view
            ->assignMultiple([
                'accommodations' => $accommodations,
                'offset' => $newOffset
            ])
            ->render();

        return json_encode([
            'totalResults' => $totalAccommodationsForCurrentFilter,
            'offset' => $newOffset,
            'content' => $content
        ]);
    }
}
