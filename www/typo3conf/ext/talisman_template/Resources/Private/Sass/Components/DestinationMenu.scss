.dropdown-menu.destination-menu {
    width: 100%;
    background: $dropdown-menu-background;
    border: none;
    box-shadow: none;
    border-bottom: 1px solid #e5e5e5;
    padding: 14px 0 19px 0;
    margin-top: 0;
    border-top-right-radius: 0;
    border-top-left-radius: 0;


    @media (min-width: $screen-sm-min) {
        padding: 54px 0 69px 0;
    }

    &.active {
        display: block;
    }

    .container {
        position: relative;

        .col-md-12 {
            position: absolute;
            right: 16.667%;
            top: 355px;
        }

        .col-md-offset-8 {
            //margin-top: 0px;

            .btn {

                &.btn-default {

                    &::after {
                        background-position: -72px -168px;
                    }
                }
            }
        }
    }

    ul {
        li {
            height: auto!important;

            span {
                @include custom-font-family('Nunito', 600);
                color: $footer-header-link;
                font-size: 14px;
                text-transform: uppercase;
            }

            ul {
                margin-top: 5px;
                margin-bottom: 38px;

                li {
                    a {
                        @include custom-font-family('Nunito', 400);
                        font-size: 14px;
                        color: $black;
                        text-transform: none;
                        line-height: 25px;

                        &:hover, &:active, &:focus {
                            color: $black;
                            text-decoration: none;
                        }
                    }
                }
            }
        }
    }
}


//
// Add hovers to lg
//
@media (min-width: $screen-lg-min) {

    .dropdown-menu.destination-menu {
        ul {
            li {
                ul {
                    li {
                        a {
                            &:hover, &:active, &:focus {
                                text-decoration: underline;
                            }
                        }
                    }
                }
            }
        }
    }
}
