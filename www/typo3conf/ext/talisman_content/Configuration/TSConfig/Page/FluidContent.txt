mod.wizards.newContentElement.wizardItems.extra {
    header = LLL:EXT:talisman_content/Resources/Private/Language/locallang_be.xlf:talisman_content
    elements {
        talismancontent_highlightedwithmap {
            icon = EXT:talisman_content/Resources/Public/Icons/highlighted-with-map.jpg
            title = LLL:EXT:talisman_content/Resources/Private/Language/locallang_be.xlf:highlightedwithmap.title
            description = LLL:EXT:talisman_content/Resources/Private/Language/locallang_be.xlf:highlightedwithmap.description
            tt_content_defValues {
                CType = talismancontent_highlightedwithmap
            }
        }

        talismancontent_highlightedwithzoom {
            icon = EXT:talisman_content/Resources/Public/Icons/highlighted-with-zoom.jpg
            title = LLL:EXT:talisman_content/Resources/Private/Language/locallang_be.xlf:highlightedwithzoom.title
            description = LLL:EXT:talisman_content/Resources/Private/Language/locallang_be.xlf:highlightedwithzoom.description
            tt_content_defValues {
                CType = talismancontent_highlightedwithzoom
            }
        }

        talismancontent_quotewidget {
            icon = EXT:talisman_content/Resources/Public/Icons/quote-widget.jpg
            title = LLL:EXT:talisman_content/Resources/Private/Language/locallang_be.xlf:quote_widget.title
            description = LLL:EXT:talisman_content/Resources/Private/Language/locallang_be.xlf:quote_widget.description
            tt_content_defValues {
                CType = talismancontent_quotewidget
            }
        }

        talismancontent_circledheader {
            icon = EXT:talisman_content/Resources/Public/Icons/circled-header.jpg
            title = LLL:EXT:talisman_content/Resources/Private/Language/locallang_be.xlf:circled_header.title
            description = LLL:EXT:talisman_content/Resources/Private/Language/locallang_be.xlf:circled_header.description
            tt_content_defValues {
                CType = talismancontent_circledheader
            }
        }

        talismancontent_squaredheader {
            icon = EXT:talisman_content/Resources/Public/Icons/circled-header.jpg
            title = LLL:EXT:talisman_content/Resources/Private/Language/locallang_be.xlf:squared_header.title
            description = LLL:EXT:talisman_content/Resources/Private/Language/locallang_be.xlf:squared_header.description
            tt_content_defValues {
                CType = talismancontent_squaredheader
            }
        }

        talismancontent_banner {
            icon = EXT:talisman_content/Resources/Public/Icons/banner.jpg
            title = LLL:EXT:talisman_content/Resources/Private/Language/locallang_be.xlf:banner.title
            description = LLL:EXT:talisman_content/Resources/Private/Language/locallang_be.xlf:banner.description
            tt_content_defValues {
                CType = talismancontent_banner
            }
        }

        talismancontent_quote {
            icon = EXT:talisman_content/Resources/Public/Icons/quote.jpg
            title = LLL:EXT:talisman_content/Resources/Private/Language/locallang_be.xlf:quote.title
            description = LLL:EXT:talisman_content/Resources/Private/Language/locallang_be.xlf:quote.description
            tt_content_defValues {
                CType = talismancontent_quote
            }
        }

        talismancontent_button {
            icon = EXT:talisman_content/Resources/Public/Icons/button.jpg
            title = LLL:EXT:talisman_content/Resources/Private/Language/locallang_be.xlf:button.title
            description = LLL:EXT:talisman_content/Resources/Private/Language/locallang_be.xlf:button.description
            tt_content_defValues {
                CType = talismancontent_button
            }
        }

        talismancontent_circlemenu {
            icon = EXT:talisman_content/Resources/Public/Icons/circle-menu.jpg
            title = LLL:EXT:talisman_content/Resources/Private/Language/locallang_be.xlf:circle_menu.title
            description = LLL:EXT:talisman_content/Resources/Private/Language/locallang_be.xlf:circle_menu.description
            tt_content_defValues {
                CType = talismancontent_circlemenu
            }
        }

        talismancontent_linkwidget {
            icon = EXT:talisman_content/Resources/Public/Icons/link-widget.jpg
            title = LLL:EXT:talisman_content/Resources/Private/Language/locallang_be.xlf:link_widget.title
            description = LLL:EXT:talisman_content/Resources/Private/Language/locallang_be.xlf:link_widget.description
            tt_content_defValues {
                CType = talismancontent_linkwidget
            }
        }

        talismancontent_videowidget {
            icon = EXT:talisman_content/Resources/Public/Icons/video-widget.jpg
            title = LLL:EXT:talisman_content/Resources/Private/Language/locallang_be.xlf:video_widget.title
            description = LLL:EXT:talisman_content/Resources/Private/Language/locallang_be.xlf:video_widget.description
            tt_content_defValues {
                CType = talismancontent_videowidget
            }
        }

        talismancontent_audiowidget {
            icon = EXT:talisman_content/Resources/Public/Icons/audio-widget.jpg
            title = LLL:EXT:talisman_content/Resources/Private/Language/locallang_be.xlf:audio_widget.title
            description = LLL:EXT:talisman_content/Resources/Private/Language/locallang_be.xlf:audio_widget.description
            tt_content_defValues {
                CType = talismancontent_audiowidget
            }
        }

        talismancontent_streetviewwidget {
            icon = EXT:talisman_content/Resources/Public/Icons/streetview-widget.jpg
            title = LLL:EXT:talisman_content/Resources/Private/Language/locallang_be.xlf:streetview_widget.title
            description = LLL:EXT:talisman_content/Resources/Private/Language/locallang_be.xlf:streetview_widget.description
            tt_content_defValues {
                CType = talismancontent_streetviewwidget
            }
        }

        talismancontent_carousel {
            icon = EXT:talisman_content/Resources/Public/Icons/carousel.jpg
            title = LLL:EXT:talisman_content/Resources/Private/Language/locallang_be.xlf:carousel.title
            description = LLL:EXT:talisman_content/Resources/Private/Language/locallang_be.xlf:carousel.description
            tt_content_defValues {
                CType = talismancontent_carousel
            }
        }

        talismancontent_contactwidget {
            icon = EXT:talisman_content/Resources/Public/Icons/contact-widget.jpg
            title = LLL:EXT:talisman_content/Resources/Private/Language/locallang_be.xlf:contact_widget.title
            description = LLL:EXT:talisman_content/Resources/Private/Language/locallang_be.xlf:contact_widget.description
            tt_content_defValues {
                CType = talismancontent_contactwidget
            }
        }

        talismancontent_videolightbox {
            title = LLL:EXT:talisman_content/Resources/Private/Language/locallang_be.xlf:video_lightbox.title
            description = LLL:EXT:talisman_content/Resources/Private/Language/locallang_be.xlf:video_lightbox.description
            tt_content_defValues {
                CType = talismancontent_videolightbox
            }
        }

        talismancontent_slider {
            icon = EXT:talisman_content/Resources/Public/Icons/carousel.jpg
            title = LLL:EXT:talisman_content/Resources/Private/Language/locallang_be.xlf:tt_content.slider.title
            description = LLL:EXT:talisman_content/Resources/Private/Language/locallang_be.xlf:tt_content.slider.description
            tt_content_defValues {
                CType = talismancontent_slider
            }
        }
    }
    show = *
}
