<?php
namespace Redkiwi\TalismanInspiration\Usage;

use ApacheSolrForTypo3\Solr\ConnectionManager;
use ApacheSolrForTypo3\Solr\System\Solr\SolrConnection;
use TYPO3\CMS\Core\Utility\GeneralUtility;

/**
 * Trait: Solr layer for inspiration index
 *
 * @package Redkiwi\TalismanInspiration\Usage
 */
trait InspirationSolr
{
    /**
     * Get SolrService
     *
     * @return SolrConnection
     * @throws \Exception
     */
    protected function getSolrConnection()
    {
        /** @var SolrConnection $solrConnection */
        static $solrConnection;

        if ($solrConnection === null) {

            $rootPageUid = 1;
            $sys_language_uid = 0;

            /** @var SolrConnection $solrConnection */
            $solrConnection = GeneralUtility::makeInstance(ConnectionManager::class)
                ->getConnectionByRootPageId($rootPageUid, $sys_language_uid);

            if (!$solrConnection->getReadService()->ping()) {
                throw new \Exception('Solr service not responding.', 1479479233796);
            }
        }
        return $solrConnection;
    }
}
