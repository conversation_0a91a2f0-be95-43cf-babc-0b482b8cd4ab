<types>
	<!--
		Field type definitions.

		The "name" attribute is just a label to be used by field definitions.
		The "class" attribute and any other attributes determine the real
		behavior of the fieldType.
			Class names starting with "solr" refer to java classes in the
		org.apache.solr.analysis package.

	-->

	<!-- The StrField type is not analyzed, but indexed/stored verbatim.
		- StrField and TextField support an optional compressThreshold which
		limits compression (if enabled in the derived fields) to values which
		exceed a certain size (in characters).
	-->
	<fieldType name="string"  class="solr.StrField"  sortMissingLast="true" omitNorms="true" useDocValuesAsStored="false" />

	<!-- boolean type: "true" or "false" -->
	<fieldType name="boolean" class="solr.BoolField" sortMissingLast="true" omitNorms="true"/>
	<!--Binary data type. The data should be sent/retrieved in as Base64 encoded Strings -->
	<fieldType name="binary" class="solr.BinaryField"/>

	<!-- The optional sortMissingLast and sortMissingFirst attributes are
			currently supported on types that are sorted internally as strings.
		- If sortMissingLast="true", then a sort on this field will cause documents
			without the field to come after documents with the field,
			regardless of the requested sort order (asc or desc).
		- If sortMissingFirst="true", then a sort on this field will cause documents
			without the field to come before documents with the field,
			regardless of the requested sort order.
		- If sortMissingLast="false" and sortMissingFirst="false" (the default),
			then default lucene sorting will be used which places docs without the
			field first in an ascending sort and last in a descending sort.
	-->


	<!--
		Default numeric field types. For faster range queries, consider the tint/tfloat/tlong/tdouble types.
	-->
	<fieldType name="integer" class="solr.TrieIntField"    precisionStep="0" positionIncrementGap="0" omitNorms="true" useDocValuesAsStored="false" />
	<fieldType name="float"   class="solr.TrieFloatField"  precisionStep="0" positionIncrementGap="0" omitNorms="true" useDocValuesAsStored="false" />
	<fieldType name="long"    class="solr.TrieLongField"   precisionStep="0" positionIncrementGap="0" omitNorms="true" useDocValuesAsStored="false" />
	<fieldType name="double"  class="solr.TrieDoubleField" precisionStep="0" positionIncrementGap="0" omitNorms="true" useDocValuesAsStored="false" />

	<!--
		Numeric field types that index each value at various levels of precision
		to accelerate range queries when the number of values between the range
		endpoints is large. See the javadoc for NumericRangeQuery for internal
		implementation details.

		Smaller precisionStep values (specified in bits) will lead to more tokens
		indexed per value, slightly larger index size, and faster range queries.
		A precisionStep of 0 disables indexing at different precision levels.
	-->
	<fieldType name="tint"     class="solr.TrieIntField"    precisionStep="8" positionIncrementGap="0" omitNorms="true" indexed="true" stored="false" useDocValuesAsStored="false" />
	<fieldType name="tfloat"   class="solr.TrieFloatField"  precisionStep="8" positionIncrementGap="0" omitNorms="true" indexed="true" stored="false" useDocValuesAsStored="false" />
	<fieldType name="tlong"    class="solr.TrieLongField"   precisionStep="8" positionIncrementGap="0" omitNorms="true" indexed="true" stored="false" useDocValuesAsStored="false" />
	<fieldType name="tdouble"  class="solr.TrieDoubleField" precisionStep="8" positionIncrementGap="0" omitNorms="true" indexed="true" stored="false" useDocValuesAsStored="false" />

	<fieldType name="tdouble4" class="solr.TrieDoubleField" precisionStep="4" positionIncrementGap="0" omitNorms="true" indexed="true" stored="false" useDocValuesAsStored="false" />

	<!--
		The format for this date field is of the form 1995-12-31T23:59:59Z, and
		is a more restricted form of the canonical representation of dateTime
		http://www.w3.org/TR/xmlschema-2/#dateTime
		The trailing "Z" designates UTC time and is mandatory.
		Optional fractional seconds are allowed: 1995-12-31T23:59:59.999Z
		All other components are mandatory.

		Expressions can also be used to denote calculations that should be
		performed relative to "NOW" to determine the value, ie...

		NOW/HOUR
			... Round to the start of the current hour
		NOW-1DAY
			... Exactly 1 day prior to now
		NOW/DAY+6MONTHS+3DAYS
			... 6 months and 3 days in the future from the start of
				the current day

		Consult the DateField javadocs for more information.

		Note: For faster range queries, consider the tdate type
	-->
	<fieldType name="date" class="solr.TrieDateField" precisionStep="0" positionIncrementGap="0" sortMissingLast="true" omitNorms="true" useDocValuesAsStored="false" />

	<!-- A Trie based date field for faster date range queries and date faceting. -->
	<fieldType name="tdate" class="solr.TrieDateField" precisionStep="6" positionIncrementGap="0" omitNorms="true" />


	<!-- solr.TextField allows the specification of custom text analyzers
		specified as a tokenizer and a list of token filters. Different
		analyzers may be specified for indexing and querying.

		The optional positionIncrementGap puts space between multiple fields of
		this type on the same document, with the purpose of preventing false phrase
		matching across fields.

		For more info on customizing your analyzer chain, please see
		http://wiki.apache.org/solr/AnalyzersTokenizersTokenFilters
	-->

	<!-- One can also specify an existing Analyzer class that has a
		default constructor via the class attribute on the analyzer element
	<fieldType name="text_greek" class="solr.TextField">
		<analyzer class="org.apache.lucene.analysis.el.GreekAnalyzer"/>
	</fieldType>
	-->

	<!-- This is an example of using the KeywordTokenizer along
		With various TokenFilterFactories to produce a sortable field
		that does not include some properties of the source text
	-->
	<fieldType name="textSort" class="solr.TextField" sortMissingLast="true" omitNorms="true">
		<analyzer>
			<!-- KeywordTokenizer does no actual tokenizing, so the entire
				input string is preserved as a single token
			-->
			<tokenizer class="solr.KeywordTokenizerFactory"/>

			<!-- The LowerCase TokenFilter does what you expect, which can be
				when you want your sorting to be case insensitive
			-->
			<!-- <filter class="solr.LowerCaseFilterFactory" /> -->
			<!-- The TrimFilter removes any leading or trailing whitespace -->
			<filter class="solr.TrimFilterFactory" />
			<!-- The PatternReplaceFilter gives you the flexibility to use
				Java Regular expression to replace any sequence of characters
				matching a pattern with an arbitrary replacement string,
				which may include back refrences to portions of the orriginal
				string matched by the pattern.

				See the Java Regular Expression documentation for more
				infomation on pattern and replacement string syntax.

				http://java.sun.com/j2se/1.5.0/docs/api/java/util/regex/package-summary.html

			<filter class="solr.PatternReplaceFilterFactory"
					pattern="(^\p{Punct}+)" replacement="" replace="all"
			/>
			-->
		</analyzer>
	</fieldType>

	<!--
		A text field that only splits on whitespace for exact matching of words
	-->
	<fieldType name="textWhiteSpaceTokenized" class="solr.TextField" positionIncrementGap="100">
		<analyzer>
			<tokenizer class="solr.WhitespaceTokenizerFactory"/>

			<filter class="solr.LowerCaseFilterFactory"/>
			<filter class="solr.RemoveDuplicatesTokenFilterFactory"/>
		</analyzer>
	</fieldType>

	<fieldType name="textPath" class="solr.TextField" positionIncrementGap="100">
		<analyzer type="index">
			<tokenizer class="solr.PathHierarchyTokenizerFactory"/>
		</analyzer>
	</fieldType>


	<fieldType name="phonetic" stored="false" indexed="true" class="solr.TextField" >
		<analyzer>
			<tokenizer class="solr.StandardTokenizerFactory"/>

			<filter class="solr.DoubleMetaphoneFilterFactory" inject="false"/>
		</analyzer>
	</fieldType>

	<!--
		The "RandomSortField" is not used to store or search any
		data.	You can declare fields of this type in your schema
		to generate psuedo-random orderings of your docs for sorting
		purposes.	The ordering is generated based on the field name
		and the version of the index, As long as the index version
		remains unchanged, and the same field name is reused,
		the ordering of the docs will be consistent.
		If you want differend psuedo-random orderings of documents,
		for the same version of the index, use a dynamicField and
		change the name
	-->
	<fieldType name="random" class="solr.RandomSortField" indexed="true" />

	<!--
		This point type indexes the coordinates as separate fields (subFields)

		Example: with subFieldSuffix="_doubleS" the coordinates will be indexed
			in fields myLocation_0_doubleS,myLocation_1_doubleS
	 -->
	<fieldType name="point" class="solr.PointType" dimension="2" subFieldSuffix="_doubleS" />

	<!-- A specialized field for geospatial search. If indexed, this fieldType must not be multivalued. -->
	<fieldType name="location" class="solr.LatLonType" subFieldSuffix="_coordinate" />

	<!--
		An alternative geospatial field type. It supports multiValued and polygon shapes.
		For more information about this see http://wiki.apache.org/solr/SolrAdaptersForLuceneSpatial4
	-->
	<fieldType name="locationRpt" class="solr.SpatialRecursivePrefixTreeFieldType"
			   geo="true" distErrPct="0.025" maxDistErr="0.000009" distanceUnits="degrees"  />

	<fieldType name="currency" class="solr.CurrencyField" precisionStep="8" defaultCurrency="USD" currencyConfig="currency.xml" />

    <fieldType name="text_general" class="solr.TextField" positionIncrementGap="100" multiValued="true">
      <analyzer type="index">
        <tokenizer class="solr.StandardTokenizerFactory"/>
        <!-- in this example, we will only use synonyms at query time
        <filter class="solr.SynonymFilterFactory" synonyms="index_synonyms.txt" ignoreCase="true" expand="false"/>
        -->
        <filter class="solr.LowerCaseFilterFactory"/>
      </analyzer>
      <analyzer type="query">
        <tokenizer class="solr.StandardTokenizerFactory"/>
        <filter class="solr.LowerCaseFilterFactory"/>
      </analyzer>
    </fieldType>


	<!-- since fields of this type are by default not stored or indexed, any data added to
		them will be ignored outright
	-->
	<fieldType name="ignored" stored="false" indexed="false" class="solr.StrField" />

</types>
