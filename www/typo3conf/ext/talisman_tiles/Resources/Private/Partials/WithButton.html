<f:link.typolink parameter="{tile.link}" class="cards with-button">
    <figure>
        <f:if condition="{tile.visual.uid}">
            <f:image src="{f:uri.image(image: tile.visual)}" alt="" height="320c" width="560"/>
        </f:if>
        <figcaption>
            <h2>{tile.title}</h2>
            <div class="col-sm-12 col-md-5 col-md-offset-7">
                <h3>{tile.subtitle}</h3>
                <span class="btn btn-default btn-rubine">{tile.buttonText}
            </div>
        </figcaption>
    </figure>
</f:link.typolink>