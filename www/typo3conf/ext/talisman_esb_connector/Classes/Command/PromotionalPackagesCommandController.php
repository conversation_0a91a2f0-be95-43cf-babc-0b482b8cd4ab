<?php
namespace Redkiwi\TalismanEsbConnector\Command;

use Redkiwi\TalismanEsbConnector\Utility\ConfigurationUtility;

/**
 * Command: Promotional packages
 *
 * @package Redkiwi\TalismanEsbConnector\Command
 */
class PromotionalPackagesCommandController extends \TYPO3\CMS\Extbase\Mvc\Controller\CommandController
{
    /**
     * @var \Redkiwi\TalismanEsbConnector\Service\Esb\PromotionalPackageService
     * @inject
     */
    protected $esbPromotionalPackageService;

    /**
     * @var \Redkiwi\TalismanEsbConnector\Service\TYPO3\PromotionalPackageService
     * @inject
     */
    protected $typo3PromotionalPackageService;

    /**
     * @return bool
     * @throws \Exception
     */
    public function syncCommand()
    {
        if (ConfigurationUtility::isValid()) {
            $configuration = ConfigurationUtility::getEsbSettings();
            $countries = $this->esbPromotionalPackageService->makeRequest([]);
            $this->typo3PromotionalPackageService->sync($countries, $configuration['promotionalPackageStoragePageUid']);
            return true;
        } else {
            throw new \Exception('Configuration invalid. Check Extension Manager');
        }
    }
}
