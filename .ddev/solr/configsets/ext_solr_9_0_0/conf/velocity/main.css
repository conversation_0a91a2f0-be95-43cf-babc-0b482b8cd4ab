#admin{
  text-align: right;
  vertical-align: top; 
}

#head{
  width: 100%;
}
.array-field {
  border: 2px solid #474747;
  background: #FFE9D8;
  padding: 5px;
  margin: 5px;
}

.array-field-list li {
  list-style: circle;
  margin-left: 20px;
}

body {
  font-family: Helvetica, Arial, sans-serif;
  font-size: 10pt;
}

a {
  color: #43a4b1;
}

.navigators {
  float: left;
  margin: 5px;
  margin-top: 0px;
  width: 185px;
  padding: 5px;
  position: relative;  
}

.navigators h2 {
  background: #FEC293;
  border: 1px solid #ce9d77;
  padding: 5px;
}

.navigators ul {
  list-style: none;
  margin: 0;
  margin-bottom: 5px;
  margin-top: 5px;
  padding-left: 10px;
}

.navigators ul li {
  color: #999;
  padding: 2px;
}



.facet-field {
  font-weight: bold;
}

.highlight {
  color: white;
  background-color: gray;
  border: 1px black solid;
}

.highlight-box {
  margin-left: 15px;
}

.field-name {
  font-weight: bold;
}

.highlighted-facet-field {
  background: white;
}

.constraints {
  margin-top: 10px;
}

#query-form{
  width: 80%;
}



.query-box, .constraints {
  padding: 5px;
  margin: 5px;
  font-weight: normal;
  font-size: 24px;
  letter-spacing: 0.08em;
}

.query-box #q {
  margin-left: 8px;
  width: 60%;
  height: 50px;
  border: 1px solid #999;
  font-size: 1em;
  padding: 0.4em;
}

.query-box {
  
}

.query-boost {
  
  top: 10px;
  left: 50px;
  position: relative;
  font-size: 0.8em;
}

.query-box .inputs{
  left: 180px;
  position: relative;
  
}

#logo {
  margin: 10px;
  border-style: none;
}

.pagination {
  padding-left: 33%;
  background: #eee;
  margin: 5px;
  margin-left: 210px;
  padding-top: 5px;
  padding-bottom: 5px;
}

.result-document {
  border: 1px solid #999;
  padding: 5px;
  margin: 5px;
  margin-left: 210px;
  margin-bottom: 15px;
}

.result-document div{
  padding: 5px;
}

.result-title{
  width:60%;
}

.mlt{
  
}

.map{
  float: right;
  position: relative;
  top: -25px;  
}

.result-document:nth-child(2n+1) {
  background-color: #eee;
}


.selected-facet-field {
  font-weight: bold;
}

li.show {
  list-style: disc;
}

.group-value{
  font-weight: bold;
}