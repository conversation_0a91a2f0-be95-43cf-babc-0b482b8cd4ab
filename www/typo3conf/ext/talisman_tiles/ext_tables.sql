#
# Table structure for table 'tt_content'
#
CREATE TABLE tt_content (
    talismantiles_tiles int(11) DEFAULT '0' NOT NULL,
);

#
# Table structure for table 'tx_talismantiles_domain_model_tile'
#
CREATE TABLE tx_talismantiles_domain_model_tile (
    uid int(11) NOT NULL auto_increment,
    pid int(11) DEFAULT '0' NOT NULL,
    tstamp int(11) DEFAULT '0' NOT NULL,
    crdate int(11) DEFAULT '0' NOT NULL,
    cruser_id int(11) DEFAULT '0' NOT NULL,
    sorting int(10) DEFAULT '0' NOT NULL,
    editlock tinyint(4) DEFAULT '0' NOT NULL,
    deleted tinyint(4) DEFAULT '0' NOT NULL,
    hidden tinyint(4) DEFAULT '0' NOT NULL,
    starttime int(11) DEFAULT '0' NOT NULL,
    endtime int(11) DEFAULT '0' NOT NULL,
    content_id int(11) DEFAULT '0' NOT NULL,
    title_backend tinytext,
    type int(11) DEFAULT '0' NOT NULL,
    visual int(11) DEFAULT '0' NOT NULL,
    link text,
    title tinytext,
    subtitle text,
    price text,
    button_text text,

    PRIMARY KEY (uid),
    KEY parent (pid),
    KEY content_id (content_id)
);