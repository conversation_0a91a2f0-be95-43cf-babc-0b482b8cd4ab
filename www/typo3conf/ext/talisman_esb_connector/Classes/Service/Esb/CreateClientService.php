<?php

namespace Redkiwi\TalismanEsbConnector\Service\Esb;

/**
 * Service: Create client
 *
 * @package Redkiwi\TalismanEsbConnector\Service\Esb
 */
class CreateClientService extends AbstractConnectorService
{
    /**
     * @param array $parameters
     * @return array
     * @throws \Exception
     */
    public function makeRequest(array $parameters = [])
    {
        $createClientRequest = $this->request('data/createclient', $parameters);
        if ($createClientRequest['info']['http_code'] !== 200) {
            throw new \Exception('Request data/createclient failed with status code ' . $createClientRequest['info']['http_code']);
        }

        $response = new \SimpleXMLElement($createClientRequest['response']);

        return [
            'statusCode' => (int)$response->statusCode,
            'clientId' => (int)$response->additionalData->clientId
        ];
    }
}
