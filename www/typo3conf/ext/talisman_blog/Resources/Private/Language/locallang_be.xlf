<?xml version="1.0" encoding="UTF-8"?>
<xliff version="1.0">
    <file source-language="en" datatype="plaintext" original="messages" date="2012-10-17T19:30:32Z" product-name="talisman_blog">
        <header/>
        <body>
            <trans-unit id="plugin.list.title">
                <source>Blog list</source>
            </trans-unit>
            <trans-unit id="plugin.list.description">
                <source>A blog list plugin</source>
            </trans-unit>
            <trans-unit id="plugin.popular.title">
                <source>Blog most read</source>
            </trans-unit>
            <trans-unit id="plugin.popular.description">
                <source>A plugin showing the most read blog posts</source>
            </trans-unit>
            <trans-unit id="plugin.sub_menu.title">
                <source>Sub menu</source>
            </trans-unit>
            <trans-unit id="plugin.intro.title">
                <source>Blog intro</source>
            </trans-unit>
            <trans-unit id="plugin.intro.description">
                <source>An intro text for blog detail pages</source>
            </trans-unit>
            <trans-unit id="plugin.comments.title">
                <source>Blog comments</source>
            </trans-unit>
            <trans-unit id="plugin.comments.description">
                <source>A section that renders a comments area for blog posts</source>
            </trans-unit>
            <trans-unit id="plugin.related.title">
                <source>Blog related</source>
            </trans-unit>
            <trans-unit id="plugin.related.description">
                <source>A section that renders related blog posts</source>
            </trans-unit>
            <trans-unit id="plugin.share.title">
                <source>Share buttons</source>
            </trans-unit>
            <trans-unit id="plugin.share.description">
                <source>Share buttons for under a blog article</source>
            </trans-unit>

            <trans-unit id="configuration.list.start_point">
                <source>Blog storage directory ID</source>
            </trans-unit>
            <trans-unit id="configuration.list.limit">
                <source>Blog posts shown</source>
            </trans-unit>
            <trans-unit id="configuration.intro.first_letter">
                <source>First letter</source>
            </trans-unit>
            <trans-unit id="configuration.intro.intro">
                <source>Intro</source>
            </trans-unit>
            <trans-unit id="configuration.related.pages">
                <source>Related blogs</source>
            </trans-unit>

            <trans-unit id="pages.tx_talismanblog_tab">
                <source>Blog</source>
            </trans-unit>
            <trans-unit id="pages.tx_talismanblog_author">
                <source>Author</source>
            </trans-unit>
            <trans-unit id="pages.tx_talismanblog_country">
                <source>Country</source>
            </trans-unit>
        </body>
    </file>
</xliff>
