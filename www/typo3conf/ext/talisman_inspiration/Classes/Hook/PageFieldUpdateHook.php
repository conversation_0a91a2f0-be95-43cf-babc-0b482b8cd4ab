<?php

namespace Red<PERSON>wi\TalismanInspiration\Hook;

use TYPO3\CMS\Core\Database\ConnectionPool;
use TYPO3\CMS\Core\DataHandling\DataHandler;
use TYPO3\CMS\Core\Utility\GeneralUtility;

/**
 * Hook: Page field update
 *
 * @package Redkiwi\TalismanInspiration\Hook
 */
class PageFieldUpdateHook
{
    /**
     * @param string $status
     * @param string $table
     * @param int $id
     * @param array $fieldArray
     * @param DataHandler $dataHandler
     * @return void
     */
    public function processDatamap_afterDatabaseOperations($status, $table, $id, $fieldArray, DataHandler $dataHandler)
    {
        /** @var QueryBuilder $selectQueryBuilder */
        $selectQueryBuilder = $this->getDatabaseConnection()->getQueryBuilderForTable($table);

        if ($table === 'pages' && $status === 'new') {

            $page = $selectQueryBuilder
                ->select('tx_talismanpage_include_in_inspiration', 'tx_talismanpage_include_in_specials', 'hidden', 'deleted')
                ->from($table)
                ->where(
                    $selectQueryBuilder->expr()->eq('uid', $dataHandler->substNEWwithIDs[$id])
                )
                ->execute()
                ->fetch();

            if (((int)$page['tx_talismanpage_include_in_inspiration'] === 1 || (int)$page['tx_talismanpage_include_in_specials'] === 1) && (int)$page['hidden'] === 0 && (int)$page['deleted'] === 0) {

                $this->getDatabaseConnection()->getQueryBuilderForTable('tx_talismaninspiration_solr_queue')
                    ->insert('tx_talismaninspiration_solr_queue')
                    ->values([
                        'page_id' => (int)$dataHandler->substNEWwithIDs[$id],
                        'action' => 'update'
                    ])->execute();
            }
        } else if ($table === 'pages' && $status === 'update') {
            $page = $selectQueryBuilder->select('tx_talismanpage_include_in_inspiration', 'tx_talismanpage_include_in_specials', 'hidden', 'deleted')
                ->from($table)
                ->where(
                    $selectQueryBuilder->expr()->eq('uid', $id)
                )->execute()->fetch();

            if (((int)$page['tx_talismanpage_include_in_inspiration'] === 1 || (int)$page['tx_talismanpage_include_in_specials'] === 1) && (int)$page['hidden'] === 0 && (int)$page['deleted'] === 0) {
                $action = 'update';
            } else {
                $action = 'delete';
            }

            /** @var QueryBuilder $deleteQueryBuilder */
            $deleteQueryBuilder = $this->getDatabaseConnection()->getQueryBuilderForTable('tx_talismaninspiration_solr_queue');
            $deleteQueryBuilder
                ->delete('tx_talismaninspiration_solr_queue')
                ->where(
                    $deleteQueryBuilder->expr()->eq('page_id', (int)$id)
                )->execute();

            $this->getDatabaseConnection()->getQueryBuilderForTable('tx_talismaninspiration_solr_queue')
                ->insert('tx_talismaninspiration_solr_queue')
                ->values([
                    'page_id' => (int)$id,
                    'action' => $action
                ])->execute();

        }
    }

    /**
     * @param string $table
     * @param integer $id
     * @param array $recordToDelete
     * @return void
     */
    public function processCmdmap_deleteAction($table, $id, $recordToDelete)
    {
        if ($table == 'pages') {
            $queryTableName = 'tx_talismaninspiration_solr_queue';

            /** @var QueryBuilder $queryBuilder1 */
            $queryBuilder1 = $this->getDatabaseConnection()->getQueryBuilderForTable($queryTableName);
            $queryBuilder1->delete($queryTableName)->where(
                $queryBuilder1->expr()->eq('page_id', (int)$id)
            )->execute();

            if ((int)$recordToDelete['tx_talismanpage_include_in_inspiration'] === 1) {

                /** @var QueryBuilder $queryBuilder2 */
                $queryBuilder2 = $this->getDatabaseConnection()->getQueryBuilderForTable($queryTableName);
                $queryBuilder2->delete($queryTableName)->where(
                    $queryBuilder2->expr()->eq('page_id', (int)$id),
                    $queryBuilder2->expr()->eq('action', 'delete')
                )->execute();
            };
        }
    }

    /**
     * @return ConnectionPool
     */
    protected function getDatabaseConnection()
    {
        return GeneralUtility::makeInstance(ConnectionPool::class);
    }
}
