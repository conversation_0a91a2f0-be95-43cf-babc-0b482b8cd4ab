<?php
if (!defined('TYPO3_MODE')) {
    die('Access denied.');
}

\TYPO3\CMS\Extbase\Utility\ExtensionUtility::registerPlugin(
    'talisman_travelogues',
    'List',
    'LLL:EXT:talisman_travelogues/Resources/Private/Language/locallang_be.xlf:plugin.list.title',
    'EXT:talisman_travelogues/Resources/Public/Icons/list.jpg'
);

\TYPO3\CMS\Extbase\Utility\ExtensionUtility::registerPlugin(
    'talisman_travelogues',
    'Detail',
    'LLL:EXT:talisman_travelogues/Resources/Private/Language/locallang_be.xlf:plugin.detail.title',
    'EXT:talisman_travelogues/Resources/Public/Icons/detail.jpg'
);

\TYPO3\CMS\Extbase\Utility\ExtensionUtility::registerPlugin(
    'talisman_travelogues',
    'Form',
    'LLL:EXT:talisman_travelogues/Resources/Private/Language/locallang_be.xlf:plugin.form.title',
    'EXT:talisman_travelogues/Resources/Public/Icons/form.jpg'
);

$GLOBALS['TCA']['tt_content']['types']['talismantravelogues_list'] = [
    'showitem' => '
         --palette--;LLL:EXT:frontend/Resources/Private/Language/locallang_ttc.xml:palette.general;general, 
         --palette--;LLL:EXT:frontend/Resources/Private/Language/locallang_ttc.xml:palette.header;header,
         pi_flexform,
      --div--;LLL:EXT:frontend/Resources/Private/Language/locallang_ttc.xml:tabs.appearance,
         --palette--;LLL:EXT:frontend/Resources/Private/Language/locallang_ttc.xml:palette.frames;frames,
      --div--;LLL:EXT:frontend/Resources/Private/Language/locallang_ttc.xml:tabs.access,
         --palette--;LLL:EXT:frontend/Resources/Private/Language/locallang_ttc.xml:palette.visibility;visibility,
         --palette--;LLL:EXT:frontend/Resources/Private/Language/locallang_ttc.xml:palette.access;access,
      --div--;LLL:EXT:frontend/Resources/Private/Language/locallang_ttc.xml:tabs.extended
'];

$GLOBALS['TCA']['tt_content']['types']['talismantravelogues_detail'] = [
    'showitem' => '
         --palette--;LLL:EXT:frontend/Resources/Private/Language/locallang_ttc.xml:palette.general;general,
      --div--;LLL:EXT:frontend/Resources/Private/Language/locallang_ttc.xml:tabs.appearance,
         --palette--;LLL:EXT:frontend/Resources/Private/Language/locallang_ttc.xml:palette.frames;frames,
      --div--;LLL:EXT:frontend/Resources/Private/Language/locallang_ttc.xml:tabs.access,
         --palette--;LLL:EXT:frontend/Resources/Private/Language/locallang_ttc.xml:palette.visibility;visibility,
         --palette--;LLL:EXT:frontend/Resources/Private/Language/locallang_ttc.xml:palette.access;access,
      --div--;LLL:EXT:frontend/Resources/Private/Language/locallang_ttc.xml:tabs.extended
'];

$GLOBALS['TCA']['tt_content']['types']['talismantravelogues_form'] = [
    'showitem' => '
         --palette--;LLL:EXT:frontend/Resources/Private/Language/locallang_ttc.xml:palette.general;general,
      --div--;LLL:EXT:frontend/Resources/Private/Language/locallang_ttc.xml:tabs.appearance,
         --palette--;LLL:EXT:frontend/Resources/Private/Language/locallang_ttc.xml:palette.frames;frames,
      --div--;LLL:EXT:frontend/Resources/Private/Language/locallang_ttc.xml:tabs.access,
         --palette--;LLL:EXT:frontend/Resources/Private/Language/locallang_ttc.xml:palette.visibility;visibility,
         --palette--;LLL:EXT:frontend/Resources/Private/Language/locallang_ttc.xml:palette.access;access,
      --div--;LLL:EXT:frontend/Resources/Private/Language/locallang_ttc.xml:tabs.extended
'];

\TYPO3\CMS\Core\Utility\ExtensionManagementUtility::addPiFlexFormValue(
    '*',
    'FILE:EXT:talisman_travelogues/Configuration/FlexForm/List.xml',
    'talismantravelogues_list'
);
