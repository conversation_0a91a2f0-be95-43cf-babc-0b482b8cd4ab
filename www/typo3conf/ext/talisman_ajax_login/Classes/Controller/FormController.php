<?php
namespace Redkiwi\TalismanAjaxLogin\Controller;

use TYPO3\CMS\Frontend\Controller\TypoScriptFrontendController;

/**
 * Controller: Form
 *
 * @package Redkiwi\TalismanAjaxLoginController
 */
class FormController extends \TYPO3\CMS\Extbase\Mvc\Controller\ActionController
{
    /**
     * @return void
     */
    public function showAction()
    {
        $referrer = $this->uriBuilder
            ->reset()
            ->setCreateAbsoluteUri(true)
            ->build();

        if ($this->getTypoScriptFrontendController()->loginUser) {
            $loggedIn = true;
        } else {
            $loggedIn = false;
        }

        $this->view->assignMultiple([
            'referrer' => $referrer,
            'loggedIn' => $loggedIn
        ]);
    }

    /**
     * @return TypoScriptFrontendController
     */
    protected function getTypoScriptFrontendController()
    {
        return $GLOBALS['TSFE'];
    }
}
