Talisman.Extensions.TalismanMap = {

    /**
     * @var google.maps.Map
     */
    map: null,

    /** @var array */
    markers: [],

    /**
     * @var InfoBox
     */
    activeInfoBox: null,

    /** @var array */
    markerData: [],

    /**
     * @var object
     */
    viewport: {
        sw: {
            latitude: null,
            longitude: null
        },
        ne: {
            latitude: null,
            longitude: null
        }
    },

    /**
     * @var boolean
     */
    mapLoaded: false,

    /**
     * @var boolean
     */
    dataLoaded: false,

    /**
     * @var boolean
     */
    showMap: null,

    /**
     * @var int
     */
    clickedMarkerUid: null,

    /**
     * @return void
     */
    init: function() {
        Talisman.Extensions.TalismanMap.loadMapData();

        $('a[data-action="worldmap-toggle"]').click(function() {
            Talisman.Extensions.TalismanMap.toggleWorldmap();
        });

        $('body').on('click', 'div[data-action="show-accommodation-on-map"]', function(e) {
            e.preventDefault();

            Talisman.Extensions.TalismanMap.clickedMarkerUid = $(this).attr('data-marker-uid');

            if ($('#google-map').is(':visible')) {
                Talisman.Extensions.TalismanMap.goToLastClickedMarker();
                Talisman.Extensions.TalismanMap.scrollToMap();
            } else {
                if (Talisman.Extensions.TalismanMap.dataLoaded) {
                    if (Talisman.Extensions.TalismanMap.mapLoaded) {
                        $('#google-map').toggle();
                        Talisman.Extensions.TalismanMap.goToLastClickedMarker();
                        Talisman.Extensions.TalismanMap.scrollToMap();
                    } else {
                        Talisman.Extensions.TalismanMap.toggleWorldmap();
                        google.maps.event.addListenerOnce(Talisman.Extensions.TalismanMap.map, 'idle', function () {
                            Talisman.Extensions.TalismanMap.goToLastClickedMarker();
                        });
                    }
                }
            }
        });
    },

    /**
     * @return boolean
     */
    toggleWorldmap: function() {
        if (Talisman.Extensions.TalismanMap.dataLoaded) {
            if (Talisman.Extensions.TalismanMap.showMap) {
                $('#google-map').toggle();
                Talisman.Extensions.TalismanMap.loadMap();
                if ($('#google-map').is(':visible')) {
                    Talisman.Extensions.TalismanMap.scrollToMap();
                }
            } else {
                return true;
            }
        }
        return false;
    },

    /**
     * @reutrn void
     */
    scrollToMap: function() {
        if ($(window).scrollTop() !== 0) {
            $('html,body').animate({scrollTop: 0}, 'slow');
        }
    },

    /**
     * @return void
     */
    goToLastClickedMarker: function() {
        for (var i = 0; i < Talisman.Extensions.TalismanMap.markers.length; i++) {
            if (Talisman.Extensions.TalismanMap.markers[i].get('uid') == Talisman.Extensions.TalismanMap.clickedMarkerUid) {
                Talisman.Extensions.TalismanMap.map.panTo(Talisman.Extensions.TalismanMap.markers[i].position);
                Talisman.Extensions.TalismanMap.map.setZoom(12);
                google.maps.event.trigger(Talisman.Extensions.TalismanMap.markers[i], 'click');
                break;
            }
        }

    },

    /**
     * @return void
     */
    loadMap: function() {
        if (Talisman.Extensions.TalismanMap.dataLoaded && !Talisman.Extensions.TalismanMap.mapLoaded) {
            var closeControlDiv = document.createElement('div');
            var closeControlUi = document.createElement('div');
            var closeControlContent = document.createElement('div');

            closeControlContent.className = 'sprite-icon-48 cross xl';
            closeControlUi.className = 'close-button';
            closeControlUi.appendChild(closeControlContent);
            closeControlDiv.appendChild(closeControlUi);

            closeControlDiv.addEventListener('click', function () {
                $('#google-map').toggle();
            });

            Talisman.Extensions.TalismanMap.map = new google.maps.Map(
                document.getElementById('google-map'),
                {
                    center: null,
                    zoom: 3,
                    minZoom: 3,
                    mapTypeId: google.maps.MapTypeId.TERRAIN,
                    scrollwheel: false
                }
            );

            Talisman.Extensions.TalismanMap.map.controls[google.maps.ControlPosition.TOP_RIGHT].push(closeControlDiv);

            for (var i = 0; i < Talisman.Extensions.TalismanMap.markerData.length; i++) {
                var latlng = new google.maps.LatLng(
                    parseFloat(Talisman.Extensions.TalismanMap.markerData[i].latitude),
                    parseFloat(Talisman.Extensions.TalismanMap.markerData[i].longitude)
                );

                var marker = new google.maps.Marker({
                    uid: Talisman.Extensions.TalismanMap.markerData[i].uid,
                    position: latlng,
                    map: Talisman.Extensions.TalismanMap.map,
                    title: Talisman.Extensions.TalismanMap.markerData[i].title,
                    icon: '/typo3conf/ext/talisman_template/Resources/Public/Icons/google-marker.png'
                });

                var infoBoxContent = document.createElement('div');
                infoBoxContent.className = 'info-box';
                infoBoxContent.innerHTML = Talisman.Extensions.TalismanMap.markerData[i].infoBoxHtml;

                var toolBoxContainer = document.createElement('div');
                toolBoxContainer.className = 'toolbox';

                var closeButton = document.createElement('div');
                closeButton.className = 'infobox-button close';
                closeButton.innerHTML = '<span class="sprite-icon-48 cross xl"></span>';

                var optionsButton = document.createElement('div');
                optionsButton.className = 'infobox-button options';
                optionsButton.innerHTML = '<span class="sprite-icon-48 toggle"></span>';

                toolBoxContainer.appendChild(closeButton);
                toolBoxContainer.appendChild(optionsButton);
                infoBoxContent.appendChild(toolBoxContainer);

                var infoBoxOptions = {
                    content: infoBoxContent,
                    maxWidth: 360,
                    boxStyle: {
                        width: '360px'
                    },
                    closeBoxURL: '',
                    infoBoxClearance: new google.maps.Size(1, 1),
                    pane: 'floatPane'
                };

                var infoBox = new InfoBox(infoBoxOptions);

                (function(map, marker, infoBox, closeButton, optionsButton, infoBoxContent) {
                    marker.addListener('click', function () {
                        if (Talisman.Extensions.TalismanMap.activeInfoBox !== null) {
                            Talisman.Extensions.TalismanMap.activeInfoBox.close();
                        }
                        infoBox.open(map, marker);
                        Talisman.Extensions.TalismanMap.activeInfoBox = infoBox;
                    });

                    closeButton.addEventListener('click', function () {
                        Talisman.Extensions.TalismanMap.activeInfoBox.close();
                        Talisman.Extensions.TalismanMap.activeInfoBox = null;
                    });

                    optionsButton.addEventListener('click', function () {
                        Talisman.Extensions.TalismanMap.toggleInfoBox(infoBoxContent);
                        $(optionsButton).toggleClass('white');
                        $(optionsButton).find('.toggle').toggleClass('black');
                    });

                })(Talisman.Extensions.TalismanMap.map, marker, infoBox, closeButton, optionsButton, infoBoxContent);

                Talisman.Extensions.TalismanMap.markers.push(marker);
            }

            new MarkerClusterer(
                Talisman.Extensions.TalismanMap.map,
                Talisman.Extensions.TalismanMap.markers,
                {
                    imagePath: '/typo3conf/ext/talisman_template/Resources/Public/Icons/Clusterer/m'
                }
            );

            Talisman.Extensions.TalismanMap.map.fitBounds(
                new google.maps.LatLngBounds(
                    new google.maps.LatLng(Talisman.Extensions.TalismanMap.viewport.sw.latitude, Talisman.Extensions.TalismanMap.viewport.sw.longitude),
                    new google.maps.LatLng(Talisman.Extensions.TalismanMap.viewport.ne.latitude, Talisman.Extensions.TalismanMap.viewport.ne.longitude)
                )
            );
            Talisman.Extensions.TalismanMap.mapLoaded = true;
        }
    },

    /**
     * @param infoBoxContent
     * @return void
     */
    toggleInfoBox: function(infoBoxContent) {
        $(infoBoxContent).find('.info-box-back').toggleClass('active');
        $(infoBoxContent).find('.info-box-front').toggleClass('active');
    },

    /**
     * @return void
     */
    loadMapData: function() {
        if (!Talisman.Extensions.TalismanMap.dataLoaded) {
            var pageId = $('body').attr('id').replace('page-', '');

            $.ajax({
                dataType: 'json',
                url: '/?type=165',
                data: {
                    pageId: pageId
                },
                success: function (response) {
                    Talisman.Extensions.TalismanMap.showMap = response.showMap;
                    Talisman.Extensions.TalismanMap.dataLoaded = true;
                    if (Talisman.Extensions.TalismanMap.showMap) {
                        Talisman.Extensions.TalismanMap.markerData = response.markers;
                        Talisman.Extensions.TalismanMap.viewport.sw.latitude = response.viewport.swLatitude;
                        Talisman.Extensions.TalismanMap.viewport.sw.longitude = response.viewport.swLongitude;
                        Talisman.Extensions.TalismanMap.viewport.ne.latitude = response.viewport.neLatitude;
                        Talisman.Extensions.TalismanMap.viewport.ne.longitude = response.viewport.neLongitude;
                        $('a[data-action="worldmap-toggle"]').removeClass('hidden');
                    }
                }
            });
        }
    }
};
