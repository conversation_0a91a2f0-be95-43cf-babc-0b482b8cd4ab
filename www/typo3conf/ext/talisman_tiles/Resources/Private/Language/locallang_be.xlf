<?xml version="1.0" encoding="UTF-8"?>
<xliff version="1.0">
    <file source-language="en" datatype="plaintext" original="messages" date="2012-10-17T19:30:32Z" product-name="talisman_tiles">
        <header/>
        <body>
            <trans-unit id="plugin.show.title">
                <source>Tiles</source>
            </trans-unit>
            <trans-unit id="plugin.show.description">
                <source>Shows a grid of two or three tiles</source>
            </trans-unit>

            <trans-unit id="tx_talismantiles_domain_model_tile">
                <source>Tile</source>
            </trans-unit>
            <trans-unit id="tx_talismantiles_domain_model_tile.title_backend">
                <source>Title (backend)</source>
            </trans-unit>
            <trans-unit id="tx_talismantiles_domain_model_tile.type">
                <source>Type</source>
            </trans-unit>
            <trans-unit id="tx_talismantiles_domain_model_tile.type.0">
                <source>Regular</source>
            </trans-unit>
            <trans-unit id="tx_talismantiles_domain_model_tile.type.1">
                <source>With price</source>
            </trans-unit>
            <trans-unit id="tx_talismantiles_domain_model_tile.type.2">
                <source>With button</source>
            </trans-unit>
            <trans-unit id="tx_talismantiles_domain_model_tile.type.3">
                <source>Video</source>
            </trans-unit>
            <trans-unit id="tx_talismantiles_domain_model_tile.visual">
                <source>Visual</source>
            </trans-unit>
            <trans-unit id="tx_talismantiles_domain_model_tile.link">
                <source>Link</source>
            </trans-unit>
            <trans-unit id="tx_talismantiles_domain_model_tile.title">
                <source>Title</source>
            </trans-unit>
            <trans-unit id="tx_talismantiles_domain_model_tile.subtitle">
                <source>Subtitle</source>
            </trans-unit>
            <trans-unit id="tx_talismantiles_domain_model_tile.price">
                <source>Price</source>
            </trans-unit>
            <trans-unit id="tx_talismantiles_domain_model_tile.button_text">
                <source>Button text</source>
            </trans-unit>

            <trans-unit id="tt_content.talismantiles_tiles">
                <source>Tiles</source>
            </trans-unit>

            <trans-unit id="configuration.link">
                <source>Link</source>
            </trans-unit>
            <trans-unit id="configuration.link_text">
                <source>Link text</source>
            </trans-unit>
        </body>
    </file>
</xliff>

