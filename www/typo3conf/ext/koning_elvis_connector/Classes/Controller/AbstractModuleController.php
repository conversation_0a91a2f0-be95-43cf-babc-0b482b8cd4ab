<?php
namespace Keizer\KoningElvisConnector\Controller;

use Keizer\KoningElvisConnector\Utility\ConfigurationUtility;

/**
 * Controller: Abstract module
 *
 * @package Keizer\KoningElvisConnector\Controller
 */
class AbstractModuleController extends \Keizer\KoningLibrary\Controller\AbstractModuleController
{
    /** @var string */
    protected $sessionKey;

    /**
     * @var \Keizer\KoningElvisConnector\Service\ElvisService
     * @inject
     */
    protected $elvisService;

    /**
     * @var \Keizer\KoningLibrary\Backend\BackendSession
     * @inject
     */
    protected $session;

    /** @var array */
    protected $configuration = [];

    /** @var bool */
    protected $connectionStatus = false;

    /**
     * @return void
     */
    public function initializeAction()
    {
        $this->configuration = ConfigurationUtility::getElvisDamSettings();

        $this->connectionStatus = $this->elvisService->connect(
            $this->configuration['baseUrl'],
            $this->configuration['username'],
            $this->configuration['password'],
            $this->configuration['cookieFile']
        );

        $this->session->setBackendUserAuthentication($GLOBALS['BE_USER']);
        $this->session->createSession($this->sessionKey, [
            'folders' => [],
            'rootFolder' => 'root',
            'containerId' => ''
        ]);
    }

    /**
     * @param array $browseFolderResults
     * @param string $rootFolder
     * @param string $containerId
     * @return void
     */
    protected function saveFoldersInSession($browseFolderResults, $rootFolder = 'root', $containerId = '')
    {
        $folders = [];
        foreach ($browseFolderResults as $folder) {
            $folders[$rootFolder][$folder['assetPath']] = [
                'name' => $folder['name'],
                'assetPath' => $folder['assetPath'],
                'containerId' => $folder['containerId'],
                'children' => []
            ];
        }

        if ($rootFolder === 'root') {
            $this->session->saveSessionContents($this->sessionKey, [
                'folders' => $folders,
                'rootFolder' => $rootFolder,
                'containerId' => $containerId
            ]);
        } else {
            $folderTree = $this->session->getSessionContents($this->sessionKey)['folders']['root'];

            $this->saveFoldersRecursively(
                $folderTree,
                $folders,
                $rootFolder
            );

            $this->session->saveSessionContents($this->sessionKey, [
                'folders' => ['root' => $folderTree],
                'rootFolder' => $rootFolder,
                'containerId' => $containerId
            ]);
        }
    }

    /**
     * @param array $folderTree
     * @param array $folders
     * @param string $rootFolder
     */
    protected function saveFoldersRecursively(array &$folderTree, array $folders, $rootFolder = 'root')
    {
        foreach ($folders as $parentFolder => $folder) {
            foreach ($folderTree as $folderPath => &$treeFolder) {
                if ($parentFolder === $folderPath) {
                    $treeFolder['children'] = $folder;
                } else if (!empty($treeFolder['children'])) {
                    $this->saveFoldersRecursively($treeFolder['children'], $folders, $rootFolder);
                }
            }
        }
    }
}
