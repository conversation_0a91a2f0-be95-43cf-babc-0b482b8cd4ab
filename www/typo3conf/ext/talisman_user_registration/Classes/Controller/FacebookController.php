<?php
namespace Redkiwi\TalismanUserRegistration\Controller;

use Redkiwi\TalismanUserRegistration\Domain\Model\User;
use Redkiwi\TalismanUserRegistration\Facebook\PersistentDataHandler;
use TYPO3\CMS\Extbase\Domain\Model\FrontendUserGroup;

/**
 * Controller: Facebook
 *
 * @package Redkiwi\TalismanUserRegistration\Controller
 */
class FacebookController extends \TYPO3\CMS\Extbase\Mvc\Controller\ActionController
{
    /** @var \TYPO3\CMS\Extbase\Persistence\PersistenceManagerInterface */
    protected $persistenceManager;

    /**
     * @var \Redkiwi\TalismanUserRegistration\Domain\Repository\UserRepository
     * @inject
     */
    protected $userRepository;

    /**
     * @var \TYPO3\CMS\Extbase\Domain\Repository\FrontendUserGroupRepository
     * @inject
     */
    protected $userGroupRepository;

    /**
     * @var \Redkiwi\TalismanUserRegistration\Service\AuthenticationService
     * @inject
     */
    protected $authenticationService;

    /** @var \Facebook\Facebook */
    protected $facebook;

    /**
     * @param \TYPO3\CMS\Extbase\Persistence\PersistenceManagerInterface $persistenceManager
     */
    public function injectPersistenceManager(\TYPO3\CMS\Extbase\Persistence\PersistenceManagerInterface $persistenceManager)
    {
        $this->persistenceManager = $persistenceManager;
    }

    /**
     * @param string $referrer
     * @return void
     * @throws \TYPO3\CMS\Extbase\Mvc\Exception\StopActionException
     * @throws \TYPO3\CMS\Extbase\Mvc\Exception\UnsupportedRequestTypeException
     */
    public function facebookLoginAction($referrer)
    {
        $helper = $this->facebook->getRedirectLoginHelper();

        $handleLoginUrl = $this->uriBuilder
            ->reset()
            ->setTargetPageUid($GLOBALS['TSFE']->id)
            ->setCreateAbsoluteUri(true)
            ->setUseCacheHash(false)
            ->uriFor('handleFacebookLogin', ['referrer' => $referrer]);

        $loginUrl = $helper->getLoginUrl($handleLoginUrl, ['email']);

        $this->redirectToUri($loginUrl);
    }

    /**
     * @param string $referrer
     * @return void
     * @throws \Exception
     */
    public function handleFacebookLoginAction($referrer)
    {
        $helper = $this->facebook->getRedirectLoginHelper();

        try {
            $accessToken = $helper->getAccessToken();

            $oAuth2Client = $this->facebook->getOAuth2Client();

            $tokenMetadata = $oAuth2Client->debugToken($accessToken);
            $tokenMetadata->validateAppId($this->settings['facebook']['appId']);
            $tokenMetadata->validateExpiration();

            if (!$accessToken->isLongLived()) {
                try {
                    $accessToken = $oAuth2Client->getLongLivedAccessToken($accessToken);
                } catch (\Facebook\Exceptions\FacebookSDKException $e) {
                    $this->redirect('facebookError');
                }
            }

            $response = $this->facebook->get('/me?fields=first_name,middle_name,last_name,email', $accessToken);
            if ($response->getHttpStatusCode() === 200) {
                $facebookUser = $response->getGraphUser();

                $localUser = $this->userRepository->findOneByFacebookId($facebookUser->getId());
                if (!($localUser instanceof User)) {
                    $userGroup = $this->userGroupRepository->findByUid($this->settings['signUp']['userGroup']);
                    if (!($userGroup instanceof FrontendUserGroup)) {
                        throw new \Exception('Frontend user group not found, check TypoScript configuration');
                    }

                    $localUser = new User();
                    $localUser->setFirstName($facebookUser->getFirstName());
                    $localUser->setMiddleName($facebookUser->getMiddleName());
                    $localUser->setLastName($facebookUser->getLastName());
                    $localUser->setUsername('facebook-' . $facebookUser->getId());
                    $localUser->setEmail($facebookUser->getEmail());
                    $localUser->setConfirmed(true);
                    $localUser->setFacebookId($facebookUser->getId());
                    $localUser->addUsergroup($userGroup);
                    $localUser->setPassword(md5(time() . $facebookUser->getId()));
                    $this->userRepository->add($localUser);
                    $this->persistenceManager->persistAll();
                }

                $this->authenticationService->loginUser($localUser->getUsername());
                $this->redirectToUri($referrer);
            }
        } catch (\Facebook\Exceptions\FacebookResponseException $e) {
            $this->redirect('facebookError');
        } catch (\Facebook\Exceptions\FacebookSDKException $e) {
            $this->redirect('facebookError');
        }
    }

    /**
     * @return void
     */
    public function facebookErrorAction()
    {
    }

    /**
     * @return void
     */
    protected function initializeAction()
    {
        $this->facebook = new \Facebook\Facebook([
            'app_id' => $this->settings['facebook']['appId'],
            'app_secret' => $this->settings['facebook']['appSecret'],
            'default_graph_version' => 'v2.2',
            'persistent_data_handler' => new PersistentDataHandler()
        ]);
    }
}
