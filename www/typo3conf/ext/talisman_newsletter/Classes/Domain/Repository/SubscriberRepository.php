<?php
namespace Redkiwi\TalismanNewsletter\Domain\Repository;

/**
 * Repository: Brochure
 *
 * @package Redkiwi\TalismanNewsletter\Domain\Repository
 */
class SubscriberRepository extends \TYPO3\CMS\Extbase\Persistence\Repository
{
    /**
     * @param int $limit
     * @return array|\TYPO3\CMS\Extbase\Persistence\QueryResultInterface
     */
    public function findByLimit($limit = 10)
    {
        $query = $this->createQuery();
        return $query->setLimit((int)$limit)->execute();
    }
}
