<?php
namespace Redkiwi\TalismanQuoteForm\Domain\Session;

use Keizer\KoningLibrary\Domain\Session\SessionInterface;

/**
 * Session: Quote form
 *
 * @package Redkiwi\TalismanQuoteForm\Domain\Session
 */
class QuoteForm implements SessionInterface
{
    /** @var \Redkiwi\TalismanQuoteForm\Domain\Model\DataTransfer\Quote\StepOne */
    protected $stepOne;

    /** @var \Redkiwi\TalismanQuoteForm\Domain\Model\DataTransfer\Quote\StepTwo */
    protected $stepTwo;

    /** @var \Redkiwi\TalismanQuoteForm\Domain\Model\DataTransfer\Quote\StepThree */
    protected $stepThree;

    /**
     * @return \Redkiwi\TalismanQuoteForm\Domain\Model\DataTransfer\Quote\StepOne
     */
    public function getStepOne()
    {
        return $this->stepOne;
    }

    /**
     * @param \Redkiwi\TalismanQuoteForm\Domain\Model\DataTransfer\Quote\StepOne $stepOne
     * @return void
     */
    public function setStepOne(\Redkiwi\TalismanQuoteForm\Domain\Model\DataTransfer\Quote\StepOne $stepOne)
    {
        $this->stepOne = $stepOne;
    }

    /**
     * @return \Redkiwi\TalismanQuoteForm\Domain\Model\DataTransfer\Quote\StepTwo
     */
    public function getStepTwo()
    {
        return $this->stepTwo;
    }

    /**
     * @param \Redkiwi\TalismanQuoteForm\Domain\Model\DataTransfer\Quote\StepTwo $stepTwo
     * @return void
     */
    public function setStepTwo(\Redkiwi\TalismanQuoteForm\Domain\Model\DataTransfer\Quote\StepTwo $stepTwo)
    {
        $this->stepTwo = $stepTwo;
    }

    /**
     * @return \Redkiwi\TalismanQuoteForm\Domain\Model\DataTransfer\Quote\StepThree
     */
    public function getStepThree()
    {
        return $this->stepThree;
    }

    /**
     * @param \Redkiwi\TalismanQuoteForm\Domain\Model\DataTransfer\Quote\StepThree $stepThree
     * @return void
     */
    public function setStepThree(\Redkiwi\TalismanQuoteForm\Domain\Model\DataTransfer\Quote\StepThree $stepThree)
    {
        $this->stepThree = $stepThree;
    }
}
