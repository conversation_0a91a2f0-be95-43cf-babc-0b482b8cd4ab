<?php
namespace Redkiwi\TalismanTravelogues\Domain\Repository;

/**
 * Repository: Travelogue
 *
 * @package Redkiwi\TalismanTravelogues\Domain\Repository
 */
class TravelogueRepository extends \TYPO3\CMS\Extbase\Persistence\Repository
{
    /** @var array */
    protected $defaultOrderings = [
        'date' => \TYPO3\CMS\Extbase\Persistence\QueryInterface::ORDER_DESCENDING
    ];

    /**
     * @return void
     */
    public function initializeObject()
    {
        $querySettings = \TYPO3\CMS\Core\Utility\GeneralUtility::makeInstance(\TYPO3\CMS\Extbase\Persistence\Generic\Typo3QuerySettings::class);
        /** @var \TYPO3\CMS\Extbase\Persistence\Generic\Typo3QuerySettings $querySettings */
        $querySettings->setRespectStoragePage(false);
        $this->setDefaultQuerySettings($querySettings);
    }

    /**
     * @param int $pid
     * @param boolean $hideInList
     * @return array|\TYPO3\CMS\Extbase\Persistence\QueryResultInterface
     */
    public function findByPidAndHideInList($pid, $hideInList)
    {

        $query = $this->createQuery();
        $constraints = [];
        $constraints[] = $query->equals('pid', $pid);
        $constraints[] = $query->equals('hideInList', $hideInList);
        return $query
            ->matching($query->logicalAnd($constraints))
            ->execute();
    }
}
