{namespace k=Keizer\KoningLibrary\ViewHelper}
<f:layout name="Default" />

<f:section name="Content">
    <h2>
        <f:translate id="sign_up.confirm.header" />
    </h2>

    <div class="nav-tabs-box with-border activate-account">
        <p>
            <f:format.nl2br><f:translate id="sign_up.confirm.text" arguments="{0: user.firstName}"  /></f:format.nl2br>
        </p>

        <f:form action="activate" objectName="activate" class="form" arguments="{hash: user.signUphash}">
            <div class="row">
                <div class="col-sm-6">
                    <f:render
                        partial="Form/Password"
                        arguments="{
                            property: 'password',
                            formObjectName: 'activate',
                            label: '{f:translate(id: \'sign_up.confirm.password\')}'
                        }" />
                </div>
            </div>
            <div class="row">
                <div class="col-sm-6">
                    <f:render
                        partial="Form/Password"
                        arguments="{
                            property: 'repeatPassword',
                            formObjectName: 'activate',
                            label: '{f:translate(id: \'sign_up.confirm.repeat_password\')}'
                        }" />
                </div>
            </div>

            <div class="row submit-row">
                <div class="col-sm-6 col-sm-push-6 text-right">
                    <button type="submit" class="btn btn-default btn-rubine pull-right">
                        <f:translate id="sign_up.confirm.submit" />
                    </button>
                    <div class="clearfix"></div>
                </div>
                <div class="col-sm-6 col-sm-pull-6">
                    <span class="mandatory">* <f:translate id="form.label.mandatory" extensionName="TalismanTemplate" /></span>
                </div>
            </div>
        </f:form>
    </div>
</f:section>
