<f:layout name="Mail" />

<f:section name="Title">
    {subject}
</f:section>

<f:section name="Content">
    <table cellpadding="0" cellspacing="0" border="0" id="backgroundTable">
        <tr>
            <td style="vertical-align:top;" valign="top">
                <table cellpadding="0" cellspacing="0" border="0" align="center">
                    <tr>
                        <td width="600" colspan="2">
                            <img class="image_fix" src="{f:uri.resource(path: 'Images/logo.png', extensionName: 'talisman_template', absolute: 1)}" width="180" height="84" alt="{values.subject}" title="{values.subject}" />
                        </td>
                    </tr>
                    <tr>
                        <td style="vertical-align:top;" height="20">&nbsp;</td>
                    </tr>
                    <tr>
                        <td style="vertical-align:top;" width="600">
                            <span>
                                Onderstaand een overzicht van de nieuwe belafspraak:
                            </span>
                        </td>
                    </tr>
                    <tr>
                        <td style="vertical-align:top;" height="80">&nbsp;</td>
                    </tr>
                </table>
                <table cellpadding="0" cellspacing="0" border="0" align="center">
                    <tr>
                        <td style="vertical-align:top;" width="250">
                            <span><f:translate id="form.show.name" extensionName="TalismanAppointment" /></span>
                        </td>
                        <td style="vertical-align:top;" width="350">
                            <span>
                                {fields.name}
                            </span>
                        </td>
                    </tr>
                    <tr>
                        <td style="vertical-align:top;" height="20">&nbsp;</td>
                    </tr>
                </table>
                <table cellpadding="0" cellspacing="0" border="0" align="center">
                    <tr>
                        <td style="vertical-align:top;" width="250">
                            <span><f:translate id="form.show.phone" extensionName="TalismanAppointment" /></span>
                        </td>
                        <td style="vertical-align:top;" width="350">
                            <span>
                                {fields.phone}
                            </span>
                        </td>
                    </tr>
                    <tr>
                        <td style="vertical-align:top;" height="20">&nbsp;</td>
                    </tr>
                </table>
                <table cellpadding="0" cellspacing="0" border="0" align="center">
                    <tr>
                        <td style="vertical-align:top;" width="250">
                            <span><f:translate id="form.show.known" extensionName="TalismanAppointment" /></span>
                        </td>
                        <td style="vertical-align:top;" width="350">
                            <span>
                                {fields.known}
                            </span>
                        </td>
                    </tr>
                    <tr>
                        <td style="vertical-align:top;" height="20">&nbsp;</td>
                    </tr>
                </table>

                <table cellpadding="0" cellspacing="0" border="0" align="center">
                    <tr>
                        <td style="vertical-align:top;" width="250">
                            <span><f:translate id="form.show.phoneAppointment" extensionName="TalismanAppointment" /></span>
                        </td>
                        <td style="vertical-align:top;" width="350">
                            <span>
                                <f:if condition="{fields.preferredReachDateAnswer}">
                                    <f:translate id="form.date.{fields.preferredReachDateAnswer}" extensionName="TalismanAppointment" />
                                </f:if>
                            </span>
                        </td>
                    </tr>
                    <tr>
                        <td style="vertical-align:top;" height="20">&nbsp;</td>
                    </tr>
                </table>

                <f:if condition="{fields.preferredReachTimeAnswer}">
                    <f:then>
                        <table cellpadding="0" cellspacing="0" border="0" align="center">
                            <tr>
                                <td style="vertical-align:top;" width="250"></td>
                                <td style="vertical-align:top;" width="350">
                                    <span>
                                        <f:if condition="{fields.preferredReachTimeAnswer}">
                                            <f:translate id="form.time.{fields.preferredReachTimeAnswer}" extensionName="TalismanAppointment" />
                                        </f:if>
                                    </span>
                                </td>
                            </tr>
                            <tr>
                                <td style="vertical-align:top;" height="20">&nbsp;</td>
                            </tr>
                        </table>
                    </f:then>
                    <f:else>
                        <table cellpadding="0" cellspacing="0" border="0" align="center">
                            <tr>
                                <td style="vertical-align:top;" width="250"></td>
                                <td style="vertical-align:top;" width="350">
                                    <span>
                                        <f:if condition="{fields.preferredReachTimeOtherAnswer}">
                                            {fields.preferredReachTimeOtherAnswer}
                                        </f:if>
                                    </span>
                                </td>
                            </tr>
                            <tr>
                                <td style="vertical-align:top;" height="20">&nbsp;</td>
                            </tr>
                        </table>
                    </f:else>
                </f:if>

                <table cellpadding="0" cellspacing="0" border="0" align="center">
                    <tr>
                        <td style="vertical-align:top;" width="250">
                            <span><f:translate id="form.show.description" extensionName="TalismanAppointment" /></span>
                        </td>
                        <td style="vertical-align:top;" width="350">
                            <span>
                                {fields.description}
                            </span>
                        </td>
                    </tr>
                    <tr>
                        <td style="vertical-align:top;" height="20">&nbsp;</td>
                    </tr>
                </table>
            </td>
        </tr>
    </table>
</f:section>
