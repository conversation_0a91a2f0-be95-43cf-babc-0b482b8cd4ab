plugin.tx_felogin_pi1 {
    storagePid = {$pages.storage.users}
    templateFile = EXT:talisman_template/Resources/Private/Extensions/felogin/Templates/FrontendLogin.html

    _CSS_DEFAULT_STYLE >

    errorMessage_stdWrap {
        wrap (
            <div class="alert alert-danger">
                <i class="fa fa-exclamation-triangle"></i>&nbsp;|
            </div>
        )
    }

    _LOCAL_LANG {
        nl {
            ll_welcome_message =
            ll_error_message = Gebruikersnaam/wachtwoord combinatie niet gevonden.
            username = E-mailadres
            password = Wachtwoord
            permalogin = Onthoud mij
        }
    }
}
