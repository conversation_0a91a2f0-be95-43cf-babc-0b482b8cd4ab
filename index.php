<?php
require_once 'vendor/autoload.php';

use Dompdf\Dompdf;

// Dummy data for order
$order = [
    'number' => '#958201',
    'billing_address' => [
        'companyname' => null,
        'name' => '<PERSON>',
        'street' => 'Daltonstraat',
        'housenumber' => '65',
        'address_line_2' => '',
        'zipcode' => '3316GD',
        'city' => 'Dordrecht',
        'country' => 'NL',
        'email' => '<EMAIL>',
        'phone' => '0101234567',
    ],
    'delivery_address' => [
        'companyname' => '',
        'name' => '<PERSON>',
        'street' => 'Daltonstraat',
        'housenumber' => '65',
        'address_line_2' => '',
        'zipcode' => '3316GD',
        'city' => 'Dordrecht',
        'country' => 'NL',
    ],
    'items' => [
        [
            'qty' => 2,
            'name' => 'Jeans - Black - 36',
            'sku' => 69205,
            'ean' => '8710552295268',
        ],
        [
            'qty' => 1,
            'name' => '<PERSON>ja<PERSON> - <PERSON><PERSON> Oranje',
            'sku' => 25920,
            'ean' => '3059943009097',
        ]
    ]
];

// API credentials
$user = '<EMAIL>';
$password = '4QJW9yh94PbTcpJGdKz6egwH';

$companyId = '9e606e6b-44a4-4a4e-a309-cc70ddd3a103';
$brandId = 'e41c8d26-bdfd-4999-9086-e5939d67ae28';

$productCombinationId = 3; // DHL pakje

// wrap all API calls into a function
function call_qls_api($endpoint, $method = 'GET', $payload = null)
{
    global $user, $password;

    $curl = curl_init();
    curl_setopt($curl, CURLOPT_URL, 'https://api.pakketdienstqls.nl' . $endpoint);
    curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($curl, CURLOPT_HTTPAUTH, CURLAUTH_BASIC);
    curl_setopt($curl, CURLOPT_USERPWD, $user . ':' . $password);
    curl_setopt($curl, CURLOPT_TIMEOUT, 30);

    if ($method === 'POST' && $payload) {
        curl_setopt($curl, CURLOPT_POST, true);
        curl_setopt($curl, CURLOPT_HTTPHEADER, array('Content-Type: application/json'));
        curl_setopt($curl, CURLOPT_POSTFIELDS, json_encode($payload));
    }

    $response = curl_exec($curl);
    $http_code = curl_getinfo($curl, CURLINFO_HTTP_CODE);
    $redirect_url = curl_getinfo($curl, CURLINFO_REDIRECT_URL);
    curl_close($curl);

    // basic error handling
    if ($response === false || $http_code >= 400) {
        throw new RuntimeException("QLS API call failed. HTTP code: $http_code, Response: " . ($response ?: 'false'));
    }

    // for 302 responses, return the redirect URL so we can extract shipment ID
    if ($http_code === 302 && $redirect_url) {
        return ['redirect_url' => $redirect_url];
    }

    return $response;
}


function create_shipment($productCombinationId)
{
    global $order, $companyId, $brandId;

    // build the shipment information for the API
    $shipment_data = array(
        'product_id' => (int)$productCombinationId,
        'product_combination_id' => (int)$productCombinationId,
        'brand_id' => $brandId,
        'reference' => $order['number'],
        'weight' => 1000, // 1kg default weight
        'cod_amount' => 0, // no cash on delivery
        'receiver_contact' => array(
            'name' => $order['delivery_address']['name'],
            'companyname' => $order['delivery_address']['companyname'] ?: '',
            'street' => $order['delivery_address']['street'],
            'housenumber' => $order['delivery_address']['housenumber'],
            'postalcode' => $order['delivery_address']['zipcode'],
            'locality' => $order['delivery_address']['city'],
            'country' => $order['delivery_address']['country'],
        )
    );

    $response = call_qls_api("/companies/$companyId/shipments", 'POST', $shipment_data);

    // API didn't work
    if (!$response) {
        throw new RuntimeException("QLS shipment creation failed - no response from API");
    }

    // check if we got a successful redirect (302)
    if (isset($response['redirect_url'])) {
        // extract shipment ID from redirect URL
        // URL format: .../shipment-lists/2ccfda85-6d8f-4d2c-95d6-63ba2b8891e7
        $url_parts = explode('/', $response['redirect_url']);
        $shipment_id = end($url_parts);
        return ['id' => $shipment_id];
    }

    $result = json_decode($response, true);

    // something went wrong with the response
    if (isset($result['errors']) || !isset($result['data']['id'])) {
        throw new RuntimeException("QLS shipment creation failed - API returned errors: " . json_encode($result));
    }

    return $result['data'];
}

function get_shipment_label_pdf($shipment_id)
{
    global $companyId;

    // try common QLS API endpoints for label download
    $possible_endpoints = [
        "/v2/companies/$companyId/shipments/$shipment_id/label",
        "/companies/$companyId/shipments/$shipment_id/label",
        "/v2/companies/$companyId/shipments/$shipment_id/labels",
        "/companies/$companyId/shipments/$shipment_id/labels"
    ];

    foreach ($possible_endpoints as $endpoint) {
        try {
            $response = call_qls_api($endpoint);
            if ($response && strlen($response) > 1000) { // basic check for PDF content
                return $response;
            }
        } catch (RuntimeException $e) {
            // try next endpoint
            continue;
        }
    }

    throw new RuntimeException("Could not retrieve label PDF from QLS API");
}

function convert_pdf_to_image($pdf_content)
{
    // create temp file for PDF
    $temp_pdf = tempnam(sys_get_temp_dir(), 'qls_label_') . '.pdf';
    $temp_image = tempnam(sys_get_temp_dir(), 'qls_label_') . '.png';

    try {
        file_put_contents($temp_pdf, $pdf_content);

        // convert PDF to PNG using ImageMagick
        $imagick = new Imagick();
        $imagick->setResolution(150, 150); // good quality for labels
        $imagick->readImage($temp_pdf . '[0]'); // first page only
        $imagick->setImageFormat('png');
        $imagick->setImageCompressionQuality(90);

        // resize to fit nicely in A4 layout (max width ~400px)
        $imagick->resizeImage(400, 0, Imagick::FILTER_LANCZOS, 1);

        $imagick->writeImage($temp_image);
        $image_data = file_get_contents($temp_image);

        $imagick->clear();
        $imagick->destroy();

        return base64_encode($image_data);

    } finally {
        // cleanup temp files
        if (file_exists($temp_pdf)) unlink($temp_pdf);
        if (file_exists($temp_image)) unlink($temp_image);
    }
}

function generate_label_html($shipment_id): string
{
    try {
        // get actual PDF label from QLS API
        $label_pdf = get_shipment_label_pdf($shipment_id);

        // convert PDF to image
        $label_image_base64 = convert_pdf_to_image($label_pdf);

        // embed real label as image
        return '<div class="shipping-label-container">
                    <img src="data:image/png;base64,' . $label_image_base64 . '"
                         alt="QLS Verzendlabel" class="label-image" />
                </div>';

    } catch (Exception $e) {
        // fallback to basic info if label retrieval fails
        return '<div class="shipping-label-container">
                    <div class="label-error">
                        <strong>Verzendlabel kon niet worden opgehaald</strong><br>
                        Zending ID: ' . htmlspecialchars($shipment_id) . '
                    </div>
                </div>';
    }
}

function build_pdf_document($label_html): ?string
{
    global $order;
    $billing = $order['billing_address'];
    $delivery = $order['delivery_address'];
    $order_num = $order['number'];

    // load CSS and build HTML for PDF
    $css_content = file_get_contents('style.css');
    $html_content = '<!DOCTYPE html>
<html lang="nl">
<head>
    <meta charset="UTF-8">
    <title>QLS Verzendlabel Generator</title>
    <style>' . $css_content . '</style>
</head>
<body>
    <div class="document-header">
        <h1>PAKBON - ' . htmlspecialchars($order_num) . '</h1>
    </div>

    <div class="address-container">
        <div class="addr-left">
            <div class="address-box">
                <h3>Factuuradres</h3>
                ' . htmlspecialchars($billing['name']) . '<br>
                ' . htmlspecialchars($billing['street'] . ' ' . $billing['housenumber']) . '<br>
                ' . htmlspecialchars($billing['zipcode'] . ' ' . $billing['city']) . '<br>
                ' . htmlspecialchars($billing['email']) . '
            </div>
        </div>
        <div class="addr-right">
            <div class="address-box">
                <h3>Bezorgadres</h3>
                ' . htmlspecialchars($delivery['name']) . '<br>
                ' . htmlspecialchars($delivery['street'] . ' ' . $delivery['housenumber']) . '<br>
                ' . htmlspecialchars($delivery['zipcode'] . ' ' . $delivery['city']) . '
            </div>
        </div>
    </div>

    <table class="items-table">
        <thead>
            <tr>
                <th>Aantal</th>
                <th>Artikel</th>
                <th>SKU</th>
                <th>EAN</th>
            </tr>
        </thead>
        <tbody>';

    // Adds all the order items to the PDF
    foreach ($order['items'] as $item) {
        $html_content .= '<tr>
            <td>' . $item['qty'] . '</td>
            <td>' . htmlspecialchars($item['name']) . '</td>
            <td>' . htmlspecialchars($item['sku']) . '</td>
            <td>' . htmlspecialchars($item['ean']) . '</td>
        </tr>';
    }

    $html_content .= '
        </tbody>
    </table>

    <div class="shipping-label">
        <h3>Verzendlabel</h3>
        ' . $label_html . '
    </div>
</body>
</html>';

    // Generate the pdf
    $dompdf = new Dompdf();
    $dompdf->loadHtml($html_content);
    $dompdf->setPaper('A4');
    $dompdf->render();

    return $dompdf->output();
}

// checks if the form was submitted or not
$error_message = '';
if ($_POST) {
    try {
        $shipment_result = create_shipment(1); // DHL pakje

        if ($shipment_result && isset($shipment_result['id'])) {
            $label_html = generate_label_html($shipment_result['id']);
            $pdf_content = build_pdf_document($label_html);

            // create filename
            $orderRef = str_replace('#', '', $order['number']);
            $timestamp = date('Y-m-d_H-i-s');
            $pdf_filename = "pakbon_{$orderRef}_{$timestamp}.pdf";

            // send PDF to browser
            header('Content-Type: application/pdf');
            header('Content-Disposition: attachment; filename="' . $pdf_filename . '"');
            echo $pdf_content;
            exit;
        }
    } catch (RuntimeException $e) {
        $error_message = 'Kon geen verzendlabel aanmaken. Probeer het later opnieuw.';
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>QLS Verzendlabel Generator</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
<h1>QLS Verzendlabel Generator</h1>

<div class="main-container">
    <h2>Bestelling <?= htmlspecialchars($order['number']) ?></h2>

    <div class="order-info">
        <p><strong>Klant:</strong> <?= htmlspecialchars($order['delivery_address']['name']) ?></p>
        <p><strong>Bezorgadres:</strong>
            <?= htmlspecialchars($order['delivery_address']['street'] . ' ' . $order['delivery_address']['housenumber']) ?>
            ,
            <?= htmlspecialchars($order['delivery_address']['zipcode'] . ' ' . $order['delivery_address']['city']) ?>
        </p>
    </div>

    <table>
        <thead>
        <tr>
            <th>Aantal</th>
            <th>Artikel</th>
            <th>SKU</th>
            <th>EAN</th>
        </tr>
        </thead>
        <tbody>
        <?php foreach ($order['items'] as $item): ?>
            <tr>
                <td><?= $item['qty'] ?></td>
                <td><?= htmlspecialchars($item['name']) ?></td>
                <td><?= htmlspecialchars($item['sku']) ?></td>
                <td><?= htmlspecialchars($item['ean']) ?></td>
            </tr>
        <?php endforeach; ?>
        </tbody>
    </table>
</div>

<div class="main-container">
    <h2>Verzendlabel Genereren</h2>

    <div>
        <p>Verzending via DHL Pakje. Na het klikken op de knop wordt er een PDF gemaakt met daarop de pakbon
            en de verzendlabel.</p>

        <?php if ($error_message): ?>
            <div id="status-message">
                <?= htmlspecialchars($error_message) ?>
            </div>
        <?php endif; ?>

        <form method="post">
            <input type="hidden" name="product_id" value="1">
            <button type="submit">Genereer Pakbon + Label</button>
        </form>
    </div>
</div>
</body>
</html>
