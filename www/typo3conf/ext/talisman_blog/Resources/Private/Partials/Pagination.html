<nav>
    <ul class="tx-pagebrowse">
        <li class="tx-pagebrowse-prev">
            <f:if condition="{pagination.previousPage}">
                <f:then>
                    <f:if condition="{country}">
                        <f:then>
                            <f:link.action action="{action}" controller="{controller}" arguments="{page: pagination.previousPage, country: country}" section="{section}">
                                <span class="sprite-icon-48 arrow-left xl"></span>
                            </f:link.action>
                        </f:then>
                        <f:else>
                            <f:if condition="{author}">
                                <f:then>
                                    <f:link.action action="{action}" controller="{controller}" arguments="{page: pagination.previousPage, author: author}" section="{section}">
                                        <span class="sprite-icon-48 arrow-left xl"></span>
                                    </f:link.action>
                                </f:then>
                                <f:else>
                                    <f:link.action action="{action}" controller="{controller}" arguments="{page: pagination.previousPage}" section="{section}">
                                        <span class="sprite-icon-48 arrow-left xl"></span>
                                    </f:link.action>
                                </f:else>
                            </f:if>
                        </f:else>
                    </f:if>
                </f:then>
                <f:else>
                    <span class="sprite-icon-48 arrow-left xl"></span>
            </f:else>
        </f:if>
        <f:if condition="{pagination.hasLessPages}">
            <li>
                ...
            </li>
        </f:if>
        <f:for each="{pagination.pages}" as="page">
            <f:if condition="{page.isCurrent}">
                <f:then>
                    <li class="tx-pagebrowse-current">{page.number}</li>
                </f:then>
                <f:else>
                    <li class="tx-pagebrowse-page">
                        <f:if condition="{country}">
                            <f:then>
                                <f:link.action action="{action}" controller="{controller}" arguments="{page: page.number, country: country}" section="{section}">{page.number}</f:link.action>
                            </f:then>
                            <f:else>
                                <f:if condition="{author}">
                                    <f:then>
                                        <f:link.action action="{action}" controller="{controller}" arguments="{page: page.number, author: author}" section="{section}">{page.number}</f:link.action>
                                    </f:then>
                                    <f:else>
                                        <f:link.action action="{action}" controller="{controller}" arguments="{page: page.number}" section="{section}">{page.number}</f:link.action>
                                    </f:else>
                                </f:if>
                            </f:else>
                        </f:if>
                    </li>
                </f:else>
            </f:if>
        </f:for>
        <f:if condition="{pagination.hasMorePages}">
            <li>...</li>
        </f:if>
        <f:if condition="{pagination.nextPage}">
            <f:then>
                <li class="tx-pagebrowse-next">
                    <f:if condition="{country}">
                        <f:then>
                            <f:link.action action="{action}" controller="{controller}" arguments="{page: pagination.nextPage, country: country}" section="{section}">
                                <span class="sprite-icon-48 arrow-right xl"></span>
                            </f:link.action>
                        </f:then>
                        <f:else>
                            <f:if condition="{author}">
                                <f:then>
                                    <f:link.action action="{action}" controller="{controller}" arguments="{page: pagination.nextPage, author: author}" section="{section}">
                                        <span class="sprite-icon-48 arrow-right xl"></span>
                                    </f:link.action>
                                </f:then>
                                <f:else>
                                    <f:link.action action="{action}" controller="{controller}" arguments="{page: pagination.nextPage}" section="{section}">
                                        <span class="sprite-icon-48 arrow-right xl"></span>
                                    </f:link.action>
                                </f:else>
                            </f:if>
                        </f:else>
                    </f:if>
                </li>
            </f:then>
            <f:else>
                <li class="tx-pagebrowse-next">
                    <span class="sprite-icon-48 arrow-right xl"></span>
                </li>
            </f:else>
        </f:if>
    </ul>
    <div class="clearfix"></div>
</nav>
