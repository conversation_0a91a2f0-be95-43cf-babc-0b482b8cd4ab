<?php
namespace Redkiwi\TalismanExampleTrip\Domain\Model;

/**
 * Timeline model
 *
 * @package Redkiwi\TalismanExampleTrip\Domain\Model
 */
class Timeline extends \TYPO3\CMS\Extbase\DomainObject\AbstractEntity
{
    /** @var string */
    protected $title;

    /** @var bool */
    protected $enablePopup;

    /** @var string */
    protected $description;

    /** @var string */
    protected $icon;

    /** @var string */
    protected $style;

    /**
     * @return string
     */
    public function getTitle()
    {
        return $this->title;
    }

    /**
     * @param string $title
     * @return void
     */
    public function setTitle($title)
    {
        $this->title = $title;
    }

    /**
     * @return bool
     */
    public function isEnablePopup()
    {
        return $this->enablePopup;
    }

    /**
     * @param bool $enablePopup
     * @return void
     */
    public function setEnablePopup($enablePopup)
    {
        $this->enablePopup = $enablePopup;
    }

    /**
     * @return string
     */
    public function getDescription()
    {
        return $this->description;
    }

    /**
     * @param string $description
     * @return void
     */
    public function setDescription($description)
    {
        $this->description = $description;
    }

    /**
     * @return string
     */
    public function getIcon()
    {
        return $this->icon;
    }

    /**
     * @param string $icon
     * @return void
     */
    public function setIcon($icon)
    {
        $this->icon = $icon;
    }

    /**
     * @return string
     */
    public function getStyle()
    {
        return $this->style;
    }

    /**
     * @param string $style
     * @return void
     */
    public function setStyle($style)
    {
        $this->style = $style;
    }
}
