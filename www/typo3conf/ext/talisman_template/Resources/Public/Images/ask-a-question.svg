<?xml version="1.0" encoding="UTF-8"?>
<svg width="134px" height="128px" viewBox="0 0 134 128" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <!-- Generator: Sketch 53.2 (72643) - https://sketchapp.com -->
    <title>CONTACT/ VRAAG</title>
    <desc>Created with Sketch.</desc>
    <defs>
        <filter x="-28.7%" y="-29.7%" width="157.4%" height="159.3%" filterUnits="objectBoundingBox" id="filter-1">
            <feGaussianBlur stdDeviation="9" in="SourceGraphic"></feGaussianBlur>
        </filter>
    </defs>
    <g id="Page-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="UW-REIS-IN-DETAIL---Reisschema-(Chronologisch)" transform="translate(-39.000000, -819.000000)">
            <g id="CONTACT/-VRAAG" transform="translate(59.000000, 839.000000)">
                <path d="M47,82.4290375 C72.9573832,82.4290375 94,63.976669 94,41.2145188 C94,18.4523686 72.9573832,0 47,0 C21.0426168,0 0,18.4523686 0,41.2145188 C0,55.7731994 8.60829648,68.5687972 21.6049843,75.9008576 C23.1046584,76.7468963 21.0815647,81.7799438 15.5357031,91 C33.9080497,85.286025 44.3961487,82.4290375 47,82.4290375 Z" id="Oval" fill-opacity="0.295072115" fill="#000000" filter="url(#filter-1)"></path>
                <path d="M47,82.4290375 C72.9573832,82.4290375 94,63.976669 94,41.2145188 C94,18.4523686 72.9573832,0 47,0 C21.0426168,0 0,18.4523686 0,41.2145188 C0,55.7731994 8.60829648,68.5687972 21.6049843,75.9008576 C23.1046584,76.7468963 21.0815647,81.7799438 15.5357031,91 C33.9080497,85.286025 44.3961487,82.4290375 47,82.4290375 Z" id="Oval-Copy" fill="#FF0078"></path>
                <text id="Vragen?" font-family="Avenir-Heavy, Avenir" font-size="16" font-weight="600" line-spacing="13" fill="#FFFFFF">
                    <tspan x="16" y="43">Vragen?</tspan>
                </text>
                <text id="Stel-ze-hier" font-family="Avenir-Medium, Avenir" font-size="12" font-weight="400" line-spacing="13" fill="#FFFFFF">
                    <tspan x="16" y="58">Stel ze hier</tspan>
                </text>
            </g>
        </g>
    </g>
</svg>