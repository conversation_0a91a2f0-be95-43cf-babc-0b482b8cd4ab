.tx-solr {

    .tx-solr-pagination-page-browser {
        margin-bottom: 160px;
    }

    .tx-solr-searchbox {
        margin-bottom: 60px;
    }

    h2 {
        font-size: 32px;
        line-height: 40px;
    }

    h3 {
        @include custom-font-family('Nunito', 600);
        text-transform: uppercase;
        font-size: 15px;
        margin-top: 40px;
        margin-bottom: 30px;
    }

    .search-wrapper {
        width: 100%;
    }

    .tx-solr-q {
        @include custom-font-family('Nunito', 400);
        height: 43px;
        width: 100%;
        font-size: 15px;
        padding-left: 15px;
        color: $search-form-color;

        &:active, &:focus {
            border: 1px solid $search-form-border-color;
        }
    }

    .tx-solr-submit {
        height: 43px;
    }

    #tx-solr-faceting {
        margin-bottom: 40px;

        .facet-option {
            display: inline-block;
            width: 33%;
        }

        .checkbox {

            label {
                @include custom-font-family('Nunito', 400);
            }
        }
    }

    ol {
        list-style: none;
        padding-left: 0;

        &.results-list {

            .row {
                margin-top: 40px;
            }

            p {
                @include custom-font-family('Nunito', 400);
                font-size: 17px;
                margin-bottom: 0;

                &.result-link {
                    color: $search-form-color;
                }
            }

            h5 {
                margin: 0;
                font-weight: 400;

                &.results-topic {
                    font-size: 22px;
                }
            }
        }
    }

    .tx-solr-pagination-page-browser {
        padding-top: 50px;
    }
}

ul {

    &.ui-autocomplete {
        padding-left: 15px;
        border: 1px solid $search-form-border-color;
        border-top: none;
        position: absolute;
        background-color: $white;
        box-shadow: 2px 2px 2px $search-form-border-color;
        z-index: $zindex-heaviest;

        li {
            list-style: none;
            margin-bottom: 4px;

            &:hover {
                cursor: pointer;
            }

            a {
                @include custom-font-family('Nunito', 600);
                font-size: 15px;
                color: $black;

                &:hover {
                    text-decoration: none;
                }
            }
        }
    }
}

@media (max-width: $screen-xs-max) {
    .tx-solr {

        ol {

            &.results-list {

                img {
                    margin-bottom: 15px;
                }
            }
        }

        #tx-solr-faceting {

            .facet-option {
                width: 50%;
            }
        }

        .tx-solr-pagination-page-browser {

            ul {

                li {
                    height: 30px;
                    width: 30px;
                    margin: 2px;
                    padding: 2px;
                }
            }
        }

        .btn {
            width: 100%;
            margin-top: 5px;
        }
    }
}
