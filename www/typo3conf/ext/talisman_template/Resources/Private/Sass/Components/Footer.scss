footer {
    position: relative;
    z-index: 1300;

    ul {

        li {

            span {
                @include custom-font-family('Nunito', 600);
                font-size: 14px;
                color: $footer-header-link;
                text-transform: uppercase;
            }

            ul {
                margin-top: 5px;
                margin-bottom: 38px;

                li {

                    a {
                        @include custom-font-family('Nunito', 400);
                        font-size: 14px;
                        color: $black;
                        text-transform: none;
                        line-height: 25px;

                        &:hover, &:active, &:focus {
                            color: $black;
                            text-decoration: none;
                        }
                    }
                }
            }
        }
    }

    .footer-menu {
        background: $footer-background;
        padding-top: 41px;
        padding-bottom: 76px;

        .container {
            min-height: 707px;
        }

        .footer-menu-header {
            @include custom-font-family('Nunito', 600);
            @include responsive-font-property(30);
            color: $black;
            text-transform: uppercase;
            display: inline-block;
            margin-bottom: 20px;
        }
    }

    .footer-info {
        background: $black;
        padding-bottom: 60px;
        padding-top: 41px;

        .footer-info-header {
            @include custom-font-family('Nunito', 600);
            @include responsive-font-property(30);
            color: $white;
            text-transform: uppercase;
            display: inline-block;
            margin-bottom: 23px;
        }

        p {
            @include custom-font-family('Nunito', 400);
            font-size: 14px;
            line-height: 18px;
            color: $white;
            margin-bottom: 30px;

            a {
                color: $white;

                &:hover, &:active, &:focus {
                    color: $white;
                    text-decoration: none;
                }
            }
        }

        a {
            margin-right: 20px;

            &:last-child {
                margin-right: 0;
            }

            &:hover, &:focus, &:active {
                text-decoration: none;
            }
        }

        ul {

            li {

                ul {

                    li {

                        a {
                            color: $white;

                            &:hover, &:active, &:focus {
                                color: $white;
                                text-decoration: none;
                            }
                        }
                    }
                }
            }
        }
    }

    .footer-copyright {
        padding-top: 34px;
        padding-bottom: 66px;

        span {
            @include custom-font-family('Nunito', 400);
            font-size: 13px;
            color: $black;
            display: inline-block;

            &.separator {
                margin-left: 5px;
                margin-right: 6px;
            }

            a {
                color: $black;

                &:hover, &:active, &:focus {
                    color: $black;
                    text-decoration: none;
                }
            }
        }

        img {
            margin-top: -12px;
        }
    }

    .talisman-experts {

        &.widget {
            margin-top: 6px;
        }
    }

    .__fbcw__bar-widget {
        justify-content: flex-start !important;
        margin-top: 24px;
        padding: 0 !important;
    }
}


//
// Add hovers to lg
//
@media (min-width: $screen-lg-min) {

    footer {

        ul {

            li {

                ul {

                    li {

                        a {

                            &:hover, &:active, &:focus {
                                color: $black;
                                text-decoration: underline;
                            }
                        }
                    }
                }
            }
        }

        .footer-info {

            p {

                a {

                    &:hover, &:focus, &:active {
                        text-decoration: underline;
                        color: $white;
                    }
                }
            }

            ul {

                li {

                    ul {

                        li {

                            a {

                                &:hover, &:active, &:focus {
                                    text-decoration: underline;
                                    color: $white;
                                }
                            }
                        }
                    }
                }
            }
        }

        .footer-copyright {

            span {

                a {

                    &:hover, &:focus, &:active {
                        color: $black;
                        text-decoration: underline;
                    }
                }
            }
        }
    }
}

//
// Change the font size of the menu's on md
//
@media (min-width: $screen-md-min) and (max-width: $screen-md-max) {

    footer {

        .footer-menu {

            ul {

                li {

                    span {
                        font-size: 13px;
                    }
                }
            }
        }
    }
}

//
// Change the font size of the menu's on sm
//
@media (min-width: $screen-sm-min) and (max-width: $screen-sm-max) {

    footer {

        .footer-menu {

            .col-sm-3:last-child {
                clear: left;
            }

            ul {

                li {

                    span {
                        font-size: 13px;
                    }

                    ul {

                        li {

                            a {
                                font-size: 13px;
                            }
                        }
                    }
                }
            }
        }
    }
}

//
// Styling adjustments for xs
//
@media (max-width: $screen-xs-max) {

    footer {

        .footer-info-header, .footer-menu-header {
            width: 100%;
        }

        .footer-info {

            .footer-info-header {

                &.description-header {
                    margin-top: 50px;
                }
            }

            a {
                margin-right: 10px;
            }
        }

        .footer-menu {

            ul {
                margin-bottom: 50px;
            }

            .col-sm-4:nth-child(4) {
                clear: none;
            }

            .col-xs-6:nth-child(2n+3) {
                clear: left;
            }
        }

        .talisman-experts {

            &.widget {
                margin-top: 0;
            }
        }
    }
}
