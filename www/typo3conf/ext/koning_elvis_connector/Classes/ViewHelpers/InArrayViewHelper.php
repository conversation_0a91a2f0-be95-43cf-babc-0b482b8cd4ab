<?php
namespace Keizer\KoningElvisConnector\ViewHelpers;

/**
 * ViewHelper: in_array
 *
 * @package Keizer\KoningElvisConnector\ViewHelpers
 */
class InArrayViewHelper extends \TYPO3\CMS\Fluid\Core\ViewHelper\AbstractViewHelper
{
    /**
     * @return void
     */
    public function initializeArguments()
    {
        parent::initializeArguments();
        $this->registerArgument('array', 'array', 'The array to check.');
        $this->registerArgument('value', 'string', 'The value to check.');
    }

    /**
     * @return mixed
     */
    public function render()
    {
        return in_array($this->arguments['value'], $this->arguments['array']);
    }
}
