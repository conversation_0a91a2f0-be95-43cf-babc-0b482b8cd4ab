<f:layout name="Default" />

<f:section name="Content">
    <div class="nav-tabs-box with-border">
        <p><f:format.nl2br><f:translate id="form.show.intro" /></f:format.nl2br></p>
        <f:form class="form" additionalAttributes="{autocomplete: 'off'}">
            <div class="form-group">
                <div class="radio radio-inline">
                    <f:form.radio name="type" value="1" id="type-1" checked="{type} == 1" />
                    <label for="type-1"><f:translate id="form.show.type.1" /></label>
                </div>
                <div class="radio radio-inline">
                    <f:form.radio name="type" value="2" id="type-2" checked="{type} == 2" />
                    <label for="type-2"><f:translate id="form.show.type.2" /></label>
                </div>
            </div>
        </f:form>
        <div class="form-type form-type-1" style="display: {f:if(condition: '{type} == 1', then: 'block', else: 'none')};">
            <hr>
            <f:form action="handleAppointment" objectName="appointment" class="form" additionalAttributes="{autocomplete: 'off'}">
                <div class="row">
                    <div class="col-sm-6">
                        <h2><f:translate id="form.show.your_details" /></h2>

                        <f:render
                            partial="Form/TextField"
                            arguments="{
                                property: 'name',
                                formObjectName: 'appointment',
                                required: 1,
                                label: '{f:translate(id: \'form.show.name\')}'
                            }" />

                        <f:render
                            partial="Form/TextField"
                            arguments="{
                                property: 'email',
                                formObjectName: 'appointment',
                                required: 1,
                                label: '{f:translate(id: \'form.show.email\')}'
                            }" />

                        <f:render
                            partial="Form/TextField"
                            arguments="{
                                property: 'phone',
                                formObjectName: 'appointment',
                                required: 1,
                                label: '{f:translate(id: \'form.show.phone\')}'
                            }" />

                        <div class="heard-of-talisman-answer">
                            <f:render
                                    partial="Form/Select"
                                    arguments="{
                                property: 'known',
                                options: '{heardOfTalismanAnswers}',
                                prependOptionLabel: '{f:translate(key: \"LLL:EXT:talisman_template/Resources/Private/Language/locallang.xlf:form.choose_an_option\")}',
                            label: '{f:translate(id: \'form.show.known\')}',
                            additionalAttributes: '{data-parsley-trigger: \'change\', data-parsley-required: \'\', data-parsley-required-message: \'{f:translate(key: \"LLL:EXT:talisman_template/Resources/Private/Language/locallang.xlf:form.error.required_field\")}\', data-parsley-errors-container: \'.bootstrap-select\'}'
                            }" />

                            <div class="heard-of-talisman-other-answer" style="display: none;">
                                <f:render
                                        partial="Form/TextField"
                                        arguments="{
                                    property: 'known',
                                    formObjectName: 'appointment'
                            }" />
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-xs-12"><span class="comment"><f:translate id="form.show.appointment" /><span class="required">*</span></span></div>

                            <div class="col-md-6">
                                <f:render
                                        partial="Form/Select"
                                        arguments="{
                                    property: 'preferredReachDateAnswer',
                                    formObjectName: 'appointment',
                                    options: preferredReachDateAnswers,
                                    required: 1
                                }" />
                            </div>

                            <div class="col-md-6">
                                <div class="heard-of-talisman-answer">
                                    <f:render
                                            partial="Form/Select"
                                            arguments="{
                                        property: 'preferredReachTimeAnswer',
                                        formObjectName: 'appointment',
                                        options: preferredReachTimeAnswers,
                                        required: 1
                                    }" />

                                    <div class="heard-of-talisman-other-answer" style="display: none;">
                                        <f:render
                                                partial="Form/TextField"
                                                arguments="{
                                            property: 'preferredReachTimeOtherAnswer',
                                            formObjectName: 'appointment'
                                        }" />
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-sm-6 subject-container">
                        <h2><f:translate id="form.show.subject" /></h2>

                        <div class="form-group">
                            <label><f:translate id="form.show.destination" /> <span class="required">*</span></label>
                            <f:render
                                partial="Form/MultiSelect"
                                arguments="{
                                    formObjectName: 'appointment',
                                    options: destinations,
                                    property: 'destination',
                                    value: appointment.destination.uid,
                                    compact: 1,
                                    displayLabel: '{f:translate(id: \'form.show.choose_destination\')}'
                                }" />
                        </div>

                        <f:render
                            partial="Form/TextArea"
                            arguments="{
                                property: 'description',
                                formObjectName: 'appointment',
                                rows: 10,
                                cols: 10
                                label: '{f:translate(id: \'form.show.description\')}'
                            }" />
                    </div>
                </div>
                <div class="row submit-row">
                    <div class="col-sm-6 col-sm-push-6 text-right">
                        <button type="submit" class="btn btn-default btn-rubine pull-right">
                            <f:translate id="form.show.submit" />
                        </button>
                        <div class="clearfix"></div>
                    </div>
                    <div class="col-sm-6 col-sm-pull-6">
                        <span class="mandatory">* <f:translate id="form.label.mandatory" extensionName="TalismanTemplate" /></span>
                    </div>
                </div>
            </f:form>
        </div>
        <div class="form-type form-type-2" style="display: {f:if(condition: '{type} == 2', then: 'block', else: 'none')};">
            <hr>
            <f:form action="handlePhoneAppointment" objectName="phoneAppointment" class="form" additionalAttributes="{autocomplete: 'off'}">
                <div class="row">
                    <div class="col-sm-6">
                        <h2><f:translate id="form.show.your_details" /></h2>

                        <f:render
                            partial="Form/TextField"
                            arguments="{
                                property: 'name',
                                formObjectName: 'phoneAppointment',
                                required: 1,
                                label: '{f:translate(id: \'form.show.name\')}'
                            }" />

                        <f:render
                            partial="Form/TextField"
                            arguments="{
                                property: 'phone',
                                formObjectName: 'phoneAppointment',
                                required: 1,
                                label: '{f:translate(id: \'form.show.phone\')}'
                            }" />

                        <div class="heard-of-talisman-answer">
                            <f:render
                                    partial="Form/Select"
                                    arguments="{
                                property: 'known',
                                options: '{heardOfTalismanAnswers}',
                                prependOptionLabel: '{f:translate(key: \"LLL:EXT:talisman_template/Resources/Private/Language/locallang.xlf:form.choose_an_option\")}',
                            label: '{f:translate(id: \'form.show.known\')}',
                            additionalAttributes: '{data-parsley-trigger: \'change\', data-parsley-required: \'\', data-parsley-required-message: \'{f:translate(key: \"LLL:EXT:talisman_template/Resources/Private/Language/locallang.xlf:form.error.required_field\")}\', data-parsley-errors-container: \'.bootstrap-select\'}'
                            }" />

                            <div class="heard-of-talisman-other-answer" style="display: none;">
                                <f:render
                                        partial="Form/TextField"
                                        arguments="{
                                    property: 'known',
                                    formObjectName: 'phoneAppointment'
                            }" />
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-xs-12"><span class="comment"><f:translate id="form.show.phoneAppointment" /><span class="required">*</span></span></div>

                            <div class="col-md-6">
                                <f:render
                                        partial="Form/Select"
                                        arguments="{
                                        property: 'preferredReachDateAnswer',
                                        formObjectName: 'phoneAppointment',
                                        options: preferredReachDateAnswers,
                                        required: 1
                                    }" />
                            </div>

                            <div class="col-md-6">
                                <div class="heard-of-talisman-answer">
                                    <f:render
                                            partial="Form/Select"
                                            arguments="{
                                            property: 'preferredReachTimeAnswer',
                                            formObjectName: 'phoneAppointment',
                                            options: preferredReachTimeAnswers,
                                            required: 1
                                        }" />

                                    <div class="heard-of-talisman-other-answer" style="display: none;">
                                        <f:render
                                                partial="Form/TextField"
                                                arguments="{
                                                property: 'preferredReachTimeOtherAnswer',
                                                formObjectName: 'phoneAppointment'
                                            }" />
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-sm-6 subject-container">
                        <h2><f:translate id="form.show.subject" /></h2>

                        <f:render
                            partial="Form/TextArea"
                            arguments="{
                                property: 'description',
                                formObjectName: 'phoneAppointment',
                                rows: 10,
                                cols: 10
                                label: '{f:translate(id: \'form.show.description\')}'
                            }" />
                    </div>
                </div>
                <div class="row submit-row">
                    <div class="col-sm-6 col-sm-push-6 text-right">
                        <button type="submit" class="btn btn-default btn-rubine pull-right">
                            <f:translate id="form.show.submit" />
                        </button>
                        <div class="clearfix"></div>
                    </div>
                    <div class="col-sm-6 col-sm-pull-6">
                        <span class="mandatory">* <f:translate id="form.label.mandatory" extensionName="TalismanTemplate" /></span>
                    </div>
                </div>
            </f:form>
        </div>
    </div>
</f:section>
