<div class="result-title"><b>#field('title')</b><span class="mlt">#if($params.getBool('mlt', false) == false)<a href="#lensNoQ&q=id:$docId&mlt=true">More Like This</a>#end</span></div>
<div>Content: #field('content')</div>
<div>Keywords: #field('keywords')</div>
<div>Author: #field('author')</div>
<div class="mlt">
  #set($mlt = $mltResults.get($docId))
  #set($mltOn = $params.getBool('mlt'))
  #if($mltOn == true)<div class="field-name">Similar Items</div>#end
  #if ($mltOn && $mlt && $mlt.size() > 0)
  <ul>
    #foreach($mltHit in $mlt)
      #set($mltId = $mltHit.getFieldValue('id'))
      <li><div><a href="#url_for_home?q=id:$mltId">$mltId</a></div>
        <div><span class="field-name">Title:</span> $mltHit.getFieldValue('title')</div>
        <div><span class="field-name">Content:</span> $mltHit.getFieldValue('content')
      </li>
    #end
  </ul>
  #elseif($mltOn && $mlt.size() == 0)
    <div>No Similar Items Found</div>
  #end
</div>
#if($params.getBool("debugQuery",false))
  <a href="#" onclick='jQuery(this).siblings("pre").toggle(); return false;'>toggle explain</a>
  <pre style="display:none">$response.getExplainMap().get($doc.getFirstValue('id'))</pre>
  <a href="#" onclick='jQuery(this).siblings("pre2").toggle(); return false;'>toggle all fields</a>
  <pre2 style="display:none">
  #foreach($fieldname in $doc.fieldNames)
     <br>
       <span class="field-name">$fieldname :</span>
       <span>
       #foreach($value in $doc.getFieldValues($fieldname))
         $value
       #end
       </span>
  #end
   </br>
  </pre2>
#end