<?php
namespace Redkiwi\TalismanUserRegistration\Domain\DataTransfer;

/**
 * Data transfer: Forgot password
 *
 * @package Redkiwi\TalismanUserRegistration\Domain\DataTransfer
 */
class ForgotPassword
{
    /**
     * @var string
     * @validate NotEmpty, <PERSON>ailAddress
     */
    protected $email;

    /**
     * @return string
     */
    public function getEmail()
    {
        return $this->email;
    }

    /**
     * @param string $email
     * @return void
     */
    public function setEmail($email)
    {
        $this->email = $email;
    }
}
