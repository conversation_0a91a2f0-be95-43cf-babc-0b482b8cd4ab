<html xmlns="http://www.w3.org/1999/xhtml" lang="en"
	  xmlns:f="http://typo3.org/ns/TYPO3/Fluid/ViewHelpers"
	  xmlns:s="http://typo3.org/ns/ApacheSolrForTypo3/Solr/ViewHelpers">

<f:section name="Facets">
	<div id="tx-solr-faceting">
		<div id="tx-solr-facets-available" class="secondaryContentSection">
			<s:facet.area.group groupName="main" facets="{resultSet.facets.available}">
				<div class="facet-area-main">
					<div class="solr-facets-available secondaryContentSection">
						<div class="facets">
							<f:for each="{areaFacets}" as="facet">
								<div class="facet facet-type facet-type-{facet.type} panel-group" id="facet-accordion-{facet.name}">
									<f:render partial="Facets/{facet.partialName}" arguments="{resultSet:resultSet, facet:facet}"/>
								</div>
							</f:for>
						</div>
					</div>
				</div>
			</s:facet.area.group>
		</div>
	</div>
</f:section>
