//
// Talisman circle menu styling
//
.talisman-content-circle-menu {

    a {
        display: block;
        margin: 0 auto 30px;
        max-width: 230px;

        figure {
            position: relative;

            figcaption {
                @include custom-font-family('Nunito', 600);
                position: absolute;
                top: 50%;
                width: 100%;
                text-align: center;
                text-transform: uppercase;
                color: $white;
                font-size: 22px;
                display: block;
                transform: translate(0px, -50%);
                opacity: 1;
                z-index: $zindex-default;
            }
        }

        .effect {
            border-radius: 50%;
            background: $black;
            position: absolute;
            top: 0;
            left: 0;
            bottom: 0;
            right: 0;
            opacity: 0.2;
            z-index: $zindex-lighter;
            transition: all 0.4s ease 0s;
        }
    }
}

//
// In the left column, add block margin styling
//
.left-column {

    .talisman-content-circle-menu {

        &.block-margin-1 {
            margin-bottom: 30px;
        }
    }
}

//
// Add hovers to lg
//
@media (min-width: $screen-lg-min) {

    .talisman-content-circle-menu {

        a {

            &:hover {

                .effect {
                    background: $rubine;
                    opacity: 0.8;
                }
            }
        }
    }
}
