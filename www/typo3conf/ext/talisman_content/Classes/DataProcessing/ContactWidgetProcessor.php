<?php
namespace Redkiwi\TalismanContent\DataProcessing;

use TYPO3\CMS\Frontend\ContentObject\ContentObjectRenderer;
use TYPO3\CMS\Frontend\ContentObject\DataProcessorInterface;

/**
 * Data processor for contact widget
 *
 * @package Redkiwi\TalismanContent\DataProcessing
 */
class ContactWidgetProcessor implements DataProcessorInterface
{
    /**
     * @param ContentObjectRenderer $cObj
     * @param array $contentObjectConfiguration
     * @param array $processorConfiguration
     * @param array $processedData
     * @return array
     */
    public function process(
        ContentObjectRenderer $cObj,
        array $contentObjectConfiguration,
        array $processorConfiguration,
        array $processedData
    )
    {
        if (isset($processorConfiguration['settings.']['pages.']['askAQuestion'])) {
            $processedData['askAQuestionPageUid'] = (int) $processorConfiguration['settings.']['pages.']['askAQuestion'];
        }
        if (isset($processorConfiguration['settings.']['pages.']['phoneAppointment'])) {
            $processedData['phoneAppointmentPageUid'] = (int) $processorConfiguration['settings.']['pages.']['phoneAppointment'];
        }
        if (isset($processorConfiguration['settings.']['pages.']['visit'])) {
            $processedData['visitPageUid'] = (int) $processorConfiguration['settings.']['pages.']['visit'];
        }
        return $processedData;
    }
}
