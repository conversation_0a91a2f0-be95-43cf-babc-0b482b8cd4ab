plugin.tx_solr {
    view {
        templateRootPaths {
            10 = EXT:talisman_template/Resources/Private/Extensions/Solr/Templates
        }
        partialRootPaths {
            10 = EXT:talisman_template/Resources/Private/Extensions/Solr/Partials
            20 = EXT:talisman_template/Resources/Private/Partials
        }
        layoutRootPaths {
            10 = EXT:talisman_template/Resources/Private/Extensions/Solr/Layouts
            20 = EXT:talisman_template/Resources/Private/Layouts
        }

        templateFiles {
            results = Results
            results.availableTemplates {
                search {
                    label = Search
                    file = Search
                }
            }
        }
    }

    javascriptFiles >

    index {
        queue {
            pages {
                allowedPageTypes = 1,7,4,{$dokTypes.accommodation},{$dokTypes.travelogue},{$dokTypes.blog},{$dokTypes.exampleTrip},{$dokTypes.tour},{$dokTypes.excursion}

                additionalWhereClause = (doktype = 1 OR doktype=4 OR doktype = {$dokTypes.tour} OR doktype = {$dokTypes.excursion} OR doktype = {$dokTypes.accommodation} OR doktype= {$dokTypes.travelogue} OR doktype = {$dokTypes.blog} OR doktype = {$dokTypes.exampleTrip} OR (doktype=7 AND mount_pid_ol=0)) AND no_search = 0

                fields {
                    doktype_tIntS = doktype
                    image_stringS = FILES
                    image_stringS {
                        references {
                            table = pages
                            uid.field = uid
                            fieldName = media
                        }

                        renderObj = TEXT
                        renderObj.data = file:current:publicUrl
                    }
                }
            }
        }
    }

    search {

        faceting = 1
        faceting {
            showEmptyFacets = 1
            keepAllFacetsOnSelection = 1

            facets {

                type >

                doktype {
                    label =
                    field = doktype_tIntS
                    operator = OR
                    renderingInstruction = CASE
                    renderingInstruction {
                        key.field = optionValue

                        default = TEXT
                        default.value = Pagina's

                        {$dokTypes.accommodation} = TEXT
                        {$dokTypes.accommodation}.value = Accommodaties

                        {$dokTypes.travelogue} = TEXT
                        {$dokTypes.travelogue}.value = Reisverslagen

                        {$dokTypes.tour} = TEXT
                        {$dokTypes.tour}.value = Tours

                        {$dokTypes.excursion} = TEXT
                        {$dokTypes.excursion}.value = Excursies

                        {$dokTypes.blog} = TEXT
                        {$dokTypes.blog}.value = Blog

                        {$dokTypes.exampleTrip} = TEXT
                        {$dokTypes.exampleTrip}.value = Routesuggesties
                    }
                }
            }
        }

        spellchecking = 1
    }

    _LOCAL_LANG {
        nl {
            faceting_narrowSearch = Resultaten filteren op
            results_found = @resultsTotal zoekresultaten voor
            results_searched_for = "@searchWord"
            didYouMean = Bedoelde je
            error_errors = Fout
            error_emptyQuery = Vul een zoekterm in.
        }
    }
}