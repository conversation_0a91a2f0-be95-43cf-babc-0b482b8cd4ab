<?php
namespace Redkiwi\TalismanEsbConnector\Service\Esb;
use Redkiwi\TalismanEsbConnector\Utility\ConfigurationUtility;

/**
 * Service: ESB service
 *
 * @package Redkiwi\TalismanEsbConnector\Esb\Service
 */
abstract class AbstractConnectorService
{
    /**
     * @param array $parameters
     * @return array
     */
    abstract function makeRequest(array $parameters = []);

    /**
     * @param string $service
     * @param array $parameters
     * @return array
     * @throws \Exception
     */
    protected function request($service, array $parameters = [])
    {
        if (ConfigurationUtility::isValid()) {
            $configuration = ConfigurationUtility::getEsbSettings();

            $requestBody = $this->generateRequestBody($configuration['apiKey'], $parameters);
            $requestSignature = $this->generateRequestSignature($configuration['privateKey'], $requestBody);

            $curl = curl_init();
            curl_setopt_array($curl, [
                CURLOPT_RETURNTRANSFER => 1,
                CURLOPT_URL => $configuration['baseUrl'] . $service,
                CURLOPT_USERAGENT => 'Talisman ESB',
                CURLOPT_POST => 1,
                CURLOPT_POSTFIELDS => [
                    'request' => $requestBody,
                    'signature' => $requestSignature
                ]
            ]);

            $response = curl_exec($curl);
            $info = curl_getinfo($curl);

            curl_close($curl);

            return [
                'response' => $response,
                'info' => $info
            ];
        } else {
            throw new \Exception('Configuration for talisman_esb_connector invalid');
        }
    }

    /**
     * @param string $apiKey
     * @param array $parameters
     * @return string
     */
    protected function generateRequestBody($apiKey, array $parameters = [])
    {
        $xml = new \SimpleXMLElement('<?xml version="1.0" encoding="utf-8"?><request></request>');
        $xml->addChild('apiKey', $apiKey);

        $xmlParameters = $xml->addChild('parameters');
        foreach ($parameters as $parameter => $value) {
            $xmlParameters->addChild($parameter, $value);
        }
        return $xml->asXML();
    }

    /**
     * @param string $requestBody
     * @param string $privateKey
     * @return string
     */
    protected function generateRequestSignature($privateKey, $requestBody)
    {
        $signature = '';
        openssl_sign($requestBody, $signature, $privateKey);
        return base64_encode($signature);
    }
}
