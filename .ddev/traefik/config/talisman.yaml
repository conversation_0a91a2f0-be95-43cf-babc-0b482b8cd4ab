#ddev-generated
# If you remove the ddev-generated line above you
# are responsible for maintaining this file. DDEV will not then
# update it, for example if you add `additional_hostnames`, etc.

http:
  routers:
    talisman-solr-8983-http:
      entrypoints:
        - http-8983
      rule: HostRegexp(`^talisman\.ddev\.site$`)|| HostRegexp(`^talisman\.nl\.local$`)|| HostRegexp(`^www\.talisman\.nl\.local$`)
      
      service: "talisman-solr-8983"
      ruleSyntax: v3
      tls: false
      # middlewares:
      #   - "talisman-redirectHttps"
    talisman-web-80-http:
      entrypoints:
        - http-80
      rule: HostRegexp(`^talisman\.ddev\.site$`)|| HostRegexp(`^talisman\.nl\.local$`)|| HostRegexp(`^www\.talisman\.nl\.local$`)
      
      service: "talisman-web-80"
      ruleSyntax: v3
      tls: false
      # middlewares:
      #   - "talisman-redirectHttps"
    talisman-web-8025-http:
      entrypoints:
        - http-8025
      rule: HostRegexp(`^talisman\.ddev\.site$`)|| HostRegexp(`^talisman\.nl\.local$`)|| HostRegexp(`^www\.talisman\.nl\.local$`)
      
      service: "talisman-web-8025"
      ruleSyntax: v3
      tls: false
      # middlewares:
      #   - "talisman-redirectHttps"
    talisman-xhgui-80-http:
      entrypoints:
        - http-8143
      rule: HostRegexp(`^talisman\.ddev\.site$`)|| HostRegexp(`^talisman\.nl\.local$`)|| HostRegexp(`^www\.talisman\.nl\.local$`)
      
      service: "talisman-xhgui-80"
      ruleSyntax: v3
      tls: false
      # middlewares:
      #   - "talisman-redirectHttps"
    
    
    
    
    talisman-web-80-https:
      entrypoints:
        - http-443
      rule: HostRegexp(`^talisman\.ddev\.site$`) || HostRegexp(`^talisman\.nl\.local$`) || HostRegexp(`^www\.talisman\.nl\.local$`)
      
      service: "talisman-web-80"
      ruleSyntax: v3
      
      tls: true
      
    talisman-web-8025-https:
      entrypoints:
        - http-8026
      rule: HostRegexp(`^talisman\.ddev\.site$`) || HostRegexp(`^talisman\.nl\.local$`) || HostRegexp(`^www\.talisman\.nl\.local$`)
      
      service: "talisman-web-8025"
      ruleSyntax: v3
      
      tls: true
      
    
    talisman-xhgui-80-https:
      entrypoints:
        - http-8142
      rule: HostRegexp(`^talisman\.ddev\.site$`) || HostRegexp(`^talisman\.nl\.local$`) || HostRegexp(`^www\.talisman\.nl\.local$`)
      
      service: "talisman-xhgui-80"
      ruleSyntax: v3
      
      tls: true
      
    

  middlewares:
    talisman-redirectHttps:
      redirectScheme:
        scheme: https
        permanent: true

  services:
    talisman-solr-8983:
      loadbalancer:
        servers:
          - url: http://ddev-talisman-solr:8983
        
    talisman-web-80:
      loadbalancer:
        servers:
          - url: http://ddev-talisman-web:80
        
    talisman-web-8025:
      loadbalancer:
        servers:
          - url: http://ddev-talisman-web:8025
        
    
    
    talisman-xhgui-80:
      loadbalancer:
        servers:
          - url: http://ddev-talisman-xhgui:80
        
    
    

tls:
  certificates:
    - certFile: /mnt/ddev-global-cache/traefik/certs/talisman.crt
      keyFile: /mnt/ddev-global-cache/traefik/certs/talisman.key