<f:layout name="Default" />

<f:section name="Content">

    <div class="talisman-blog-related">
        <h2><PERSON><PERSON>lateerd</h2>
        <div class="inner">
            <div class="row">
                <f:for each="{pages}" as="blogPost" iteration="i">
                    <div class="col-sm-3">
                        <f:if condition="{blogPost.images.0}">
                            <figure>
                                <f:link.page pageUid="{blogPost.uid}">
                                    <f:image image="{blogPost.images.0}" alt="{blogPost.title}" class="visual img-responsive" height="176c" width="292" />
                                </f:link.page>
                            </figure>
                            <f:link.page pageUid="{blogPost.uid}">
                                {blogPost.title}
                            </f:link.page>
                            <span class="post-specs">
                                <f:translate key="blogpost.posted_on" />: <time><f:format.date format="%e %B, %Y">{blogPost.tstamp}</f:format.date></time>
                            </span>
                        </f:if>
                    </div>
                </f:for>
            </div>
        </div>
    </div>
</f:section>
