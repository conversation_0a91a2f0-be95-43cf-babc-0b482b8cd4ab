<?php

use Redkiwi\TalismanPage\Domain\Model\Page;
use Red<PERSON>wi\TalismanPage\Domain\Model\CategoryRating;
use TYPO3\CMS\Core\Utility\ArrayUtility;
use TYPO3\CMS\Core\Utility\ExtensionManagementUtility;
use TYPO3\CMS\Core\Utility\GeneralUtility;
use TYPO3\CMS\Frontend\Page\PageRepository;

if (!defined('TYPO3_MODE')) {
    die('Access denied.');
}

call_user_func(function ($extension, $table) {
    $languageFile = 'LLL:EXT:' . $extension . '/Resources/Private/Language/locallang_be.xlf';

    ExtensionManagementUtility::addTCAcolumns(
        $table,
        [
            'crdate' => [
                'config' => [
                    'type' => 'passthrough'
                ]
            ],
            'tstamp' => [
                'config' => [
                    'type' => 'passthrough'
                ]
            ],
            'tx_talismanpage_static_logo' => [
                'exclude' => true,
                'label' => $languageFile . ':pages.tx_talismanpage_static_logo',
                'config' => [
                    'type' => 'check'
                ]
            ],

            'tx_talismanpage_example_trip_name' => [
                'exclude' => true,
                'label' => $languageFile . ':pages.tx_talismanpage_example_trip_name',
                'config' => [
                    'type' => 'input'
                ]
            ],

            // talisman_inspirations
            'tx_talismanpage_include_in_inspiration' => [
                'exclude' => true,
                'label' => $languageFile . ':pages.tx_talismanpage_include_in_inspiration',
                'config' => [
                    'type' => 'check'
                ]
            ],
            'tx_talismanpage_include_in_private' => [
                'exclude' => true,
                'label' => $languageFile . ':pages.tx_talismanpage_include_in_private',
                'config' => [
                    'type' => 'check'
                ]
            ],
            'tx_talismanpage_include_in_specials' => [
                'exclude' => true,
                'label' => $languageFile . ':pages.tx_talismanpage_include_in_specials',
                'config' => [
                    'type' => 'check'
                ]
            ],

            'tx_talismanpage_hide_submenu' => [
                'exclude' => true,
                'label' => $languageFile . ':pages.tx_talismanpage_hide_submenu',
                'config' => [
                    'type' => 'check'
                ]
            ],

            'tx_talismanpage_iso_code' => [
                'exclude' => true,
                'label' => $languageFile . ':pages.tx_talismanpage_isocode',
                'config' => [
                    'type' => 'input'
                ]
            ],
            'tx_talismanpage_category_ratings' => [
                'exclude' => true,
                'label' => $languageFile . ':pages.tx_talismanpage_category_ratings',
                'displayCond' => [
                    'OR' => [
                        'FIELD:tx_talismanpage_include_in_inspiration:REQ:true',
                        'FIELD:tx_talismanpage_include_in_private:REQ:true',
                        'FIELD:tx_talismanpage_include_in_specials:REQ:true'
                    ]
                ],
                'config' => [
                    'type' => 'inline',
                    'foreign_table' => CategoryRating::TABLE,
                    'foreign_field' => 'page_id',
                    'foreign_sortby' => 'sorting',
                    'maxitems' => '9999',
                    'appearance' => [
                        'collapseAll' => true,
                        'expandSingle' => true,
                        'useSortable' => true,
                        'enabledControls' => true,
                        'createNewRelationLinkTitle' => 'LLL:EXT:cms/locallang_ttc.xlf:media.addFileReference',
                        'newRecordLinkAddTitle' => true,
                        'levelLinksPosition' => 'top',
                        'showSynchronizationLink' => false,
                        'showAllLocalizationLink' => true,
                        'showPossibleLocalizationRecords' => true,
                        'showRemovedLocalizationRecords' => true,
                    ]
                ],
            ],
            'tx_talismanpage_hide_breadcrumbs' => [
                'exclude' => true,
                'label' => $languageFile . ':pages.tx_talismanpage_enable_breadcrumbs',
                'config' => [
                    'type' => 'check'
                ],
            ],
        ]
    );

    ExtensionManagementUtility::addFieldsToPalette(
        $table,
        'tx_talismanpage_talisman_inspiration',
        'tx_talismanpage_include_in_inspiration, tx_talismanpage_include_in_specials, tx_talismanpage_include_in_private,  --linebreak--, tx_talismanpage_category_ratings'
    );

    ExtensionManagementUtility::addToAllTCAtypes(
        $table,
        'tx_talismanpage_static_logo',
        (string)PageRepository::DOKTYPE_DEFAULT,
        'after:backend_layout'
    );

    ExtensionManagementUtility::addToAllTCAtypes(
        $table,
        'tx_talismanpage_hide_breadcrumbs',
        (string)PageRepository::DOKTYPE_DEFAULT,
        'after:backend_layout'
    );

    ExtensionManagementUtility::addToAllTCAtypes(
        $table,
        'tx_talismanpage_iso_code, tx_talismanpage_hide_submenu',
        '',
        'after:subtitle'
    );

    ExtensionManagementUtility::addToAllTCAtypes(
        $table,
        'tx_talismanpage_example_trip_name',
        '',
        'after:abstract'
    );

    ExtensionManagementUtility::addToAllTCAtypes(
        $table,
        '--palette--;' . $languageFile . ':pages.palette.talisman_inspiration;tx_talismanpage_talisman_inspiration',
        (string)PageRepository::DOKTYPE_DEFAULT,
        'after:categories'
    );

    $GLOBALS['TCA'][$table]['ctrl']['requestUpdate'] .= ', tx_talismanpage_include_in_inspiration';

    // Add new page doktypes
    $dokTypesToAdd = [
        Page::DOKTYPE_ACCOMMODATION => [
            'icon' => GeneralUtility::getFileAbsFileName("EXT:{$extension}/Resources/Public/Icons/accommodation.png"),
            'iconIdentifier' => 'apps-pagetree-accommodation',
            'label' => $languageFile . ':pages.doktype.accommodation'
        ],
        Page::DOKTYPE_TOUR => [
            'icon' => GeneralUtility::getFileAbsFileName("EXT:{$extension}/Resources/Public/Icons/tour.png"),
            'iconIdentifier' => 'apps-pagetree-tour',
            'label' => $languageFile . ':pages.doktype.tour'
        ],
        Page::DOKTYPE_EXCURSION => [
            'icon' => GeneralUtility::getFileAbsFileName("EXT:{$extension}/Resources/Public/Icons/excursion.png"),
            'iconIdentifier' => 'apps-pagetree-excursion',
            'label' => $languageFile . ':pages.doktype.excursion'
        ],
        Page::DOKTYPE_TRAVELOGUE => [
            'icon' => GeneralUtility::getFileAbsFileName("EXT:{$extension}/Resources/Public/Icons/travelogue.png"),
            'iconIdentifier' => 'apps-pagetree-travelogue',
            'label' => $languageFile . ':pages.doktype.travelogue'
        ],
    ];

    foreach ($dokTypesToAdd as $dokType => $info) {
        ExtensionManagementUtility::addTcaSelectItem(
            $table,
            'doktype',
            [
                $info['label'],
                $dokType,
                $info['icon']
            ],
            (string)PageRepository::DOKTYPE_DEFAULT,
            'after'
        );

        ArrayUtility::mergeRecursiveWithOverrule(
            $GLOBALS['TCA'][$table],
            [
                'ctrl' => [
                    'typeicon_classes' => [
                        $dokType => $info['iconIdentifier'],
                    ],
                ],
            ]
        );
    }
}, 'talisman_page', 'pages');
