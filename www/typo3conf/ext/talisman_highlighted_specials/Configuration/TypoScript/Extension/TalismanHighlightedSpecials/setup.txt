lib.talismanHighlightedSpecialsContentElement =< lib.contentElement
lib.talismanHighlightedSpecialsContentElement {
    templateRootPaths {
        10 = {$plugin.tx_talismanhighlightedspecials.view.templateRootPath}
    }

    partialRootPaths {
        10 = {$plugin.tx_talismanhighlightedspecials.view.partialRootPath}
    }

    layoutRootPaths {
        10 = {$plugin.tx_talismanhighlightedspecials.view.layoutRootPath}
    }

    dataProcessing {
        100 = Redkiwi\TalismanHighlightedSpecials\DataProcessing\HighlightedSpecialDataProcessor
    }

    settings < talisman_template
}

plugin.tx_talismanhighlightedspecials {
    settings < talisman_template
}

tt_content {

    talismanhighlightedspecials =< lib.talismanHighlightedSpecialsContentElement
    talismanhighlightedspecials {
        templateName = HighlightedSpecials
    }
}