<f:link.typolink parameter="{tile.link}" class="cards with-price">
    <figure>
        <f:if condition="{tile.visual.uid}">
            <f:image src="{f:uri.image(image: tile.visual)}" alt="" height="320c" width="560"/>
        </f:if>
        <figcaption>
            <h2 class="left">{tile.title}</h2>
            <p>
                <f:format.nl2br>{tile.subtitle}</f:format.nl2br>
            </p>
            <p class="price">
                <f:format.nl2br>{tile.price}</f:format.nl2br>
            </p>
        </figcaption>
    </figure>
</f:link.typolink>