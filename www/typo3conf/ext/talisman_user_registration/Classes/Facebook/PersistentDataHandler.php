<?php
namespace Redkiwi\TalismanUserRegistration\Facebook;

use Facebook\PersistentData\PersistentDataInterface;

/**
 * Facebook: Persistent data handler
 *
 * @package Redkiwi\TalismanUserRegistration\Facebook
 */
class PersistentDataHandler implements PersistentDataInterface
{
    /** @var string */
    protected $sessionPrefix = 'FBRLH_';

    /**
     * @param $key
     * @return string
     */
    public function get($key)
    {
        return $this->getFrontendUserAuthentication()->getKey('ses', $key);
    }

    /**
     * @param string $key
     * @param string $value
     */
    public function set($key, $value)
    {
        $this->getFrontendUserAuthentication()->setKey('ses', $key, $value);
        $this->getFrontendUserAuthentication()->storeSessionData();
    }

    /**
     * @return \TYPO3\CMS\Frontend\Authentication\FrontendUserAuthentication;
     */
    protected function getFrontendUserAuthentication()
    {
        /** @var \TYPO3\CMS\Frontend\Controller\TypoScriptFrontendController $typoScriptFrontendController */
        $typoScriptFrontendController = $GLOBALS['TSFE'];
        return $typoScriptFrontendController->fe_user;
    }
}
