<?php

namespace Red<PERSON>wi\TalismanBlog\Controller;

use Redkiwi\TalismanBlog\Service\PaginationService;
use Redkiwi\TalismanPage\Domain\Repository\PageRepository;
use TYPO3\CMS\Core\Utility\GeneralUtility;
use TYPO3\CMS\Frontend\Controller\TypoScriptFrontendController;

/**
 * Controller: Blog
 *
 * @package Redkiwi\TalismanBlog\Controller
 */
class BlogController extends \TYPO3\CMS\Extbase\Mvc\Controller\ActionController
{
    /**
     * @var \Redkiwi\TalismanPage\Domain\Repository\PageRepository
     */
    protected $pageRepository;

    /**
     * @var \Redkiwi\TalismanBlog\Service\PaginationService
     */
    protected $paginationService;

    public function __construct(PageRepository $pageRepository, PaginationService $paginationService)
    {
        parent::__construct();
        $this->pageRepository = $pageRepository;
        $this->paginationService = $paginationService;
    }

    /**
     * @param int $page
     * @param string $author
     * @param string $country
     * @return void
     */
    public function listAction($page = 1, $author = null, $country = null)
    {
        $limit = (int) (isset($this->settings['data']['limit'])) ? $this->settings['data']['limit'] : 10;
        $pid = $this->settings['data']['startPoint'];
        $offset = ($page - 1) * $limit;
        $blogPosts = $this->pageRepository->findByPidAndBlogAuthorAndBlogCountry($pid, $offset, $limit, $author, $country);
        $totalPosts = $this->pageRepository->countByPidAndBlogAuthorAndBlogCountry($pid, $author, $country);

        $pagination = $this->paginationService->getPagination(
            $page,
            $limit,
            $totalPosts,
            4
        );

        $this->view->assignMultiple([
            'blogPosts' => $blogPosts,
            'pagination' => $pagination,
            'author' => $author,
            'country' => $country
        ]);
    }

    /**
     * @return void
     */
    public function popularAction()
    {
        $pid = $this->settings['data']['startPoint'];
        $limit = (int) (isset($this->settings['data']['limit'])) ? $this->settings['data']['limit'] : 10;
        $blogPosts = $this->pageRepository->findPopularByPid($pid, $limit);

        $this->view->assignMultiple([
            'blogPosts' => $blogPosts
        ]);
    }

    /**
     * @return void
     */
    public function subMenuAction()
    {
    }

    /**
     * @return void
     */
    public function introAction()
    {
        $this->view->assignMultiple([
            'date' => $this->getTypoScriptFrontendController()->page['lastUpdated']
        ]);
    }

    /**
     * @return void
     */
    public function commentsAction()
    {
        $this->view->assignMultiple([
            'id' => $this->getTypoScriptFrontendController()->id,
            'url' => $this->uriBuilder
                ->setTargetPageUid($this->getTypoScriptFrontendController()->id)
                ->setCreateAbsoluteUri(true)
                ->build()
        ]);
    }

    /**
     * @return void
     */
    public function relatedAction()
    {
        $pages = $this->pageRepository->findByUidList(GeneralUtility::trimExplode(',', $this->settings['data']['relatedPages']));
        $this->view->assign('pages', $pages);
    }

    /**
     * @return void
     */
    public function shareAction()
    {
    }

    /**
     * @return TypoScriptFrontendController
     */
    protected function getTypoScriptFrontendController()
    {
        return $GLOBALS['TSFE'];
    }
}
