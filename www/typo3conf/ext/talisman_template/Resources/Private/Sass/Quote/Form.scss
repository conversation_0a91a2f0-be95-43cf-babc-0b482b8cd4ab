.select-wrapper {

    display: inline-block;
    position: relative;

    &:after {

        color: $rubine;
        content: map_get($icons, 'chevron-down');
        font-family: $icon-font;
        font-size: 14px;
        font-weight: bold;
        pointer-events: none;
        position: absolute;
        right: 14px;
        top: 15px;

    }

    select {

        -webkit-appearance: none;
        background-color: $checkbox-background;
        border: 0;
        border-radius: 0;
        min-width: 70px;
        padding: 14px;
        transition: all 0.3s ease;

        &:hover,
        &:focus {

            background-color: $gray-light;
            outline: 0;

        }

        &::-ms-expand {

            display: none;

        }

    }

}