<?php
namespace Redkiwi\TalismanQuoteForm\Validation\Validator;

use TYPO3\CMS\Core\Utility\GeneralUtility;

/**
 * Validator: Conditional address field
 *
 * @package Redkiwi\TalismanQuoteForm\Validation\Validator
 */
class ConditionalAddressFieldValidator extends \TYPO3\CMS\Extbase\Validation\Validator\AbstractValidator
{
    /** @var boolean */
    protected $acceptsEmptyValues = false;

    /**
     * @param mixed $value
     * @return void
     */
    public function isValid($value) {
        $request = GeneralUtility::_POST('tx_talismanquoteform_brochureform');
        if (isset($request['request']['brochurePerPost']) && (bool) $request['request']['brochurePerPost'] === true) {
            if ($value === '') {
                $this->addError(
                    $this->translateErrorMessage(
                        'validator.notempty.empty',
                        'extbase'
                    ), 1221560718);
            }
        }
    }
}
