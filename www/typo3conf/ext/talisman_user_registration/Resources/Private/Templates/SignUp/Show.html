{namespace k=Keizer\KoningLibrary\ViewHelper}
<f:layout name="Default" />

<f:section name="Content">

    <h2><f:translate id="sign_up.show.header" /></h2>

    <div class="nav-tabs-box with-border sign-up-form">
        <div class="row social-signup-row">
            <div class="col-sm-5 col-md-6">
                <p><f:translate id="sign_up.show.text" /></p>
            </div>
            <div class="col-sm-7 col-md-6 text-right button-container">
                <f:link.action action="facebookLogin" controller="Facebook" arguments="{referrer: referrer}" class="btn btn-social btn-facebook">
                    <f:translate id="sign_up.show.login_facebook" />
                </f:link.action>

                <f:link.action action="googleLogin" controller="Google" arguments="{referrer: referrer}" class="btn btn-social btn-google">
                    <f:translate id="sign_up.show.login_google" />
                </f:link.action>
            </div>
            <div class="clearfix"></div>
        </div>

        <hr>

        <f:form action="handleSignUp" objectName="signUp" class="form">
            <div class="row">
                <div class="col-sm-5">
                    <h2><f:translate id="sign_up.show.your_name" /></h2>

                    <div class="form-group">
                        <label><f:translate id="sign_up.show.salutation" /> <span class="required">*</span></label><br>
                        <div class="radio radio-inline">
                            <f:form.radio property="salutation" value="1" id="salutation-1" />
                            <label for="salutation-1"><f:translate id="sign_up.show.salutation.1" /></label>
                        </div>
                        <div class="radio radio-inline">
                            <f:form.radio property="salutation" value="2" id="salutation-2" />
                            <label for="salutation-2"><f:translate id="sign_up.show.salutation.2" /></label>
                        </div>
                        <f:render partial="Form/Error" arguments="{formObjectName: 'signUp', property: 'salutation'}" />
                    </div>

                    <f:render
                        partial="Form/TextField"
                        arguments="{
                            property: 'firstName',
                            formObjectName: 'signUp',
                            required: 1,
                            label: '{f:translate(id: \'sign_up.show.first_name\')}'
                        }" />

                    <div class="row">
                        <div class="col-sm-5">
                            <f:render
                                partial="Form/TextField"
                                arguments="{
                                    property: 'middleName',
                                    formObjectName: 'signUp',
                                    label: '{f:translate(id: \'sign_up.show.middle_name\')}'
                                }" />
                        </div>
                    </div>

                    <f:render
                        partial="Form/TextField"
                        arguments="{
                            property: 'lastName',
                            formObjectName: 'signUp',
                            required: 1,
                            label: '{f:translate(id: \'sign_up.show.last_name\')}'
                        }" />
                </div>

                <div class="col-sm-6 col-sm-offset-1">
                    <h2><f:translate id="sign_up.show.your_details" /></h2>

                    <f:render
                        partial="Form/TextField"
                        arguments="{
                            property: 'email',
                            formObjectName: 'signUp',
                            required: 1,
                            label: '{f:translate(id: \'sign_up.show.email\')}'
                        }" />
                </div>
            </div>

            <div class="row submit-row">
                <div class="col-sm-6 col-sm-push-6 text-right">
                    <button type="submit" class="btn btn-default btn-rubine pull-right">
                        <f:translate id="sign_up.show.submit" />
                    </button>
                    <div class="clearfix"></div>
                </div>
                <div class="col-sm-6 col-sm-pull-6">
                    <span class="mandatory">* <f:translate id="form.label.mandatory" extensionName="TalismanTemplate" /></span>
                </div>
            </div>

        </f:form>
    </div>
</f:section>
