const mobileMenuToggle = document.querySelector<HTMLButtonElement>(
    '[aria-controls="mobile-nav"]',
);
mobileMenuToggle?.addEventListener('click', () => {
    document.documentElement.classList.toggle('scroll-lock');
    document.documentElement.classList.toggle('menu-open');
});

function markCurrentPage() {
    var currentPage = window.location.href;
    document.querySelectorAll('#mobile-nav a').forEach(function(item) {
        if (item.getAttribute('href') === currentPage) {
            item.setAttribute('aria-current', 'page');
        } else {
            item.removeAttribute('aria-current');
        }
    });
}

window.addEventListener('load', function() {
    markCurrentPage();
});
