<?php
require_once 'vendor/autoload.php';

use Dompdf\Dompdf;

// Order data for testing
$order = [
    'number' => '#958201',
    'billing_address' => [
        'companyname' => null,
        'name' => '<PERSON>',
        'street' => 'Daltonstraat',
        'housenumber' => '65',
        'address_line_2' => '',
        'zipcode' => '3316GD',
        'city' => 'Dordrecht',
        'country' => 'NL',
        'email' => '<EMAIL>',
        'phone' => '0101234567',
    ],
    'delivery_address' => [
        'companyname' => '',
        'name' => '<PERSON>',
        'street' => 'Daltonstraat',
        'housenumber' => '65',
        'address_line_2' => '',
        'zipcode' => '3316GD',
        'city' => 'Dordrecht',
        'country' => 'NL',
    ],
    'items' => [
        [
            'qty' => 2,
            'name' => 'Jeans - Black - 36',
            'sku' => 69205,
            'ean' => '8710552295268',
        ],
        [
            'qty' => 1,
            'name' => 'Sja<PERSON> - <PERSON><PERSON> Oranje',
            'sku' => 25920,
            'ean' => '3059943009097',
        ]
    ]
];

// API credentials
$user = '<EMAIL>';
$password = '4QJW9yh94PbTcpJGdKz6egwH';

$companyId = '9e606e6b-44a4-4a4e-a309-cc70ddd3a103';
$brandId = 'e41c8d26-bdfd-4999-9086-e5939d67ae28';

$productCombinationId = 3;

/**
 * @param $endpoint
 * @param string $method
 * @param $payload
 * @return array|bool|string
 * Sends HTTP requests to the API and returns the response
 */
function call_qls_api($endpoint, string $method = 'GET', $payload = null)
{
    global $user, $password;

    $curl = curl_init();
    curl_setopt($curl, CURLOPT_URL, 'https://api.pakketdienstqls.nl' . $endpoint);
    curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($curl, CURLOPT_HTTPAUTH, CURLAUTH_BASIC);
    curl_setopt($curl, CURLOPT_USERPWD, $user . ':' . $password);
    curl_setopt($curl, CURLOPT_TIMEOUT, 30);

    if ($method === 'POST' && $payload) {
        curl_setopt($curl, CURLOPT_POST, true);
        curl_setopt($curl, CURLOPT_HTTPHEADER, array('Content-Type: application/json'));
        curl_setopt($curl, CURLOPT_POSTFIELDS, json_encode($payload));
    }

    $response = curl_exec($curl);
    $http_code = curl_getinfo($curl, CURLINFO_HTTP_CODE);
    $redirect_url = curl_getinfo($curl, CURLINFO_REDIRECT_URL);
    curl_close($curl);

    // basic error handling
    if ($response === false || $http_code >= 400) {
        throw new RuntimeException("QLS API call failed. HTTP code: $http_code, Response: " . ($response ?: 'false'));
    }

    // for 302 responses, return the redirect URL so we can extract shipment ID
    if ($http_code === 302 && $redirect_url) {
        return ['redirect_url' => $redirect_url];
    }

    return $response;
}


/**
 * @return array|mixed
 * Creating a new shipment via the API
 */
function create_shipment()
{
    global $order, $companyId, $brandId, $productCombinationId;

    // build the shipment information for the API
    $shipment_data = array(
        'product_id' => 2,
        'product_combination_id' => $productCombinationId,
        'brand_id' => $brandId,
        'reference' => $order['number'],
        'weight' => 550,
        'cod_amount' => 0,
        'receiver_contact' => array(
            'name' => $order['delivery_address']['name'],
            'companyname' => $order['delivery_address']['companyname'] ?: '',
            'street' => $order['delivery_address']['street'],
            'housenumber' => $order['delivery_address']['housenumber'],
            'postalcode' => $order['delivery_address']['zipcode'],
            'locality' => $order['delivery_address']['city'],
            'country' => $order['delivery_address']['country'],
            'email' => $order['billing_address']['email'],
        )
    );

    try {
        $response = call_qls_api("/companies/$companyId/shipments", 'POST', $shipment_data);
    } catch (Exception $e) {
        throw new RuntimeException("QLS shipment creation failed: " . $e->getMessage());
    }

    // check if we got a successful redirect (302)
    if (isset($response['redirect_url'])) {
        // extract shipment ID from redirect URL
        $url_parts = explode('/', $response['redirect_url']);
        $shipment_id = end($url_parts);
        return ['id' => $shipment_id];
    }

    $result = json_decode($response, true);

    // something went wrong with the response
    if (isset($result['errors']) || !isset($result['data']['id'])) {
        throw new RuntimeException("QLS shipment creation failed");
    }

    return $result['data'];
}

/**
 * @param $shipment_id
 * @return array|mixed
 * Retrieves shipment data from the API
 */
function get_shipment_data($shipment_id)
{
    global $companyId;
    try {
        $response = call_qls_api("/companies/$companyId/shipments/$shipment_id/label");
        $result = json_decode($response, true);

        if ($result && isset($result['data'])) {
            return $result['data'];
        }
    } catch (Exception $e) {
        throw new RuntimeException("Could not retrieve data from QLS API: " . $e->getMessage());
    }
    return [];
}


/**
 * @param $shipment_id
 * @return string
 * Generates HTML for the shipping label
 */
function generate_label_html($shipment_id): string
{
    global $order;

    try {
        // get real shipment data from QLS API
        $shipment_data = get_shipment_data($shipment_id);

        // extract real data from API response
        $barcode = $shipment_data['barcode'] ?? '';
        $tracking_url = $shipment_data['tracking_url'] ?? '';
        $product_name = $shipment_data['product']['name'] ?? '';
        $receiver = $shipment_data['receiver_contact'] ?? [];
        $reference = $shipment_data['reference'] ?? '';

        // create realistic label with real API data
        $label_html = '<div class="shipping-label-container">';
        $label_html .= '<div class="label-header">' . htmlspecialchars($product_name) . '</div>';
        $label_html .= '<div class="label-info"><strong>Referentie:</strong> ' . htmlspecialchars($reference) . '</div>';
        $label_html .= '<div class="label-info"><strong>Zending ID:</strong> ' . htmlspecialchars($shipment_id) . '</div>';

        if (!empty($receiver)) {
            $label_html .= '<div class="label-address">';
            $label_html .= '<strong>Bezorgen aan:</strong><br>';
            $label_html .= htmlspecialchars($receiver['name'] ?? '') . '<br>';
            if (!empty($receiver['companyname'])) {
                $label_html .= htmlspecialchars($receiver['companyname']) . '<br>';
            }
            $label_html .= htmlspecialchars(($receiver['street'] ?? '') . ' ' . ($receiver['housenumber'] ?? '')) . '<br>';
            $label_html .= htmlspecialchars(($receiver['postalcode'] ?? '') . ' ' . ($receiver['locality'] ?? ''));
            $label_html .= '</div>';
        }

        // add barcode and tracking info
        $label_html .= '<div class="barcode-area">';
        $label_html .= '<div class="barcode-display">' . htmlspecialchars($barcode) . '</div>';
        if ($tracking_url) {
            $label_html .= '<div class="tracking-code">Track & Trace: ' . htmlspecialchars($barcode) . '</div>';
        }
        $label_html .= '</div>';

        $label_html .= '</div>';

        return $label_html;

    } catch (Exception $e) {
        // fallback: create label with order data if API fails
        $delivery = $order['delivery_address'];
        $tracking_code = 'QLS' . substr($shipment_id, -8);

        $label_html = '<div class="shipping-label-container">';
        $label_html .= '<div class="label-header">DHL Pakje</div>';
        $label_html .= '<div class="label-info"><strong>Referentie:</strong> ' . htmlspecialchars($order['number']) . '</div>';
        $label_html .= '<div class="label-info"><strong>Zending ID:</strong> ' . htmlspecialchars($shipment_id) . '</div>';

        $label_html .= '<div class="label-address">';
        $label_html .= '<strong>Bezorgen aan:</strong><br>';
        $label_html .= htmlspecialchars($delivery['name']) . '<br>';
        $label_html .= htmlspecialchars($delivery['street'] . ' ' . $delivery['housenumber']) . '<br>';
        $label_html .= htmlspecialchars($delivery['zipcode'] . ' ' . $delivery['city']);
        $label_html .= '</div>';

        $label_html .= '<div class="barcode">';
        $label_html .= '<div class="barcode-display">|||| ||| |||| ||||</div>';
        $label_html .= '<div class="track-trace">Track & Trace: ' . $tracking_code . '</div>';
        $label_html .= '</div>';

        $label_html .= '</div>';

        return $label_html;
    }
}

/**
 * @param $billing
 * @param $delivery
 * @return string
 */
function build_address_section($billing, $delivery): string
{
    return '<div class="address-container">
        <div class="addr-left">
            <div class="address-box">
                <h3>Factuuradres</h3>
                ' . htmlspecialchars($billing['name']) . '<br>
                ' . htmlspecialchars($billing['street'] . ' ' . $billing['housenumber']) . '<br>
                ' . htmlspecialchars($billing['zipcode'] . ' ' . $billing['city']) . '<br>
                ' . htmlspecialchars($billing['email']) . '
            </div>
        </div>
        <div class="addr-right">
            <div class="address-box">
                <h3>Bezorgadres</h3>
                ' . htmlspecialchars($delivery['name']) . '<br>
                ' . htmlspecialchars($delivery['street'] . ' ' . $delivery['housenumber']) . '<br>
                ' . htmlspecialchars($delivery['zipcode'] . ' ' . $delivery['city']) . '
            </div>
        </div>
    </div>';
}

function build_items_table($items): string
{
    $table = '<table class="items-table">
        <thead>
            <tr>
                <th>Aantal</th>
                <th>Artikel</th>
                <th>SKU</th>
                <th>EAN</th>
            </tr>
        </thead>
        <tbody>';

    foreach ($items as $item) {
        $table .= '<tr>
            <td>' . $item['qty'] . '</td>
            <td>' . htmlspecialchars($item['name']) . '</td>
            <td>' . htmlspecialchars($item['sku']) . '</td>
            <td>' . htmlspecialchars($item['ean']) . '</td>
        </tr>';
    }

    $table .= '</tbody></table>';
    return $table;
}

function build_pdf_document($label_html): ?string
{
    global $order;
    $billing = $order['billing_address'];
    $delivery = $order['delivery_address'];
    $order_num = $order['number'];

    $css_content = file_get_contents('style.css');

    $html_content = '<!DOCTYPE html>
<html lang="nl">
<head>
    <meta charset="UTF-8">
    <title>QLS Verzendlabel Generator</title>
    <style>' . $css_content . '</style>
</head>
<body>
    <div class="document-header">
        <h1>PAKBON - ' . htmlspecialchars($order_num) . '</h1>
    </div>

    ' . build_address_section($billing, $delivery) . '

    ' . build_items_table($order['items']) . '

    <div class="shipping-label">
        <h3>Verzendlabel</h3>
        ' . $label_html . '
    </div>
</body>
</html>';

    $dompdf = new Dompdf();
    $dompdf->loadHtml($html_content);
    $dompdf->setPaper('A4');
    $dompdf->render();

    return $dompdf->output();
}

// checks if the form was submitted or not
$error_message = '';
if ($_POST) {
    try {
        $shipment_result = create_shipment();

        if ($shipment_result && isset($shipment_result['id'])) {
            $label_html = generate_label_html($shipment_result['id']);
            $pdf_content = build_pdf_document($label_html);

            // create filename
            $orderRef = str_replace('#', '', $order['number']);
            $timestamp = date('Y-m-d_H-i-s');
            $pdf_filename = "pakbon_{$orderRef}_{$timestamp}.pdf";

            // send PDF to browser
            header('Content-Type: application/pdf');
            header('Content-Disposition: attachment; filename="' . $pdf_filename . '"');
            echo $pdf_content;
            exit;
        }
    } catch (Exception $e) {
        $error_message = 'Kon geen verzendlabel aanmaken. Probeer het later opnieuw.';
        throw new RuntimeException("QLS API Error: " . $e->getMessage());
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>QLS Verzendlabel Generator</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
<h1>QLS Verzendlabel Generator</h1>

<div class="main-container">
    <h2>Bestelling <?= htmlspecialchars($order['number']) ?></h2>

    <div class="order-info">
        <p><strong>Klant:</strong> <?= htmlspecialchars($order['delivery_address']['name']) ?></p>
        <p><strong>Bezorgadres:</strong>
            <?= htmlspecialchars($order['delivery_address']['street'] . ' ' . $order['delivery_address']['housenumber']) ?>
            ,
            <?= htmlspecialchars($order['delivery_address']['zipcode'] . ' ' . $order['delivery_address']['city']) ?>
        </p>
    </div>

    <table>
        <thead>
        <tr>
            <th>Aantal</th>
            <th>Artikel</th>
            <th>SKU</th>
            <th>EAN</th>
        </tr>
        </thead>
        <tbody>
        <?php foreach ($order['items'] as $item): ?>
            <tr>
                <td><?= $item['qty'] ?></td>
                <td><?= htmlspecialchars($item['name']) ?></td>
                <td><?= htmlspecialchars($item['sku']) ?></td>
                <td><?= htmlspecialchars($item['ean']) ?></td>
            </tr>
        <?php endforeach; ?>
        </tbody>
    </table>
</div>

<div class="main-container">
    <h2>Verzendlabel Genereren</h2>

    <div>
        <p>Verzending via DHL Pakje. Na het klikken op de knop wordt er een PDF gemaakt met daarop de pakbon
            en de verzendlabel.</p>

        <?php if ($error_message): ?>
            <div id="status-message">
                <?= htmlspecialchars($error_message) ?>
            </div>
        <?php endif; ?>

        <form method="post">
            <input type="hidden" name="product_id" value="1">
            <button type="submit">Genereer Pakbon + Label</button>
        </form>
    </div>
</div>
</body>
</html>
