<?php
namespace <PERSON><PERSON><PERSON>\TalismanTravelogues\Controller;

use Red<PERSON><PERSON>\TalismanTravelogues\Domain\DataTransfer\Document;
use Redkiwi\TalismanTravelogues\Domain\DataTransfer\Manual;
use Redkiwi\TalismanTravelogues\Domain\DataTransfer\StepTwo;
use TYPO3\CMS\Core\Utility\GeneralUtility;
use TYPO3\CMS\Frontend\Controller\TypoScriptFrontendController;

/**
 * Controller: Travelogue
 *
 * @package Redkiwi\TalismanTravelogues\Controller
 */
class FormController extends \TYPO3\CMS\Extbase\Mvc\Controller\ActionController
{
    const SESSION_KEY = 'talismantravelogue_form';

    /**
     * @var \Keizer\KoningLibrary\Frontend\SessionService
     * @inject
     */
    protected $sessionService;

    /** @var \Redkiwi\TalismanTravelogues\Domain\Model\Session */
    protected $session;

    /**
     * @return void
     */
    public function stepOneAction()
    {
    }

    /**
     * @param string $type
     * @return void
     * @throws \TYPO3\CMS\Extbase\Mvc\Exception\StopActionException
     * @throws \TYPO3\CMS\Extbase\Mvc\Exception\UnsupportedRequestTypeException
     */
    public function handleStepOneAction($type = 'manually')
    {
        $this->session->setFormType($type);
        $this->sessionService->saveSession(self::SESSION_KEY, $this->session);

        if ($this->getTypoScriptFrontendController()->loginUser) {
            $user = $this->getTypoScriptFrontendController()->fe_user->user;
            if ($user['middle_name'] !== '') {
                $fullName = $user['first_name'] . ' ' . $user['middle_name'] . ' ' . $user['last_name'];
            } else {
                $fullName = $user['first_name'] . ' ' . $user['last_name'];
            }
            $stepTwo = new StepTwo();
            $stepTwo->setName($fullName);
            $stepTwo->setEmail($user['email']);
            $this->session->setStepTwo($stepTwo);
            $this->sessionService->saveSession(self::SESSION_KEY, $this->session);

            if ($this->session->getFormType() === 'manually') {
                $this->redirect('manualForm');
            }
            $this->redirect('documentForm');
        } else {
            $this->redirect('stepTwo');
        }
    }

    /**
     * @return void
     * @throws \TYPO3\CMS\Extbase\Mvc\Exception\StopActionException
     * @throws \TYPO3\CMS\Extbase\Mvc\Exception\UnsupportedRequestTypeException
     */
    public function stepTwoAction()
    {
        if ($this->getTypoScriptFrontendController()->loginUser) {
            if ($this->session->getFormType() === 'manually') {
                $this->redirect('manualForm');
            }
            $this->redirect('documentForm');
        }

        $request = $this->session->getStepTwo() ? $this->session->getStepTwo() : new StepTwo();
        $this->view->assign('request', $request);
    }

    /**
     * @param \Redkiwi\TalismanTravelogues\Domain\DataTransfer\StepTwo $request
     * @return void
     * @throws \TYPO3\CMS\Extbase\Mvc\Exception\StopActionException
     * @throws \TYPO3\CMS\Extbase\Mvc\Exception\UnsupportedRequestTypeException
     */
    public function handleStepTwoAction(\Redkiwi\TalismanTravelogues\Domain\DataTransfer\StepTwo $request)
    {
        $this->session->setStepTwo($request);
        $this->sessionService->saveSession(self::SESSION_KEY, $this->session);

        if ($this->session->getFormType() === 'manually') {
            $this->redirect('manualForm');
        }
        $this->redirect('documentForm');
    }

    /**
     * @return void
     */
    public function manualFormAction()
    {
        $request = new Manual();
        $this->view->assign('request', $request);
    }

    /**
     * @param \Redkiwi\TalismanTravelogues\Domain\DataTransfer\Manual $request
     * @return void
     * @throws \TYPO3\CMS\Extbase\Mvc\Exception\StopActionException
     * @throws \TYPO3\CMS\Extbase\Mvc\Exception\UnsupportedRequestTypeException
     */
    public function handleManualFormAction(\Redkiwi\TalismanTravelogues\Domain\DataTransfer\Manual $request)
    {
        $images = [];
        if (!empty($request->getImages())) {
            /** @var \TYPO3\CMS\Core\Utility\File\BasicFileUtility $basicFileFunctions */
            $basicFileFunctions = \TYPO3\CMS\Core\Utility\GeneralUtility::makeInstance(\TYPO3\CMS\Core\Utility\File\BasicFileUtility::class);
            $folder = \TYPO3\CMS\Core\Utility\GeneralUtility::getFileAbsFileName($this->settings['images']['location']);
            if (!is_dir($folder)) {
                GeneralUtility::mkdir_deep($folder);
            }
            foreach ($request->getImages() as $image) {
                if (!empty($image['tmp_name'])) {
                    $pathInfo = pathinfo($image['name']);

                    $fileName = $basicFileFunctions->getUniqueName(
                        $pathInfo['filename'] . '.' . $pathInfo['extension'],
                        $folder
                    );

                    if (GeneralUtility::upload_copy_move($image['tmp_name'], $fileName)) {
                        $images[] = GeneralUtility::getIndpEnv('TYPO3_SITE_URL') . $this->settings['images']['location'] . '/' . basename($fileName);
                    }
                }
            }
        }

        /** @var \Keizer\KoningLibrary\Service\MailService $mailerService */
        $mailerService = GeneralUtility::makeInstance('Keizer\\KoningLibrary\\Service\\MailService');
        $mailerService
            ->setTemplateRootPaths([
                'EXT:talisman_travelogues/Resources/Private/Templates/Email'
            ])
            ->setLayoutRootPaths([
                'EXT:talisman_template/Resources/Private/Layouts'
            ])
            ->sendMail(
                GeneralUtility::trimExplode(',', $this->settings['mail']['to']['emailAddresses']),
                [$this->settings['mail']['from']['emailAddress'] => $this->settings['mail']['from']['name']],
                $this->settings['mail']['subject'],
                'Manual',
                [],
                [
                    'fields' => $request,
                    'subject' => $this->settings['mail']['subject'],
                    'images' => $images
                ]
            );

        $this->redirect('finished');
    }

    /**
     * @return void
     */
    public function documentFormAction()
    {
        $request = new Document();
        $this->view->assign('request', $request);
    }

    /**
     * @param \Redkiwi\TalismanTravelogues\Domain\DataTransfer\Document $request
     * @return void
     * @throws \TYPO3\CMS\Extbase\Mvc\Exception\StopActionException
     * @throws \TYPO3\CMS\Extbase\Mvc\Exception\UnsupportedRequestTypeException
     */
    public function handleDocumentFormAction(\Redkiwi\TalismanTravelogues\Domain\DataTransfer\Document $request)
    {
        $pathInfo = pathinfo($request->getDocument()['name']);
        $tempFile = GeneralUtility::tempnam($pathInfo['filename'], '.' . $pathInfo['extension']);
        if (GeneralUtility::upload_copy_move($request->getDocument()['tmp_name'], $tempFile)) {
            $typoscriptReceivers = explode(',', $this->settings['mail']['to']['emailAddresses']);
            $typoscriptNames = explode(',', $this->settings['mail']['to']['names']);
            $receivers = array_combine($typoscriptReceivers, $typoscriptNames);
            
            /** @var \Keizer\KoningLibrary\Service\MailService $mailerService */
            $mailerService = GeneralUtility::makeInstance('Keizer\\KoningLibrary\\Service\\MailService');
            $mailerService
                ->setTemplateRootPaths([
                    'EXT:talisman_travelogues/Resources/Private/Templates/Email'
                ])
                ->setLayoutRootPaths([
                    'EXT:talisman_template/Resources/Private/Layouts'
                ])
                ->sendMail(
                    $receivers,
                    [$this->settings['mail']['from']['emailAddress'] => $this->settings['mail']['from']['name']],
                    $this->settings['mail']['subject'],
                    'Document',
                    [],
                    [
                        'fields' => $request,
                        'subject' => $this->settings['mail']['subject']
                    ],
                    [
                        $tempFile
                    ]);

            GeneralUtility::unlink_tempfile($tempFile);
        }
        $this->redirect('finished');
    }

    /**
     * @return void
     */
    public function finishedAction()
    {
        $this->session = new \Redkiwi\TalismanTravelogues\Domain\Model\Session();
        $this->sessionService->saveSession(self::SESSION_KEY, $this->session);
    }

    /**
     *
     */
    protected function initializeAction()
    {
        $this->session = $this->sessionService->getSession(self::SESSION_KEY, 'Redkiwi\TalismanTravelogues\Domain\Model\Session');
    }

    /**
     * @return TypoScriptFrontendController
     */
    protected function getTypoScriptFrontendController()
    {
        return $GLOBALS['TSFE'];
    }
}
