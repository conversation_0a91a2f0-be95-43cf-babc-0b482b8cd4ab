Talisman.Extensions.TalismanQuoteForm = {

    /**
     * @return void
     */
    init: function() {

        var $form = $('.talisman-quote-form');

        $form.find('input[name="tx_talismanquoteform_brochureform[request][brochurePerPost]"]').click(function() {
            Talisman.Extensions.TalismanQuoteForm.toggleAddressContainer();
        });

        $('.heard-of-talisman-answer select').change(function() {

            $(this).parsley().validate();

            if (parseInt($(this).val()) === 0) {
                $(this).closest('.heard-of-talisman-answer').find('.heard-of-talisman-other-answer').show();
            } else {
                $(this).closest('.heard-of-talisman-answer').find('.heard-of-talisman-other-answer').hide();
            }
        });

        $form.find('input[name="tx_talismanquoteform_quoteform[request][unsure]"]').change(function() {
            $form.find('.multi-select i.active').trigger('click');
        });

        Talisman.Extensions.TalismanQuoteForm.toggleAddressContainer();

        $form.find('.example-step').hide();

        $form.find('.delete-example').on('click', function () {
            $('.talisman-quote-form .example-step').show();
            $('.talisman-quote-form .example-destination').hide();
            $(this).hide();
        });

    },

    /**
     * @return void
     */
    toggleAddressContainer: function() {

        var $form = $('.talisman-quote-form');

        $form.find('input[name="tx_talismanquoteform_quoteform[quote][travelledBefore]"]').on('change', function () {
            if ($(this).val() == '1') {
                $('.talisman-quote-form input[data-address-toggle="1"]').removeAttr('required');
                $('.talisman-quote-form .addres-container-field').hide();
            } else {
                $('.talisman-quote-form .addres-container-field').show();
                $('.talisman-quote-form input[data-address-toggle="1"]').prop('required', true);
            }
        });
    }
};

$(document).ready(function() {
    Talisman.Extensions.TalismanQuoteForm.init();
});
