{namespace r=KoninklijkeCollective\KoningRecaptcha\ViewHelpers}

<f:layout name="Default" />

<f:section name="Content">
    <div class="nav-tabs-box with-border">
        <p><f:translate id="form.show.intro" /></p>
        <f:form action="handle" objectName="form" class="form" additionalAttributes="{autocomplete: 'off'}">
            <div class="row">
                <div class="col-sm-6">
                    <h2><f:translate id="form.show.details" /></h2>

                    <f:render
                        partial="Form/TextField"
                        arguments="{
                            property: 'name',
                            formObjectName: 'form',
                            required: 1,
                            label: '{f:translate(id: \'form.show.your_name\')}'
                        }" />

                    <f:render
                            partial="Form/TextField"
                            arguments="{
                            property: 'email',
                            formObjectName: 'form',
                            required: 1,
                            type: 'email',
                            label: '{f:translate(id: \'form.show.email\')}'
                        }" />

                    <f:render
                            partial="Form/TextField"
                            arguments="{
                            property: 'phone',
                            formObjectName: 'form',
                            type: 'phone',
                            label: '{f:translate(id: \'form.show.phone\')}',
                            tooltip: '{f:translate(id: \'form.show.phone.tooltip\')}'
                        }" />

                    <f:render
                            partial="Form/TextArea"
                            arguments="{
                            property: 'question',
                            formObjectName: 'form',
                            rows: 10,
                            label: '{f:translate(id: \'form.show.your_question\')}',
                            required: 1,
                            additionalAttributes: {required: 'required'}
                        }" />

                    <div class="heard-of-talisman-answer">
                        <f:render
                                partial="Form/Select"
                                arguments="{
                                property: 'heardOfTalismanAnswer',
                                options: '{heardOfTalismanAnswers}',
                                prependOptionLabel: '{f:translate(key: \"LLL:EXT:talisman_template/Resources/Private/Language/locallang.xlf:form.choose_an_option\")}',
                                label: '{f:translate(key: \"LLL:EXT:talisman_quote_form/Resources/Private/Language/locallang.xlf:quote_form.heard_of_talisman\")}',
                                additionalAttributes: '{data-parsley-trigger: \'change\', data-parsley-required: \'\', data-parsley-required-message: \'{f:translate(key: \"LLL:EXT:talisman_template/Resources/Private/Language/locallang.xlf:form.error.required_field\")}\', data-parsley-errors-container: \'.bootstrap-select\'}'
                        }" />

                        <div class="heard-of-talisman-other-answer" style="display: none;">
                            <f:render
                                    partial="Form/TextField"
                                    arguments="{
                                    property: 'heardOfTalismanOtherAnswer',
                                    formObjectName: 'quote'
                            }" />
                        </div>
                    </div>

                    <label><f:translate id="form.show.newsletter" /></label>
                    <div class="form-group">
                        <div class="radio radio-inline">
                            <f:form.radio property="newsletter" value="0" id="newsletter-0" />
                            <label for="newsletter-0"><f:translate id="form.show.newsletter.0" /></label>
                        </div>
                        <div class="radio radio-inline">
                            <f:form.radio property="newsletter" value="1" id="newsletter-1" />
                            <label for="newsletter-1"><f:translate id="form.show.newsletter.1" /></label>
                        </div>
                        <div class="radio radio-inline">
                            <f:form.radio property="newsletter" value="2" id="newsletter-2" />
                            <label for="newsletter-2"><f:translate id="form.show.newsletter.2" /></label>
                        </div>
                    </div>

                    <div class="form-group">
                        <r:recaptcha />

                        <f:render partial="Form/Error" arguments="{property: 'captcha', formObjectName: 'form'}" />
                    </div>

                    <div class="checkbox">
                        <f:form.checkbox property="agree" value="1" id="agree" additionalAttributes="{required: 'required'}" data="{parsley-trigger: 'change', parsley-required-message: '{f:translate(key: \'LLL:EXT:talisman_template/Resources/Private/Language/locallang.xlf:form.error.required_field\')}'}" />
                        <label for="agree"><f:translate id="form.show.agree" /></label>
                    </div>

                    <button type="submit" class="btn btn-default btn-rubine">
                        <f:translate id="form.show.submit" />
                    </button>

                    <span class="mandatory">* <f:translate id="form.label.mandatory" extensionName="TalismanTemplate" /></span>

                </div>
            </div>

        </f:form>
    </div>
</f:section>
