<?php

namespace Redkiwi\TalismanContent\ViewHelpers\Format;

use Redkiwi\TalismanEsbConnector\Domain\Model\PromotionalPackage;
use Redkiwi\TalismanEsbConnector\Domain\Repository\PromotionalPackageRepository;
use TYPO3Fluid\Fluid\Core\ViewHelper\AbstractViewHelper;

/**
 * Example trip price view helper
 *
 * @package Redkiwi\TalismanContent\ViewHelpers\Format
 */
class ExampleTripPriceViewHelper extends AbstractViewHelper
{
    /**
     * @var PromotionalPackageRepository
     */
    protected $promotionalPackageRepository;

    /**
     * @param PromotionalPackageRepository $promotionalPackageRepository
     */
    public function __construct(PromotionalPackageRepository $promotionalPackageRepository)
    {
        $this->promotionalPackageRepository = $promotionalPackageRepository;
    }

    /**
     * @return void
     */
    public function initializeArguments()
    {
        $this->registerArgument('description', 'string', 'Description');
        $this->registerArgument('promotionalPackage', 'int', 'Promotional package uid');
    }

    /**
     * @return string
     */
    public function render()
    {
        if ((int) $this->arguments['promotionalPackage'] > 0) {
            $promotionalPackage = $this->promotionalPackageRepository->findByUid((int) $this->arguments['promotionalPackage']);
            if ($promotionalPackage instanceof PromotionalPackage) {
                return str_replace('###PRICE###', '€' . number_format($promotionalPackage->getPrice(), 0, ',', '.'), $this->arguments['description']);
            } else {
                return $this->arguments['description'];
            }
        }
        return $this->arguments['description'];
    }
}
