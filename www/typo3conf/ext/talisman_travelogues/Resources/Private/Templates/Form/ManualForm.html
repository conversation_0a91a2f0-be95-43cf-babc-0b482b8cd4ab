<f:layout name="Form" />

<f:section name="Content">
    <ul class="nav nav-tabs">
        <li>
            <f:link.action action="stepTwo">
                <span class="count">1.</span>
                <span class="hidden-xs hidden-sm"><f:translate id="form.nav.details" /></span>
                <span class="sprite-icon-48 user hidden-md hidden-lg"></span>
            </f:link.action>
        </li>
        <li class="active">
            <span class="count">2.</span>
            <span class="hidden-xs hidden-sm"><f:translate id="form.nav.travelogue" /></span>
            <span class="sprite-icon-48 details hidden-md hidden-lg"></span>
        </li>
    </ul>
    <div class="nav-tabs-box">
        <f:form action="handleManualForm" object="{request}" objectName="request" class="form" additionalAttributes="{autocomplete: 'off'}" enctype="multipart/form-data">
            <f:render
                partial="Form/TextField"
                arguments="{
                    property: 'destinations',
                    formObjectName: 'request',
                    required: 1,
                    label: '{f:translate(id: \'form.step_three.destinations\')}'
                }" />
            <f:render
                partial="Form/TextField"
                arguments="{
                    property: 'title',
                    formObjectName: 'request',
                    required: 1,
                    label: '{f:translate(id: \'form.step_three.title\')}'
                }" />
            <f:render
                partial="Form/TextArea"
                arguments="{
                    property: 'travelogue',
                    formObjectName: 'request',
                    required: 1,
                    label: '{f:translate(id: \'form.step_three.travelogue\')}',
                    rows: 12,
                    cols: 10
                }" />
            <f:render
                partial="Form/UploadField"
                arguments="{
                    property: 'images',
                    formObjectName: 'request',
                    action: 'uploadDocument',
                    label: '{f:translate(id: \'form.step_three.upload_images\')}',
                    required: 0,
                    multiple: 1,
                    placeholder: '{f:translate(id: \'form.step_three.upload_images.placeholder\')}'
                }" />

            <label class="agree">
                <f:translate id="form.step_three.agree.title" /> <span class="required">*</span>
            </label>
            <f:render
                partial="Form/Checkbox"
                arguments="{
                    property: 'agree',
                    formObjectName: 'request',
                    value: 1,
                    label: '{f:translate(id: \'form.step_three.agree\')}'
                }" />

            <div class="row submit-row">
                <div class="col-sm-6 col-sm-push-6 text-right">
                    <button type="submit" class="btn btn-default btn-rubine pull-right">
                        <f:translate id="form.step_three.submit" />
                    </button>
                    <div class="clearfix"></div>
                </div>
                <div class="col-sm-6 col-sm-pull-6">
                    <span class="mandatory">* <f:translate id="form.label.mandatory" extensionName="TalismanTemplate" /></span>
                </div>
            </div>
        </f:form>
    </div>
    <script type="text/javascript">
        window.onload = function() {
            Talisman.Form.labels.multipleFilesSelected = '%d afbeelding(en) geselecteerd';
        }
    </script>
</f:section>
