<div class="col-sm-3 asset-container text-center">
    <label>
        <span>
            {asset.title}
        </span>
        <f:if condition="{asset.thumbnail}">
            <f:then>
                <img src="/{asset.thumbnail}" class="img-responsive" />
            </f:then>
            <f:else>
                <f:image src="EXT:koning_elvis_connector/Resources/Public/Images/no-thumbnail.png" class="img-responsive" />
            </f:else>
        </f:if>
        <f:translate id="assets.import" /> <f:form.checkbox name="assets[]" value="{asset.originalUrl}|{asset.title}" />
    </label>
</div>
