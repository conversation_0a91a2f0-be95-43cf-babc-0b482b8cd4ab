<?php
namespace Redkiwi\TalismanEsbConnector\Service\Esb;

/**
 * Service: Promotional package
 *
 * @package Redkiwi\TalismanEsbConnector\Esb\Service
 */
class PromotionalPackageService extends AbstractConnectorService
{
    /**
     * @param array $parameters
     * @return array
     * @throws \Exception
     */
    public function makeRequest(array $parameters = [])
    {
        $geoTreeRequest = $this->request('data/getpromotionalpackages');
        if ($geoTreeRequest['info']['http_code'] !== 200) {
            throw new \Exception('Request data/getpromotionalpackages failed with status code ' . $geoTreeRequest['info']['http_code']);
        }

        $returnArray = [];
        $response = new \SimpleXMLElement($geoTreeRequest['response']);

        foreach ($response->additionalData->entries->entry as $promotionalPackage) {
            $returnArray[] = [
                'id' => (int) $promotionalPackage->id,
                'name' => (string) $promotionalPackage->name,
                'price' => (string) $promotionalPackage->totalPriceIncludingTax
            ];
        }
        return $returnArray;
    }
}
