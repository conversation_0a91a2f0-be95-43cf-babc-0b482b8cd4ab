<?php
namespace Redkiwi\TalismanLikes\Frontend;

use TYPO3\CMS\Frontend\Authentication\FrontendUserAuthentication;
use TYPO3\CMS\Frontend\Utility\EidUtility;

class UserData implements \TYPO3\CMS\Core\SingletonInterface
{

    /**
     * Static data
     */
    const SESSION_KEY = 'talisman_likes_user';

    /**
     * Used constants for readability
     */
    const SESSION_TYPE_LOGGED_IN = 'user';
    const SESSION_TYPE_LOGGED_OUT = 'ses';

    /** @var array */
    protected $likes;

    /**
     * UserData constructor.
     */
    public function __construct()
    {
        $this->constructLikes();
    }

    /**
     * Return likes
     *
     * @return array
     */
    public function constructLikes()
    {
        if ($this->likes === null) {
            switch ($this->getSessionType()) {
                case self::SESSION_TYPE_LOGGED_OUT:
                    // If no user logged in, use session
                    $this->likes = $this->getFrontendUserAuthentication()->getKey(self::SESSION_TYPE_LOGGED_OUT, self::SESSION_KEY) ?: [];
                    break;
                case self::SESSION_TYPE_LOGGED_IN:
                    // If user is logged in, use user data
                    $this->likes = $this->getFrontendUserAuthentication()->getKey(self::SESSION_TYPE_LOGGED_IN, self::SESSION_KEY) ?: [];

                    // If user is logged in, and has session data, combine both data sets
                    $storedLikes = $this->getFrontendUserAuthentication()->getKey(self::SESSION_TYPE_LOGGED_OUT, self::SESSION_KEY) ?: [];
                    if (!empty($storedLikes)) {
                        \TYPO3\CMS\Core\Utility\ArrayUtility::mergeRecursiveWithOverrule($this->likes, $storedLikes);
                        $this->getFrontendUserAuthentication()->setKey(self::SESSION_TYPE_LOGGED_OUT, self::SESSION_KEY, null);
                        $this->saveLikes();
                    }

                    break;
            }
        }
        return $this->likes;
    }

    /**
     * Store likes in user session
     */
    public function saveLikes()
    {
        $this->getFrontendUserAuthentication()->setKey($this->getSessionType(), self::SESSION_KEY, $this->likes);
        $this->getFrontendUserAuthentication()->storeSessionData();
        return true;
    }

    /**
     * @return array
     */
    public function getLikes()
    {
        return $this->likes;
    }

    /**
     * @param integer $reference
     * @return boolean
     */
    public function hasLike($reference)
    {
        $reference = (int)$reference;
        return ($reference > 0 && isset($this->likes[$reference]));
    }

    /**
     * @param integer $reference
     * @return boolean
     */
    public function removeLike($reference)
    {
        $reference = (int)$reference;
        if ($this->hasLike($reference)) {
            unset($this->likes[$reference]);
            return $this->saveLikes();
        }
        return false;
    }

    /**
     * @param integer $reference
     * @param $message
     * @return bool
     */
    public function addLike($reference, $message)
    {
        $reference = (int)$reference;
        $this->likes[$reference] = (string)$message;
        return $this->saveLikes();
    }

    /**
     * @return \TYPO3\CMS\Frontend\Controller\TypoScriptFrontendController
     */
    protected function getTypoScriptFrontendController()
    {
        return $GLOBALS['TSFE'];
    }

    /**
     * @return string
     */
    protected function getSessionType()
    {
        if (is_array($this->getFrontendUserAuthentication()->user) && !empty($this->getFrontendUserAuthentication()->user['uid'])) {
            return self::SESSION_TYPE_LOGGED_IN;
        } else {
            return self::SESSION_TYPE_LOGGED_OUT;
        }
    }

    /**
     * @return FrontendUserAuthentication
     */
    protected function getFrontendUserAuthentication()
    {
        // set static value cause this is not class specific
        static $frontendUserAuthentication;
        if ($frontendUserAuthentication === null) {
            if ($this->getTypoScriptFrontendController() !== null) {
                $frontendUserAuthentication = $this->getTypoScriptFrontendController()->fe_user;
            } else {
                $frontendUserAuthentication = EidUtility::initFeUser();
            }
        }
        return $frontendUserAuthentication;
    }
}
