<?php
namespace Redkiwi\TalismanAppointment\Domain\DataTransfer;

/**
 * Data transfer: Appointment
 *
 * @package Redkiwi\TalismanAppointment\Domain\DataTransfer
 */
class Appointment
{
    /**
     * @var string
     * @validate NotEmpty
     */
    protected $name;

    /**
     * @var string
     * @validate NotEmpty, <PERSON>ailAddress
     */
    protected $email;

    /** @var string */
    protected $known;

    /**
     * @var string
     * @validate NotEmpty
     */
    protected $phone;

    /** @var string */
    protected $preferredReachDateAnswer;

    /** @var string */
    protected $preferredReachTimeAnswer;

    /** @var string */
    protected $preferredReachTimeOtherAnswer;

    /**
     * @validate NotEmpty
     * @var \Redkiwi\TalismanPage\Domain\Model\Page
     */
    protected $destination;

    /** @var string */
    protected $description;

    /**
     * @return string
     */
    public function getName()
    {
        return $this->name;
    }

    /**
     * @param string $name
     * @return void
     */
    public function setName($name)
    {
        $this->name = $name;
    }

    /**
     * @return string
     */
    public function getEmail()
    {
        return $this->email;
    }

    /**
     * @param string $email
     * @return void
     */
    public function setEmail($email)
    {
        $this->email = $email;
    }

    /**
     * @return string
     */
    public function getKnown()
    {
        return $this->known;
    }

    /**
     * @param string $known
     * @return void
     */
    public function setKnown($known)
    {
        $this->known = $known;
    }

    /**
     * @return string
     */
    public function getPhone()
    {
        return $this->phone;
    }

    /**
     * @param string $phone
     * @return void
     */
    public function setPhone($phone)
    {
        $this->phone = $phone;
    }

    /**
     * @return string
     */
    public function getPreferredReachDateAnswer()
    {
        return $this->preferredReachDateAnswer;
    }

    /**
     * @param string $preferredReachDateAnswer
     */
    public function setPreferredReachDateAnswer($preferredReachDateAnswer)
    {
        $this->preferredReachDateAnswer = $preferredReachDateAnswer;
    }

    /**
     * @return string
     */
    public function getPreferredReachTimeAnswer()
    {
        return $this->preferredReachTimeAnswer;
    }

    /**
     * @param string $preferredReachTimeAnswer
     */
    public function setPreferredReachTimeAnswer($preferredReachTimeAnswer)
    {
        $this->preferredReachTimeAnswer = $preferredReachTimeAnswer;
    }

    /**
     * @return string
     */
    public function getPreferredReachTimeOtherAnswer()
    {
        return $this->preferredReachTimeOtherAnswer;
    }

    /**
     * @param string $preferredReachTimeOtherAnswer
     */
    public function setPreferredReachTimeOtherAnswer($preferredReachTimeOtherAnswer)
    {
        $this->preferredReachTimeOtherAnswer = $preferredReachTimeOtherAnswer;
    }

    /**
     * @return \Redkiwi\TalismanPage\Domain\Model\Page
     */
    public function getDestination()
    {
        return $this->destination;
    }

    /**
     * @param \Redkiwi\TalismanPage\Domain\Model\Page $destination
     * @return void
     */
    public function setDestination($destination)
    {
        $this->destination = $destination;
    }

    /**
     * @return string
     */
    public function getDescription()
    {
        return $this->description;
    }

    /**
     * @param string $description
     * @return void
     */
    public function setDescription($description)
    {
        $this->description = $description;
    }
}
