Talisman.Extensions.TalismanInspiration = {

    /**
     * @return void
     */
    init: function () {
        if ($('.talisman-inspiration-overview').length > 0) {
            this.overview.init();
        }
    },

    overview: {
        /**
         * Static details of rendering
         */
        dataTarget: '/index.php?eID=talisman_inspiration_data',
        layouts: {
            'layout1': '',
            'layout2': '',
            'layout3': ''
        },

        /**
         * Current workflow request
         */
        type: 0,
        page: 1,
        request: null,
        information: {},
        items: [],

        iteration: {
            // Start from layout ID
            layout: 1
        },

        /**
         * Initialize the overview page
         */
        init: function () {
            var self = this,
                container = $('.talisman-inspiration-overview');

            // set static info for request
            self.type = container.data('type');
            // Set layouts for iteration
            self.setLayouts();
            // Now register filter controls
            self.registerFilterControl();

            // Initial lookup for filtering
            self.findInspirationalPages({});

            // Set default filter and reset hash
            self.getDefaultParameters();
            // Make sure inline styles are removed
            container.find('.overview-items').removeAttr('style');
        },

        /**
         * Register Filtering controls
         */
        registerFilterControl: function () {
            var self = this,
                actions = $('.talisman-inspiration-overview .filters-header-container'),
                form = $('.talisman-inspiration-filters-form');

            if (form.length > 0) {
                if (actions.length > 0) {
                    actions.find('.toggle-active').affix({
                        offset: {
                            top: 45
                        }
                    });

                    actions.on('click', '.reset-all-filters', function () {
                        form.find('select, input').each(function (_i, object) {
                            self.resetField($(object));
                        });
                    });

                    var toggles = $('[data-filter-toggle-class]');
                    actions.on('click', '.toggle-filter-classes', function () {
                        if ($(this).hasClass('scroll-up')) {
                            $('html,body').animate({scrollTop: 40}, 'slow');
                        }

                        toggles.each(function (_i, element) {
                            element = $(element);
                            var current = $(element).attr('class'),
                                target = $(element).data('filter-toggle-class');

                            // Swap active classes with toggle
                            element.attr('class', target);
                            element.data('filter-toggle-class', current);
                        });
                    });

                    if ($(window).width() <= Talisman.Layout.screenSizes.sm) {
                        actions.find('.toggle-inactive .toggle-filter-classes').trigger('click');
                    }
                }

                form.on('submit', function (e) {
                    e.preventDefault();
                    self.findInspirationalPages({
                        filter: form.serialize()
                    });
                });

                var filters = $('.talisman-inspiration-filters');
                filters.on('change', 'select, input', function () {
                    // Add/Remove filter to active container
                    var field = $(this),
                        key, label,
                        tagName = field.prop('tagName');

                    if (tagName === 'SELECT') {
                        key = field.attr('id');
                        if (field.val()) {
                            label = field.find('option:selected').text();
                        }
                    } else {
                        key = field.attr('id');
                        if (field.hasClass('input-checkbox') && field.is(':checked')) {
                            label = $('label[for="' + key + '"]').text();
                        } else if (field.hasClass('ratio-slider') && field.val() > 3) {
                            label = field.data('label');
                        }
                    }

                    if (key && typeof key !== 'undefined') {
                        // Remove old key when retrieved
                        $('#active-' + key).remove();

                        var activeContainer = $('.filters-header-container .filters-header-actions .current-filters');
                        if (label && typeof label !== 'undefined') {
                            $('<span id="active-' + key + '" class="item filter">' + label + ' <i class="icon sprite-icon-20 cross"></i></span>')
                                .on('click', function () {
                                    $('html,body').animate({scrollTop: 40}, 'slow');
                                    self.resetField(field)
                                })
                                .appendTo(activeContainer);
                        }
                    }

                    form.trigger('submit');
                });
            }
        },

        /**
         * Reset field to default value
         *
         * @param field
         */
        resetField: function (field) {
            if (typeof field !== 'object') {
                return;
            }

            var tagName = field.prop('tagName');
            if (tagName === 'SELECT') {
                field.find('option').prop('selected', function () {
                    return this.defaultSelected;
                });
            } else {
                if (field.hasClass('input-checkbox')) {
                    field.prop('checked', false);
                } else if (field.hasClass('ratio-slider')) {
                    var value = field.data('slider-value');
                    field.slider('setValue', value);
                    field.val(value);
                } else {
                    field.val('');
                }
            }
            // Always trigger change when resetting fields
            field.trigger('change');
        },

        /**
         * Get default parameters and reset hash
         */
        getDefaultParameters: function () {
            var form = $('.talisman-inspiration-filters-form');
            if (form.length > 0) {
                var filters = form.find('.talisman-inspiration-filters');

                // get hashbang
                var hash = window.location.hash;
                if (hash.indexOf('#!/filter/') === 0) {
                    var activeCategories = hash.replace('#!/filter/', '').split('/').filter(function (v) {
                        return v !== ''
                    });
                    $.each(activeCategories, function (_i, category) {
                        var option = filters.find('option[value="' + category + '"]');
                        // If category is a dropdown
                        if (option.length > 0) {
                            option.parent('select').val(category).change();
                        } else {
                            var container = filters.find('.filter-category-' + category);
                            if (container.length > 0) {
                                // If category is a checkbox
                                if (container.hasClass('type-select-checkbox')) {
                                    container.find('input[type=checkbox]').prop('checked', true).change();
                                    // If category is a rating
                                } else if (container.hasClass('type-select-ratio')) {
                                    var ratings = container.closest('.filter-options').find('.type-select-ratio');
                                    ratings.each(function (_ir, rating) {
                                        rating = $(rating);
                                        var slider = rating.find('.ratio-slider');
                                        var value = (rating.hasClass('filter-category-' + category) ? 5 : 1);
                                        slider.slider('setValue', value);
                                        slider.val(value).change();
                                    });
                                }
                            }
                        }
                    });

                    // Remove hashbang
                    window.location.hash = '';
                    // slice off the remaining '#' in HTML5:
                    if (typeof window.history.replaceState === 'function') {
                        history.replaceState({}, '', window.location.href.slice(0, -1));
                    }
                }
            }
        },

        /**
         * Find all inspirational pages based on filtering
         */
        findInspirationalPages: function (parameters) {
            var self = this, loadingContainer = $('.overview-loading');
            loadingContainer.toggleClass('hide', false);

            self.resetOverview(function () {
                self.requestData(parameters).done(function (data) {
                    if (data) {
                        self.information = data.information;
                        if (data['error']) {
                            self.displayMessage(data['error']);
                        } else {
                            if (data['items']) {
                                jQuery.each(data.items, function (index, item) {
                                    self.items.push(item);
                                });

                                self.displayItems();
                                self.registerInfiniteScrolling();
                            }
                        }

                    }
                }).fail(function (xhr) {
                    //If either of these are true, then it's not a true error and we don't care
                    if (xhr.status === 0 || xhr.readyState === 0) {
                        return;
                    }
                    // Display error message
                    self.displayMessage();
                }).always(function () {
                    loadingContainer.toggleClass('hide', true);
                });
            });
        },

        /**
         * Reset overview page
         *
         * @returns {Talisman.Extensions.TalismanInspiration}
         */
        resetOverview: function (callback) {
            var self = this;

            // Reset page to 1
            self.page = 1;
            // Reset displayed layout to 1
            self.iteration.layout = 1;
            // Remove request information
            self.information = {};
            // Remove cache items
            while (self.items.length) {
                self.items.pop();
            }

            // Abort call
            if (self.request !== null) {
                self.request.abort();
            }

            // Remove infinite scroll lookup
            $(window).off('scroll.talisman-inspiration');

            // Clean container
            $('.talisman-inspiration-overview .overview-items').fadeOut(50, function () {
                $(this).empty();
                callback();
            });

            return self;
        },

        /**
         * Display items from cache
         */
        displayItems: function () {
            var self = this,
                remainingItems = self.items.length,
                totalLayouts = Object.keys(self.layouts).length,
                forceDisplay = (self.information.current_page && self.information.last_page) ? self.information.current_page == self.information.last_page : false,
                container = $('.talisman-inspiration-overview .overview-items').stop().fadeIn(100);

            // Loop through all layouts and place items
            while (remainingItems > 0) {
                var layout = self.iteration.layout;
                var template = self.getLayout(layout);
                var renderItems = template.data('items');

                if (renderItems > 0) {
                    // It should not render the layouts when template cannot be filled fully
                    if (forceDisplay === false && renderItems > remainingItems) {
                        break;
                    }

                    // Make sure a new object is used..
                    template = template.clone();

                    // Loop through each possible render item
                    for (var i = 1; i <= renderItems; i++) {
                        var item = self.items.shift();
                        remainingItems--;
                        self.fillItemTemplate(template.find('.list-item-' + i), item);
                    }

                    // Append to view
                    container.append(template.html());
                    self.iteration.layout++;
                    if (self.iteration.layout > totalLayouts) {
                        // reset iteration layout to 1 again
                        self.iteration.layout = 1;
                    }
                } else {
                    // The rendering is not as intended..
                    break;
                }
            }

            // Dynamic lookup for all new liked objects
            var objects = container.find('.talisman-interactive-enabled');
            if (objects.length > 0 && typeof Talisman.Extensions.TalismanLikes.initiateLikedObjects === 'function') {
                Talisman.Extensions.TalismanLikes.initiateLikedObjects(objects);
                objects.removeClass('talisman-interactive-enabled');
            }
        },

        /**
         * Display message in item container
         *
         * @param message
         */
        displayMessage: function (message) {
            var self = this,
                container = $('.talisman-inspiration-overview .overview-items').stop().fadeIn(100);

            if (!message) {
                message = {
                    severity: 'info',
                    header: 'Onbekend probleem',
                    description: 'De data kan niet worden opgehaald.'
                }
            }

            $('<div class="alert alert-' + message.severity + '">' +
                (message.header ? '<strong class="header">' + message.header + '</strong>' : '') +
                (message.description ? '<div class="description">' + message.description + '</div>' : '') +
                '</div>').appendTo(container);
        },

        /**
         * Invoke infinite scrolling when available
         */
        registerInfiniteScrolling: function () {
            var self = this,
                lastPage = (self.information.current_page && self.information.last_page) ? self.information.current_page == self.information.last_page : false;

            if (lastPage === false) {
                var container = $('.talisman-inspiration-overview .overview-items');
                $(window).on('scroll.talisman-inspiration', function () {
                    if (Talisman.Layout.elementIsInViewport(container) && self.request && self.request.readyState === 4) {
                        // Remove binding
                        $(window).off('scroll.talisman-inspiration');
                        var loadingContainer = $('.overview-loading');
                        loadingContainer.toggleClass('hide', false);

                        var parameters = {
                            filter: $('.talisman-inspiration-filters-form').serialize()
                        };
                        // get next page..
                        self.page++;

                        self.requestData(parameters).done(function (data) {
                            if (data) {
                                self.information = data.information;
                                if (data['items']) {
                                    jQuery.each(data.items, function (index, item) {
                                        self.items.push(item);
                                    });

                                    self.displayItems();
                                    self.registerInfiniteScrolling();
                                }
                            }
                        }).always(function () {
                            loadingContainer.toggleClass('hide', true);
                        })
                    }
                });
            }

        },

        /**
         * Request data for inspirational pages
         *
         * @param parameters
         * @return jqXHR
         */
        requestData: function (parameters) {
            var self = this;
            if (!parameters) {
                parameters = {
                    type: self.type,
                    page: self.page
                }
            } else {
                parameters.type = self.type;
                parameters.page = self.page;
            }

            // Cancel ongoing request
            if (this.request !== null) {
                this.request.abort();
            }

            // Return and resolve request data
            return this.request = $.ajax({
                url: this.dataTarget,
                data: parameters
            });
        },

        /**
         * Fill item template based on given row
         *
         * @param selector {*}
         * @param row object
         */
        fillItemTemplate: function (selector, row) {
            if (row) {
                selector.find('.item-link').attr('href', row['link']);
                selector.find('.item-title').text(row['title']);
                selector.find('.item-subtitle').text(row['subtitle']);
                selector.find('.item-image').attr('src', row['image']);

                if (row['reference'] > 0 && typeof Talisman.Extensions.TalismanLikes.enable === 'function') {
                    Talisman.Extensions.TalismanLikes.enable(selector, row['title'], row['subtitle'], row['reference']);
                }
            } else {
                selector.remove();
            }
        },

        /**
         * @param layout integer
         * @returns {*}
         */
        getLayout: function (layout) {
            return this.layouts['layout' + layout];
        },

        /**
         * Configure layout and remove from dom
         */
        setLayouts: function () {
            this.layouts.layout1 = $('#inspiration-item-template-1').remove();
            this.layouts.layout2 = $('#inspiration-item-template-2').remove();
            this.layouts.layout3 = $('#inspiration-item-template-3').remove();
        }
    }

};

$(document).ready(function () {
    Talisman.Extensions.TalismanInspiration.init();
});

