{namespace k=Keizer\KoningLibrary\ViewHelper}
<f:layout name="Default" />

<f:section name="Content">
    <h2>
        <f:translate id="sign_up.confirmation_mail_sent.header" />
    </h2>

    <div class="nav-tabs-box with-border confirmation-mail-sent">
        <p>
            <f:format.nl2br><f:translate id="sign_up.confirmation_mail_sent.text" arguments="{0: '{f:translate(id: \'sign_up.show.salutation.{user.salutation}\')}', 1: user.fullLastName, 2: user.username}" /></f:format.nl2br>
        </p>
    </div>
</f:section>
