<html xmlns:f="http://typo3.org/ns/TYPO3/CMS/Fluid/ViewHelpers"
    xmlns:likes="http://typo3.org/ns/Redkiwi/TalismanLikes/ViewHelpers"
    data-namespace-typo3-fluid="true">
<div class="talisman-travelogues list {f:if(condition: '{blockMargin}', then: 'block-margin')}">
    <f:for each="{travelogues}" as="travelogue">
        <div class="talisman-content-widget link">
            <f:link.typolink parameter="{travelogue.uid}">
                <div class="row">
                    <div class="col-sm-push-4 col-sm-8 image">
                        <f:if condition="{travelogue.images.0}">
                            <figure>
                                <f:image
                                    image="{travelogue.images.0}"
                                    alt="{travelogue.images.0.properties.alt}"
                                    title="{travelogue.images.0.properties.title}"
                                    height="263c"
                                    width="490c"
                                    class="img-responsive"/>
                                <div class="gradient"></div>
                            </figure>
                        </f:if>
                    </div>
                    <div class="col-sm-pull-8 col-sm-4 abstract">
                        <div class="inner">
                            <h3>{travelogue.title}</h3>
                        </div>
                    </div>
                    <div class="bottom">
                        <f:if condition="{travelogue.author}">
                            <span>{travelogue.author}</span><br>
                        </f:if>
                        <f:if condition="{travelogue.date}">
                            <time>
                                <f:translate id="list.date" />
                                <f:format.date format="%d.%m.%Y">{travelogue.date}</f:format.date>
                            </time>
                        </f:if>
                    </div>
                </div>
            </f:link.typolink>
        </div>
    </f:for>
</div>
</html>
