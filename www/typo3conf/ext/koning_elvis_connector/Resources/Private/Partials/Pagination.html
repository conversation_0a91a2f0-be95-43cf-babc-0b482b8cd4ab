<f:translate id="show.pagination_info" arguments="{0: assets.pagination.showing.from, 1: assets.pagination.showing.to, 2: assets.pagination.total}" />
<ul class="pagination pagination-block">
    <f:if condition="{assets.pagination.previousButton.show}">
        <f:then>
            <li>
                <f:link.action action="show" arguments="{rootFolder: rootFolder, browseAssets: 1, start: assets.pagination.previousButton.start, rteId: rteId, assetTypes: selectedAssetTypes, containerId: session.containerId}" section="assets">
                    <span aria-hidden="true">&laquo;</span>
                </f:link.action>
            </li>
        </f:then>
        <f:else>
            <li class="disabled">
                <span>
                    <span aria-hidden="true">&laquo;</span>
                </span>
            </li>
        </f:else>
    </f:if>
    <f:if condition="{assets.pagination.nextButton.show}">
        <f:then>
            <li>
                <f:link.action action="show" arguments="{rootFolder: rootFolder, browseAssets: 1, start: assets.pagination.nextButton.start, rteId: rteId, assetTypes: selectedAssetTypes, containerId: session.containerId}" section="assets">
                    <span aria-hidden="true">&raquo;</span>
                </f:link.action>
            </li>
        </f:then>
        <f:else>
            <li class="disabled">
                <span>
                    <span aria-hidden="true">&raquo;</span>
                </span>
            </li>
        </f:else>
    </f:if>
</ul>
