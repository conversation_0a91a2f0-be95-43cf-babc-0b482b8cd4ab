Talisman.Extensions.TalismanAjaxLogin = {

    /**
     * @var object
     */
    runningTasks: {
        login: false,
        logout: false
    },

    /**
     * @return void
     */
    init: function() {
        $('.talisman-ajax-login button[type="submit"]').click(function() {
            Talisman.Extensions.TalismanAjaxLogin.login();
            return false;
        });

        $('a[data-action="logout"]').click(function() {
            Talisman.Extensions.TalismanAjaxLogin.logout();
            return false;
        });
    },

    /**
     * @return void
     */
    login: function() {
        if (!Talisman.Extensions.TalismanAjaxLogin.runningTasks.login) {
            $.ajax({
                type: 'POST',
                url: $('.talisman-ajax-login form').attr('action'),
                dataType: 'json',
                data: {
                    logintype: 'login',
                    pass: $('.talisman-ajax-login input[name="tx_talismanajaxlogin_form[pass]"]').val(),
                    user: $('.talisman-ajax-login input[name="tx_talismanajaxlogin_form[user]"]').val(),
                    pid: $('.talisman-ajax-login input[name="tx_talismanajaxlogin_form[pid]"]').val(),
                    referrer: $('.talisman-ajax-login input[name="tx_talismanajaxlogin_form[referrer]"]').val(),
                    permalogin: $('.talisman-ajax-login input[name="tx_talismanajaxlogin_form[permalogin]"]').is(':checked') ? 1 : 0
                },
                beforeSend: function () {
                    Talisman.Extensions.TalismanAjaxLogin.runningTasks.login = true;
                    $('.talisman-ajax-login .alert').remove();
                    $('.talisman-ajax-login .form-group').removeClass('has-error');
                    $('.talisman-ajax-login button[type="submit"]').removeClass('btn-rubine').addClass('btn-inactive');
                },
                success: function (response) {
                    Talisman.Extensions.TalismanAjaxLogin.runningTasks.login = false;
                    if (response.success) {
                        window.location = response.redirectUrl;
                    } else {
                        $('.talisman-ajax-login input[name="tx_talismanajaxlogin_form[user]"]')
                            .after('<div class="alert alert-danger"> <i class="fa fa-exclamation-triangle"></i> ' + $('.talisman-ajax-login form').attr('data-error-message') + ' </div>')
                            .parent().addClass('has-error');
                        $('.talisman-ajax-login button[type="submit"]').removeClass('btn-inactive').addClass('btn-rubine');
                    }
                }
            });
        }
    },

    /**
     * @return void
     */
    logout: function() {
        if (!Talisman.Extensions.TalismanAjaxLogin.runningTasks.logout) {
            $.ajax({
                type: 'POST',
                url: $('a[data-action="logout"]').attr('data-url'),
                dataType: 'json',
                data: {
                    logintype: 'logout',
                    referrer: $('a[data-action="logout"]').attr('data-referrer')
                },
                beforeSend: function () {
                    Talisman.Extensions.TalismanAjaxLogin.runningTasks.logout = true;
                },
                success: function (response) {
                    Talisman.Extensions.TalismanAjaxLogin.runningTasks.logout = false;
                    window.location = response.redirectUrl;
                }
            });
        }
    }
};

$(document).ready(function() {
    Talisman.Extensions.TalismanAjaxLogin.init();
});
