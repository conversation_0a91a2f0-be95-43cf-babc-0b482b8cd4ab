<?php
namespace Redkiwi\TalismanTemplate\Utility;

use TYPO3\CMS\Core\Database\ConnectionPool;
use TYPO3\CMS\Core\Database\Query\QueryBuilder;
use TYPO3\CMS\Core\Database\Query\Restriction\BackendWorkspaceRestriction;
use TYPO3\CMS\Core\Database\Query\Restriction\DeletedRestriction;
use TYPO3\CMS\Core\Resource\FileReference;
use TYPO3\CMS\Core\Resource\ResourceFactory;
use TYPO3\CMS\Core\Utility\GeneralUtility;

/**
 * Utility: Resource download
 *
 * @package Redkiwi\TalismanTemplate\Utility
 */
class ResourceUtility
{
    /**
     * Try to retrieve all reference objects
     *
     * @param integer $uid
     * @param string $table
     * @param string $field
     * @return array<\TYPO3\CMS\Core\Resource\FileReference>
     */
    public static function getReferenceObjects($uid, $table, $field)
    {
        $fileReferenceObjects = [];

        /** @var QueryBuilder $queryBuilder */
        $queryBuilder = GeneralUtility::makeInstance(ConnectionPool::class)->getQueryBuilderForTable('sys_file_reference');
        $queryBuilder
            ->getRestrictions()
            ->removeAll()
            ->add(GeneralUtility::makeInstance(DeletedRestriction::class))
            ->add(GeneralUtility::makeInstance(BackendWorkspaceRestriction::class));

        $references = $queryBuilder->select('uid')
            ->from('sys_file_reference')
            ->where(
                $queryBuilder->expr()->eq('tablenames', $queryBuilder->createNamedParameter('sys_file_reference')),
                $queryBuilder->expr()->eq('fieldname', $queryBuilder->createNamedParameter('sys_file_reference')),
                $queryBuilder->expr()->eq('uid_foreign', $queryBuilder->createNamedParameter(intval($uid), \PDO::PARAM_INT))
            )
            ->execute();

        if (!empty($references)) {
            foreach ($references as $reference) {
                $referenceUid = (int)$reference['uid'];
                if ($referenceUid > 0) {
                    try {
                        $referenceObject = ResourceFactory::getInstance()->getFileReferenceObject($referenceUid);
                        if ($referenceObject instanceof FileReference) {
                            $fileReferenceObjects[] = $referenceObject;
                        }
                    } catch (\Exception $e) {
                    }
                }
            }
        }
        return $fileReferenceObjects;
    }

    /**
     * Stream context to view and stop processing
     *
     * @param string $fileName
     * @param string $content
     * @param array $additionalHeaders
     */
    public static function stream($fileName, $content, $additionalHeaders = [])
    {
        foreach ($additionalHeaders as $type => $value) {
            header($type . ': ' . $value);
        }

        // Clean filename, stripped from :BasicFileUtility::cleanFileName()
        $fileName = preg_replace('/[\\x00-\\x2C\\/\\x3A-\\x3F\\x5B-\\x60\\x7B-\\xBF]/u', '_', trim($fileName));
        header('Content-Disposition: inline; filename=' . $fileName . ';');

        exit($content);
    }
}
