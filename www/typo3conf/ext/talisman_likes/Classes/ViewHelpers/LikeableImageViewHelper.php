<?php
namespace Redkiwi\TalismanLikes\ViewHelpers;

use Redkiwi\TalismanEsbConnector\Domain\Model\PromotionalPackage;
use Redkiwi\TalismanEsbConnector\Domain\Repository\PromotionalPackageRepository;
use TYPO3\CMS\Core\Utility\GeneralUtility;
use TYPO3\CMS\Extbase\Object\ObjectManager;
use TYPO3Fluid\Fluid\Core\Rendering\RenderingContextInterface;
use TYPO3Fluid\Fluid\Core\ViewHelper\AbstractViewHelper;
use TYPO3Fluid\Fluid\Core\ViewHelper\Traits\CompileWithRenderStatic;

/**
 * ViewHelper: Likeable Image via reference object
 *
 * @package Redkiwi\TalismanLikes\Service
 */
class LikeableImageViewHelper extends AbstractViewHelper
{
    use CompileWithRenderStatic;

    /**
     * This prevents double escaping as the output is encoded in DebuggerUtility::var_dump
     *
     * @var bool
     */
    protected $escapeChildren = false;

    /**
     * Output of this viewhelper is already escaped
     *
     * @var bool
     */
    protected $escapeOutput = false;

    /**
     * Initialize arguments.
     *
     * @throws \TYPO3Fluid\Fluid\Core\ViewHelper\Exception
     */
    public function initializeArguments()
    {
        $this->registerArgument('reference', 'object', 'Pass a file reference');
    }

    /**
     * Render object after fluid caching
     *
     * @param array $arguments
     * @param \Closure $renderChildrenClosure
     * @param RenderingContextInterface $renderingContext
     * @return mixed
     */
    public static function renderStatic(array $arguments, \Closure $renderChildrenClosure, RenderingContextInterface $renderingContext)
    {
        $variables = [
            'like' => static::getLikeButton($arguments['reference'])
        ];
        $templateVariableContainer = $renderingContext->getViewHelperVariableContainer();

        foreach ($variables as $aliasName => $value) {
            $templateVariableContainer->add(self::class, $aliasName, $value);
        }
        $output = $renderChildrenClosure();
        foreach ($variables as $aliasName => $value) {
            $templateVariableContainer->remove(self::class, $aliasName);
        }

        return static::wrapOutput($output, $arguments['reference']);
    }

    /**
     * @param string $output
     * @param \TYPO3\CMS\Core\Resource\FileReference $file
     * @return string
     */
    protected static function wrapOutput($output, $file)
    {
        if ($file instanceof \TYPO3\CMS\Core\Resource\FileReference) {
            $title = $file->getTitle();
            $description = null;
            if (!empty($title)) {
                $description = $file->getDescription();

                if (strpos($description, '###PRICE###') && $file->hasProperty('talisman_content_promotional_package')) {

                    $promotionalPackage = self::getPromotionalPackageRepository()->findByUid((int)$file->getProperty('talisman_content_promotional_package'));

                    if ($promotionalPackage instanceof PromotionalPackage) {
                        $description = str_replace('###PRICE###', '€' . number_format($promotionalPackage->getPrice(), 0, ',', '.'), $description);
                    }
                }
            } else {
                $page = \TYPO3\CMS\Backend\Utility\BackendUtility::getRecord(
                    'pages',
                    (int)$file->getReferenceProperty('pid'),
                    'title, subtitle'
                );
                $title = $page['title'];
                $description = $page['subtitle'];
            }

            $attributes = [
                'class' => 'talisman-likes-container',
                'data-title' => $title,
                'data-description' => $description,
                'data-reference' => $file->getUid(),
                'id' => 'like-' . $file->getUid()
            ];

            return '<div ' . GeneralUtility::implodeAttributes($attributes) . '>' . $output . '</div>';
        }

        return '<div>' . $output . '</div>';
    }

    /**
     * Generate like button for interaction
     *
     * @param \TYPO3\CMS\Core\Resource\FileReference $file
     * @return string
     */
    protected static function getLikeButton($file)
    {
        return ''; // TEMP while talisman_likes extensions has been disabled

        if ($file && (bool)$file->getReferenceProperty('talisman_likes_likeable') === true) {
            return '<div class="moodboard-entry"><span class="talisman-like sprite-icon-48 heart-white-outline" data-action="toggle-like"></span></div>';
        }

        return '';
    }

    /**
     * @return PromotionalPackageRepository
     */
    protected static function getPromotionalPackageRepository()
    {
        /** @var ObjectManager $objectManager */
        $objectManager = GeneralUtility::makeInstance(ObjectManager::class);

        return $objectManager->get(PromotionalPackageRepository::class);
    }
}