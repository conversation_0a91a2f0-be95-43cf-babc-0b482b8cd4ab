<?php
namespace Redkiwi\TalismanEsbConnector\Command;

use Redkiwi\TalismanEsbConnector\Utility\ConfigurationUtility;

/**
 * Command: Countries
 *
 * @package Redkiwi\TalismanEsbConnector\Command
 */
class CountriesCommandController extends \TYPO3\CMS\Extbase\Mvc\Controller\CommandController
{
    /**
     * @var \Redkiwi\TalismanEsbConnector\Service\Esb\CountryService
     * @inject
     */
    protected $esbCountryService;

    /**
     * @var \Redkiwi\TalismanEsbConnector\Service\TYPO3\CountryService
     * @inject
     */
    protected $typo3CountryService;

    /**
     * @return bool
     * @throws \Exception
     */
    public function syncCommand()
    {
        if (ConfigurationUtility::isValid()) {
            $configuration = ConfigurationUtility::getEsbSettings();
            $countries = $this->esbCountryService->makeRequest([]);
            $this->typo3CountryService->sync($countries, $configuration['countryStoragePageUid']);
            return true;
        } else {
            throw new \Exception('Configuration invalid. Check Extension Manager');
        }
    }
}
