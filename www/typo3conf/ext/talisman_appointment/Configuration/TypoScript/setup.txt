plugin.tx_talismanappointment {
    mvc {
        callDefaultActionIfActionCantBeResolved = 1
    }
    view {
        templateRootPaths {
            20 = EXT:talisman_appointment/Resources/Private/Templates
        }
        partialRootPaths {
            10 = EXT:talisman_template/Resources/Private/Partials
            20 = EXT:talisman_appointment/Resources/Private/Partials
        }
        layoutRootPaths {
            10 = EXT:talisman_template/Resources/Private/Layouts
            20 = EXT:talisman_appointment/Resources/Private/Layouts
        }
    }
    settings {
        pages {
            destinationRoot = {$pages.destinations}
        }
    }
}
