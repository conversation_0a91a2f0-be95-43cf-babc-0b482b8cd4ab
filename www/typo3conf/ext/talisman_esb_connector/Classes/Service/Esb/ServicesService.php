<?php
namespace Redkiwi\TalismanEsbConnector\Service\Esb;

/**
 * Service: Services
 *
 * @package Redkiwi\TalismanEsbConnector\Esb\Service
 */
class ServicesService extends AbstractConnectorService
{
    /**
     * @param array $parameters
     * @return array
     * @throws \Exception
     */
    public function makeRequest(array $parameters = [])
    {
        $servicesRequest = $this->request('data/content', $parameters);
        if ($servicesRequest['info']['http_code'] !== 200) {
            throw new \Exception('Request data/content failed with status code ' . $servicesRequest['info']['http_code']);
        }

        $returnArray = [];
        $response = new \SimpleXMLElement($servicesRequest['response']);

        foreach ($response->additionalData->entries->entry as $entry) {
            $returnArray[] = [
                'travelStudioId' => (int) $entry->travelStudioId,
                'longName' => (string) $entry->longName,
                'shortName' => (string) $entry->shortName,
                'longitude' => (double) $entry->longitude,
                'latitude' => (double) $entry->latitude,
                'travelStudioGeoTreeId' => (int) $entry->travelStudioGeoTreeId
            ];
        }
        return $returnArray;
    }
}
