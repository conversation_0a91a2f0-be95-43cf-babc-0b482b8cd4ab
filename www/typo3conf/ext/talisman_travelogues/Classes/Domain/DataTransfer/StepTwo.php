<?php
namespace Redkiwi\TalismanTravelogues\Domain\DataTransfer;

/**
 * Data transfer: Step two
 *
 * @package Redkiwi\TalismanTravelogues\Domain\DataTransfer
 */
class StepTwo
{
    /**
     * @var string
     * @validate NotEmpty
     */
    protected $name;

    /**
     * @var string
     * @validate NotEmpty, <PERSON>ailAddress
     */
    protected $email;

    /**
     * @return string
     */
    public function getName()
    {
        return $this->name;
    }

    /**
     * @param string $name
     * @return void
     */
    public function setName($name)
    {
        $this->name = $name;
    }

    /**
     * @return string
     */
    public function getEmail()
    {
        return $this->email;
    }

    /**
     * @param string $email
     * @return void
     */
    public function setEmail($email)
    {
        $this->email = $email;
    }
}
