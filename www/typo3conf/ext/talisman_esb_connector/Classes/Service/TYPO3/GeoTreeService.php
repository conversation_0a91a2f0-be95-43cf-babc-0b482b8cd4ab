<?php
namespace Redkiwi\TalismanEsbConnector\Service\TYPO3;

/**
 * Service: Geo tree
 *
 * @package Redkiwi\TalismanEsbConnector\TYPO3\Service
 */
class GeoTreeService
{
    /** @var int */
    protected $destinationRootPageUid;

    /**
     * @param array $geoTree
     * @param int $destinationRootPageUid
     * @return void
     */
    public function sync(array $geoTree, $destinationRootPageUid)
    {
        $this->destinationRootPageUid = $destinationRootPageUid;

        foreach ($geoTree as $continent) {
            $continentPage = $this->getPageByGeoTreeId($continent['travelStudioId']);
            if (!$continentPage) {
                $this->createContinentPage($continent);
            } else {
                $this->updateContinentPage($continent, $continentPage['uid']);
            }
        }
    }

    /**
     * @param array $continent
     * @return void
     */
    protected function createContinentPage(array $continent)
    {
        $fields = [
            'pid' => $this->destinationRootPageUid,
            'doktype' => 4,
            'shortcut_mode' => 1,
            'tx_realurl_exclude' => 1
        ];
        $fields = array_merge($fields, $this->getStandardPageFields(), $this->getEsbConnectorFields($continent));

        $this->getDatabaseConnection()->exec_INSERTquery(
            'pages',
            $fields
        );

        $continentPageUid = $this->getDatabaseConnection()->sql_insert_id();
        if (!empty($continent['children'])) {
            $this->syncCountryPages($continent['children'], $continentPageUid);
        }
    }

    /**
     * @param array $continent
     * @param int $continentPageUid
     * @return void
     */
    protected function updateContinentPage(array $continent, $continentPageUid)
    {
        $fields = [
            'pid' => $this->destinationRootPageUid,
            'doktype' => 4,
            'shortcut_mode' => 1,
            'tx_realurl_exclude' => 1
        ];
        $fields = array_merge($fields, $this->getEsbConnectorFields($continent));
        unset($fields['crdate']);
        unset($fields['hidden']);

        $this->getDatabaseConnection()->exec_UPDATEquery(
            'pages',
            'uid = ' . (int) $continentPageUid,
            $fields
        );

        if (!empty($continent['children'])) {
            $this->syncCountryPages($continent['children'], $continentPageUid);
        }
    }

    /**
     * @param array $countries
     * @param int $continentPageUid
     * @return void
     */
    protected function syncCountryPages($countries, $continentPageUid)
    {
        foreach ($countries as $country) {
            $countryPage = $this->getPageByGeoTreeId($country['travelStudioId']);
            if (!$countryPage) {
                $this->createCountryPage($country, $continentPageUid);
            } else {
                $this->updateCountryPage($country, $countryPage['uid'], $continentPageUid);
            }
        }
    }

    /**
     * @param array $country
     * @param int $continentPageUid
     * @return void
     */
    protected function createCountryPage(array $country, $continentPageUid)
    {
        $fields = [
            'pid' => $continentPageUid,
            'doktype' => 4,
            'shortcut_mode' => 1
        ];
        $fields = array_merge($fields, $this->getStandardPageFields(), $this->getEsbConnectorFields($country));

        $this->getDatabaseConnection()->exec_INSERTquery(
            'pages',
            $fields
        );

        $countryPageUid = $this->getDatabaseConnection()->sql_insert_id();
        $this->createCountrySubPages($country, $countryPageUid);
    }

    /**
     * @param array $country
     * @param int $countryPageUid
     * @return void
     */
    protected function createCountrySubPages(array $country, $countryPageUid)
    {
        $countrySubPages = [
            ['title' => 'Ontdek ' .  $country['name'], 'subtitle' => $country['name']],
            ['title' => 'Rondreizen'],
            ['title' => 'Te doen'],
            ['title' => 'Accommodaties'],
            ['title' => 'Reisverslagen'],
            ['title' => 'Praktisch']
        ];

        foreach ($countrySubPages as $countrySubPage)
        {
            $fields = [
                'pid' => $countryPageUid,
                'doktype' => 1
            ];
            $fields = array_merge($fields, $countrySubPage, $this->getStandardPageFields());

            $this->getDatabaseConnection()->exec_INSERTquery(
                'pages',
                $fields
            );

            if ($countrySubPage['title'] === 'Te doen') {
                $talismanPageUid = $this->getDatabaseConnection()->sql_insert_id();
                $this->createTalismanSubpages($talismanPageUid);
            }
        }
    }

    /**
     * @param int $talismanPageUid
     * @return void
     */
    protected function createTalismanSubpages($talismanPageUid)
    {
        $talismanSubpages = [
            ['title' => 'Natuur', 'doktype' => 1],
            ['title' => 'Cultuur', 'doktype' => 1],
            ['title' => 'Strand', 'doktype' => 1],
            ['title' => 'Actief', 'doktype' => 1],
            ['title' => 'Familie', 'doktype' => 1],
            ['title' => 'Culinair', 'doktype' => 1],
            ['title' => 'Stad', 'doktype' => 1],
            ['title' => 'Superluxe', 'doktype' => 1],
            ['title' => 'Wellness', 'doktype' => 1],
            ['title' => 'Nieuw', 'doktype' => 254]
        ];

        foreach ($talismanSubpages as $talismanSubpage) {
            $fields = [
                'pid' => $talismanPageUid,
                'doktype' => 1
            ];

            $fields = array_merge($fields, $talismanSubpage, $this->getStandardPageFields());

            $this->getDatabaseConnection()->exec_INSERTquery(
                'pages',
                $fields
            );
        }
    }

    /**
     * @param array $country
     * @param int $countryPageUid
     * @param int $continentPageUid
     * @return void
     */
    protected function updateCountryPage(array $country, $countryPageUid, $continentPageUid)
    {
        $fields = ['pid' => $continentPageUid, 'doktype' => 4];
        $fields = array_merge($fields, $this->getEsbConnectorFields($country));
        unset($fields['crdate']);
        unset($fields['hidden']);

        $this->getDatabaseConnection()->exec_UPDATEquery(
            'pages',
            'uid = ' . (int) $countryPageUid,
            $fields
        );
    }

    /**
     * @param int $geoTreeId
     * @return array|bool
     */
    protected function getPageByGeoTreeId($geoTreeId)
    {
        $page = $this->getDatabaseConnection()->exec_SELECTgetSingleRow(
            'uid',
            'pages',
            'tx_talismanesbconnector_geo_tree_id = ' . (int) $geoTreeId . ' AND deleted = 0'
        );
        return (is_array($page)) ? $page : false;
    }

    /**
     * @return array
     */
    protected function getStandardPageFields()
    {
        return [
            'crdate' => time(),
            'tstamp' => time(),
            'perms_user' => 31,
            'perms_group' => 31,
            'perms_userid' => 1,
            'perms_groupid' => 1,
            'hidden' => 1
        ];
    }

    /**
     * @param array $object
     * @return array
     */
    protected function getEsbConnectorFields(array $object)
    {
        return [
            'title' => $object['name'],
            'tx_talismanesbconnector_geo_tree_id' => $object['travelStudioId'],
            'tx_talismanesbconnector_latitude' => $object['latitude'],
            'tx_talismanesbconnector_longitude' => $object['longitude']
        ];
    }

    /**
     * @return \TYPO3\CMS\Core\Database\DatabaseConnection
     */
    protected function getDatabaseConnection()
    {
        return $GLOBALS['TYPO3_DB'];
    }
}
