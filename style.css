body {
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
    font-family: Arial, sans-serif;
}

.main-container {
    border: 1px solid #ccc;
    padding: 15px;
    margin-bottom: 20px;
}

h1 {
    text-align: center;
}

h2 {
    color: #333;
}

table {
    width: 100%;
    border-collapse: collapse;
    margin: 10px 0;
}

th, td {
    border: 1px solid #999;
    padding: 8px;
    text-align: left;
}

th {
    background-color: #f0f0f0;
}

.order-info {
    background: #f8f8f8;
    padding: 10px;
    margin: 10px 0;
}

button {
    background: #0066cc;
    color: white;
    border: none;
    padding: 10px 15px;
    cursor: pointer;
}

button:hover {
    background: #0052a3;
}
/* PDF styling */
.document-header {
    margin-bottom: 20px;
}

.address-container {
    margin-bottom: 20px;
}

.addr-left {
    display: inline-block;
    width: 48%;
    vertical-align: top;
}

.addr-right {
    display: inline-block;
    width: 48%;
    vertical-align: top;
    margin-left: 2%;
}

.address-box {
    border: 1px solid #999;
    padding: 10px;
}

.items-table {
    width: 100%;
    margin: 15px 0;
}

#status-message {
    background: #ffebee;
    color: #c62828;
    padding: 10px;
    margin: 10px 0;
    border: 1px solid #ffcdd2;
}