#!/bin/bash

## Description: Syncs the project
## Usage: sync
## Example: "ddev sync"

echo "Running sync script on the host system."
date
echo "Start script"

#
# Pullit sql and files
#
echo "> Pulling database content... and files content..."
cd ./private/pullit/
sh pullit.sh -d
cd ../../
#
# Customize database content
#
echo "> Deleting all backend users..." | tee -a $LOG
echo "DELETE FROM be_users;" | ddev mysql | tee -a $LOG
echo "> Adding redkiwi admin user..." | tee -a $LOG
echo "INSERT INTO be_users (username, password, admin) VALUES('redkiwi', '\$P\$C0/Yo5fcaVsMqpi66ry4opA0RFlwZ.0', 1)" | ddev mysql | tee -a $LOG

date
echo "End script"