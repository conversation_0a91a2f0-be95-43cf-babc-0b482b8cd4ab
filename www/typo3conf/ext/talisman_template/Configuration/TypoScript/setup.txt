# Pagehits needed before Base
<INCLUDE_TYPOSCRIPT: source="FILE: EXT:pagehits/Configuration/TypoScript/setup.txt">

# Base
<INCLUDE_TYPOSCRIPT: source="DIR: ./Base/" extensions="txt">

# Library
<INCLUDE_TYPOSCRIPT: source="DIR: ./Library/" extensions="txt">

# Plugin static
<INCLUDE_TYPOSCRIPT: source="FILE: EXT:gridelements/Configuration/TypoScript/setup.typoscript">
<INCLUDE_TYPOSCRIPT: source="FILE: EXT:talisman_bootstrap_carousel/Configuration/TypoScript/setup.txt">
<INCLUDE_TYPOSCRIPT: source="FILE: EXT:talisman_content/Configuration/TypoScript/setup.typoscript">
<INCLUDE_TYPOSCRIPT: source="FILE: EXT:talisman_inspiration/Configuration/TypoScript/setup.txt">
<INCLUDE_TYPOSCRIPT: source="FILE: EXT:talisman_experts/Configuration/TypoScript/setup.txt">
<INCLUDE_TYPOSCRIPT: source="FILE: EXT:talisman_accommodations/Configuration/TypoScript/setup.txt">
<INCLUDE_TYPOSCRIPT: source="FILE: EXT:talisman_tiles/Configuration/TypoScript/setup.txt">
<INCLUDE_TYPOSCRIPT: source="FILE: EXT:talisman_travelogues/Configuration/TypoScript/setup.txt">
<INCLUDE_TYPOSCRIPT: source="FILE: EXT:talisman_quote_form/Configuration/TypoScript/setup.txt">
<INCLUDE_TYPOSCRIPT: source="FILE: EXT:talisman_newsletter/Configuration/TypoScript/setup.txt">
<INCLUDE_TYPOSCRIPT: source="FILE: EXT:talisman_user_registration/Configuration/TypoScript/setup.txt">
<INCLUDE_TYPOSCRIPT: source="FILE: EXT:talisman_ajax_login/Configuration/TypoScript/setup.txt">
<INCLUDE_TYPOSCRIPT: source="FILE: EXT:talisman_ask_a_question/Configuration/TypoScript/setup.txt">
<INCLUDE_TYPOSCRIPT: source="FILE: EXT:talisman_appointment/Configuration/TypoScript/setup.txt">
<INCLUDE_TYPOSCRIPT: source="FILE: EXT:talisman_map/Configuration/TypoScript/setup.txt">
<INCLUDE_TYPOSCRIPT: source="FILE: EXT:talisman_example_trip/Configuration/TypoScript/setup.txt">
<INCLUDE_TYPOSCRIPT: source="FILE: EXT:talisman_likes/Configuration/TypoScript/setup.txt">
<INCLUDE_TYPOSCRIPT: source="FILE: EXT:talisman_blog/Configuration/TypoScript/setup.txt">
<INCLUDE_TYPOSCRIPT: source="FILE: EXT:talisman_highlighted_specials/Configuration/TypoScript/setup.txt">
<INCLUDE_TYPOSCRIPT: source="FILE:EXT:seo/Configuration/TypoScript/XmlSitemap/setup.typoscript">
<INCLUDE_TYPOSCRIPT: source="FILE: EXT:solr/Configuration/TypoScript/Solr/setup.txt">

# Public extensions
<INCLUDE_TYPOSCRIPT: source="FILE:EXT:talisman_content/Configuration/TypoScript/setup.typoscript">

# Plugin overrides
<INCLUDE_TYPOSCRIPT: source="DIR: ./Plugin/" extensions="txt">
<INCLUDE_TYPOSCRIPT: source="FILE:./Extensions/Solr/setup.typoscript">

# Conditions
<INCLUDE_TYPOSCRIPT: source="FILE: ./Conditions/Setup.txt">
