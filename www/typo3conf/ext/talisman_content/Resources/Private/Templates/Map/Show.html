<div class="worldmap-container">
    <div id="worldmap"></div>
</div>

<script type="text/javascript" src="https://www.google.com/jsapi"></script>
<script type="text/javascript">
    google.load('visualization', '1', { packages:["geochart"] });
    var worldmapData = {javaScriptData -> f:format.raw()};

    window.onload = function() {
        Talisman.Extensions.TalismanDestinations.data = google.visualization.arrayToDataTable(worldmapData);
        Talisman.Extensions.TalismanDestinations.isoLinkMap = {isoLinkMap -> f:format.raw()};
        google.setOnLoadCallback(Talisman.Extensions.TalismanDestinations.init());
    };
</script>
