<?php
namespace Redkiwi\TalismanQuoteForm\Domain\Model\DataTransfer\Form;

/**
 * Data transfer: Quote Form
 *
 * @package Redkiwi\TalismanQuoteForm\Domain\Model\DataTransfer\Form
 */
class Quote extends AbstractForm
{
    use TrackingTrait;

    /** @var string  */
    protected $additionalInformation = '';

    /**
     * @var string
     * @validate NotEmpty
     */
    protected $city = '';

    /**
     * @validate \Redkiwi\TalismanQuoteForm\Validation\Validator\QuoteDestinationFieldValidator
     * @var \Redkiwi\TalismanPage\Domain\Model\Page
     */
    protected $destinationOne;

    /** @var \Redkiwi\TalismanPage\Domain\Model\Page */
    protected $destinationTwo;

    /** @var \Redkiwi\TalismanPage\Domain\Model\Page */
    protected $exampleTrip;

    /** @var string */
    protected $heardOfTalismanAnswer = '';

    /** @var string */
    protected $heardOfTalismanOtherAnswer = '';

    /**
     * @var string
     * @validate NotEmpty
     */
    protected $houseNumber = '';

    /** @var string */
    protected $houseNumberAddendum = '';

    /** @var string */
    protected $preferredReachDateAnswer = '';

    /** @var string */
    protected $preferredReachTimeAnswer = '';

    /** @var string */
    protected $preferredReachTimeOtherAnswer = '';

    /**
     * @var string
     * @validate NotEmpty, StringLength(minimum=10, maximum=14)
     */
    protected $phone = '';

    /**
     * @var string
     * @validate NotEmpty
     */
    protected $street = '';

    /** @var bool */
    protected $travelledBefore = false;

    /** @var bool */
    protected $unsure = false;

    /**
     * @var string
     * @validate NotEmpty
     */
    protected $zip = '';

    /**
     * @return string
     */
    public function getAdditionalInformation(): string
    {
        return $this->additionalInformation;
    }

    /**
     * @param string $additionalInformation
     */
    public function setAdditionalInformation(string $additionalInformation)
    {
        $this->additionalInformation = $additionalInformation;
    }

    /**
     * @return string
     */
    public function getCity()
    {
        return $this->city;
    }

    /**
     * @param string $city
     */
    public function setCity($city)
    {
        $this->city = $city;
    }

    /**
     * @return \Redkiwi\TalismanPage\Domain\Model\Page
     */
    public function getDestinationOne()
    {
        return $this->destinationOne;
    }

    /**
     * @param \Redkiwi\TalismanPage\Domain\Model\Page $destinationOne
     */
    public function setDestinationOne($destinationOne)
    {
        $this->destinationOne = $destinationOne;
    }

    /**
     * @return \Redkiwi\TalismanPage\Domain\Model\Page
     */
    public function getDestinationTwo()
    {
        return $this->destinationTwo;
    }

    /**
     * @param \Redkiwi\TalismanPage\Domain\Model\Page $destinationTwo
     */
    public function setDestinationTwo($destinationTwo)
    {
        $this->destinationTwo = $destinationTwo;
    }

    /**
     * @return string
     */
    public function getDestinationTitles()
    {
        $titleList = [];
        if ($this->destinationOne !== null) {
            $titleList[] = $this->destinationOne->getTitle();
        }
        if ($this->destinationTwo !== null) {
            $titleList[] = $this->destinationTwo->getTitle();
        }
        return implode(', ', $titleList);
    }

    /**
     * @return \Redkiwi\TalismanPage\Domain\Model\Page
     */
    public function getExampleTrip()
    {
        return $this->exampleTrip;
    }

    /**
     * @param \Redkiwi\TalismanPage\Domain\Model\Page $exampleTrip
     */
    public function setExampleTrip(\Redkiwi\TalismanPage\Domain\Model\Page $exampleTrip)
    {
        $this->exampleTrip = $exampleTrip;
    }

    /**
     * @return string
     */
    public function getHeardOfTalismanAnswer(): string
    {
        return $this->heardOfTalismanAnswer;
    }

    /**
     * @param string $heardOfTalismanAnswer
     */
    public function setHeardOfTalismanAnswer(string $heardOfTalismanAnswer)
    {
        $this->heardOfTalismanAnswer = $heardOfTalismanAnswer;
    }

    /**
     * @return string
     */
    public function getHeardOfTalismanOtherAnswer(): string
    {
        return $this->heardOfTalismanOtherAnswer;
    }

    /**
     * @param string $heardOfTalismanOtherAnswer
     */
    public function setHeardOfTalismanOtherAnswer(string $heardOfTalismanOtherAnswer)
    {
        $this->heardOfTalismanOtherAnswer = $heardOfTalismanOtherAnswer;
    }

    /**
     * @return string
     */
    public function getHouseNumber(): string
    {
        return $this->houseNumber;
    }

    /**
     * @param string $houseNumber
     */
    public function setHouseNumber(string $houseNumber)
    {
        $this->houseNumber = $houseNumber;
    }

    /**
     * @return string
     */
    public function getHouseNumberAddendum(): string
    {
        return $this->houseNumberAddendum;
    }

    /**
     * @param string $houseNumberAddendum
     */
    public function setHouseNumberAddendum(string $houseNumberAddendum)
    {
        $this->houseNumberAddendum = $houseNumberAddendum;
    }

    /**
     * @return string
     */
    public function getPreferredReachDateAnswer(): string
    {
        return $this->preferredReachDateAnswer;
    }

    /**
     * @param string $preferredReachDateAnswer
     */
    public function setPreferredReachDateAnswer(string $preferredReachDateAnswer)
    {
        $this->preferredReachDateAnswer = $preferredReachDateAnswer;
    }

    /**
     * @return string
     */
    public function getPreferredReachTimeAnswer(): string
    {
        return $this->preferredReachTimeAnswer;
    }

    /**
     * @param string $preferredReachTimeAnswer
     */
    public function setPreferredReachTimeAnswer(string $preferredReachTimeAnswer)
    {
        $this->preferredReachTimeAnswer = $preferredReachTimeAnswer;
    }

    /**
     * @return string
     */
    public function getPreferredReachTimeOtherAnswer(): string
    {
        return $this->preferredReachTimeOtherAnswer;
    }

    /**
     * @param string $preferredReachTimeOtherAnswer
     */
    public function setPreferredReachTimeOtherAnswer(string $preferredReachTimeOtherAnswer)
    {
        $this->preferredReachTimeOtherAnswer = $preferredReachTimeOtherAnswer;
    }

    /**
     * @return string
     */
    public function getPhone(): string
    {
        return $this->phone;
    }

    /**
     * @param string $phone
     */
    public function setPhone(string $phone)
    {
        $this->phone = $phone;
    }

    /**
     * @return string
     */
    public function getStreet(): string
    {
        return $this->street;
    }

    /**
     * @param string $street
     */
    public function setStreet(string $street)
    {
        $this->street = $street;
    }

    /**
     * @return bool
     */
    public function isTravelledBefore(): bool
    {
        return $this->travelledBefore;
    }

    /**
     * @param bool $travelledBefore
     */
    public function setTravelledBefore(bool $travelledBefore)
    {
        $this->travelledBefore = $travelledBefore;
    }

    /**
     * @return bool
     */
    public function isUnsure(): bool
    {
        return $this->unsure;
    }

    /**
     * @param bool $unsure
     */
    public function setUnsure(bool $unsure)
    {
        $this->unsure = $unsure;
    }

    /**
     * @return string
     */
    public function getZip()
    {
        return strtoupper(str_replace(' ', '', $this->zip));
    }

    /**
     * @param string $zip
     */
    public function setZip(string $zip)
    {
        $this->zip = $zip;
    }
}