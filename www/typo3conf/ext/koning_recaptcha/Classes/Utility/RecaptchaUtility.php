<?php
namespace KoninklijkeCollective\KoningRecaptcha\Utility;

use TYPO3\CMS\Core\Utility\GeneralUtility;

/**
 * Utility: Recaptcha
 *
 * @package KoninklijkeCollective\KoningRecaptcha\Utility
 */
class RecaptchaUtility
{

    /**
     * Sends request to Google recaptcha service to validate Captcha response
     *
     * @param $recaptchaResponse
     * @return bool
     * @throws \TYPO3\CMS\Core\Error\Exception
     */
    public static function validateResponse($recaptchaResponse)
    {
        /** @var \TYPO3\CMS\Core\Http\HttpRequest|\HTTP_Request2 $request */
        $request = GeneralUtility::makeInstance(
            \TYPO3\CMS\Core\Http\HttpRequest::class,
            ConfigurationUtility::getSetting('verificationUrl')
        );

        $post = [
            'secret' => ConfigurationUtility::getSetting('secretKey'),
            'response' => $recaptchaResponse,
            'remoteip' => GeneralUtility::getIndpEnv('HTTP_CLIENT_IP')
        ];

        $request->addPostParameter($post);
        $request->setMethod('POST');

        try {
            $response = json_decode($request->send()->getBody());
            return ($response->success === true);
        } catch (\Exception $e) {
            return false;
        }
    }
}
