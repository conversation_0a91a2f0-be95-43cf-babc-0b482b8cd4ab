<f:layout name="Mail" />

<f:section name="Title">
    {subject}
</f:section>

<f:section name="Content">
    <table cellpadding="0" cellspacing="0" border="0" id="backgroundTable">
        <tr>
            <td style="vertical-align:top;" valign="top">
                <table cellpadding="0" cellspacing="0" border="0" align="center">
                    <tr>
                        <td width="600" colspan="2">
                            <img class="image_fix" src="{f:uri.resource(path: 'Images/logo.png', extensionName: 'talisman_template', absolute: 1)}" width="180" height="84" alt="{values.subject}" title="{values.subject}" />
                        </td>
                    </tr>
                    <tr>
                        <td style="vertical-align:top;" height="20">&nbsp;</td>
                    </tr>
                    <tr>
                        <td style="vertical-align:top;" width="600">
                            <span>
                                Onderstaand een overzicht van het nieuwe reisverslag:
                            </span>
                        </td>
                    </tr>
                    <tr>
                        <td style="vertical-align:top;" height="80">&nbsp;</td>
                    </tr>
                </table>
                <table cellpadding="0" cellspacing="0" border="0" align="center">
                    <tr>
                        <td style="vertical-align:top;" width="250">
                            <span><f:translate id="form.step_three.destinations" extensionName="TalismanTravelogues" /></span>
                        </td>
                        <td style="vertical-align:top;" width="350">
                            <span>
                                {fields.destinations}
                            </span>
                        </td>
                    </tr>
                    <tr>
                        <td style="vertical-align:top;" height="20">&nbsp;</td>
                    </tr>
                </table>
                <table cellpadding="0" cellspacing="0" border="0" align="center">
                    <tr>
                        <td style="vertical-align:top;" width="250">
                            <span><f:translate id="form.step_three.title" extensionName="TalismanTravelogues" /></span>
                        </td>
                        <td style="vertical-align:top;" width="350">
                            <span>
                                {fields.title}
                            </span>
                        </td>
                    </tr>
                    <tr>
                        <td style="vertical-align:top;" height="20">&nbsp;</td>
                    </tr>
                </table>
                <table cellpadding="0" cellspacing="0" border="0" align="center">
                    <tr>
                        <td style="vertical-align:top;" width="250">
                            <span><f:translate id="form.step_three.travelogue" extensionName="TalismanTravelogues" /></span>
                        </td>
                        <td style="vertical-align:top;" width="350">
                            <span>
                                <f:format.nl2br>{fields.travelogue}</f:format.nl2br>
                            </span>
                        </td>
                    </tr>
                    <tr>
                        <td style="vertical-align:top;" height="20">&nbsp;</td>
                    </tr>
                </table>
                <table cellpadding="0" cellspacing="0" border="0" align="center">
                    <tr>
                        <td style="vertical-align:top;" width="250">
                            <span><f:translate id="form.step_three.upload_images" extensionName="TalismanTravelogues" /></span>
                        </td>
                        <td style="vertical-align:top;" width="350">
                            <f:for each="{images}" as="image" iteration="i">
                                <a href="{image}">{image}</a>
                                <f:if condition="{i.isLast}">
                                    <f:else><br></f:else>
                                </f:if>
                            </f:for>
                        </td>
                    </tr>
                    <tr>
                        <td style="vertical-align:top;" height="20">&nbsp;</td>
                    </tr>
                </table>
                <table cellpadding="0" cellspacing="0" border="0" align="center">
                    <tr>
                        <td style="vertical-align:top;" height="70">&nbsp;</td>
                    </tr>
                    <tr>
                        <td style="vertical-align:top;" width="600">
                            <span>&copy; Talisman Travel Design</span>
                        </td>
                    </tr>
                    <tr>
                        <td style="vertical-align:top;" height="70">&nbsp;</td>
                    </tr>
                </table>
            </td>
        </tr>
    </table>
</f:section>
