<?php
namespace Red<PERSON>wi\TalismanInspiration\Controller;

use TYPO3\CMS\Core\Utility\GeneralUtility;

/**
 * Controller: Inspirational Page
 *
 * @package Redkiwi\TalismanInspiration\Controller
 */
class InspirationalPageController extends \TYPO3\CMS\Extbase\Mvc\Controller\ActionController
{

    /** @var \Redkiwi\TalismanTemplate\Domain\Repository\CategoryRepository */
    protected $categoryRepository;

    /**
     * Action: Default list action
     *
     * @return void
     * @throws \TYPO3\CMS\Extbase\Persistence\Exception\InvalidQueryException
     */
    public function overviewAction()
    {
        $filters = GeneralUtility::intExplode(',', $this->settings['data']['displayedFilters'], true);
        if (!empty($filters)) {
            // Retrieve all filters for configuration
            $filters = $this->getCategoryRepository()->findByIdentifiers($filters);
            $this->view->assign('filters', $filters);
        }

        $type = $this->settings['data']['type'] ?: 0;
        $this->view->assign('type', $type);
    }

    /**
     * @return \Redkiwi\TalismanTemplate\Domain\Repository\CategoryRepository
     */
    protected function getCategoryRepository()
    {
        if ($this->categoryRepository === null) {
            $this->categoryRepository = $this->objectManager->get(\Redkiwi\TalismanTemplate\Domain\Repository\CategoryRepository::class);
        }
        return $this->categoryRepository;
    }
}
