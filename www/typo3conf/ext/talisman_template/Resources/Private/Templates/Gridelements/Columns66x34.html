<div id="c{data.uid}" class="row talisman-row {f:if(condition: '{data.flexform_equalHeight}', then: 'row-equal')} {f:if(condition: '{data.tx_talismancontent_block_margin}', then: 'block-margin')}">
    <div class="{f:if(condition: '{data.flexform_mobileBehaviour} == \'aside\'', then: 'col-xs-6')} col-sm-7">
        {data.tx_gridelements_view_column_1 -> f:format.raw()}
    </div>
    <div class="{f:if(condition: '{data.flexform_mobileBehaviour} == \'aside\'', then: 'col-xs-6')} col-sm-5">
        {data.tx_gridelements_view_column_2 -> f:format.raw()}
    </div>
</div>
