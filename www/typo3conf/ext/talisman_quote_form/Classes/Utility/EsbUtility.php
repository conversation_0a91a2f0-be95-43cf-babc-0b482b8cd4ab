<?php

namespace Redkiwi\TalismanQuoteForm\Utility;

/**
 * Utility: ESB
 *
 * @package Redkiwi\TalismanQuoteForm\Utility
 */
class EsbUtility
{
    /** @var array */
    static $utmSourceMapping = [
        'Google Organic' => 'Google organic',
        'Display Campagnes' => 'Display Campagnes',
        'Remarketing' => 'Remarketing',
        'Google Adwords' => 'Google adwords',
        'Social Media' => 'Social Media',
        'Nieuwsbrief extern' => 'Nieuwsbrief extern',
        'Bing Adwords' => 'Bing adwords',
        'Bing Organic' => 'Bing organic',
        'Nieuwsbrief Talisman' => 'Talisman Nieuwsbrief',
        'Bestemmingsbrochure Talisman' => 'Talisman bestemmingsbrochure'
    ];

    /** @var array */
    static $heardOfTalismanAnswerMapping = [
        109 => 'Via familie/vrienden',
        147 => 'advertentie krant',
        148 => 'advertentie magazine',
        40 => 'Google organic',
        151 => 'Direct via Talisman website',
        0 => 'Direct via Talisman website'
    ];

    /**
     * @param string $gclid
     * @param string $utmSource
     * @param int $heardOfTalismanAnswer
     * @return string
     */
    public static function getSourceTypeName($gclid, $utmSource, $heardOfTalismanAnswer)
    {
        if ($gclid !== '') {
            return 'Google adwords';
        }
        if ($utmSource !== '' && isset(self::$utmSourceMapping[$utmSource]))
        {
            return self::$utmSourceMapping[$utmSource];
        }
        if (isset(self::$heardOfTalismanAnswerMapping[(int)$heardOfTalismanAnswer])) {
            return self::$heardOfTalismanAnswerMapping[(int)$heardOfTalismanAnswer];
        }
        return '';
    }
}
