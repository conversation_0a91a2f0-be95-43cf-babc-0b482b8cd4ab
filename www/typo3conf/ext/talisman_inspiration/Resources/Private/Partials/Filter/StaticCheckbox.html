<html xmlns:f="http://xsd.helmut-hummel.de/ns/TYPO3/CMS/Fluid/ViewHelpers"
      xmlns="http://www.w3.org/1999/xhtml"

      data-namespace-typo3-fluid="true">
<f:if condition="{category}">
    <div class="talisman-inspiration-filters filter-specific">
        <div class="filter-container panel panel-default" id="filter-{category.uid}">
            <div class="filter-header panel-heading panel-collapse-icons" data-toggle="collapse" data-target="#filter-{category.uid}-options">
                <div class="panel-title">
                    {category.title}
                    <div class="icons">
                        <span class="toggle-close sprite-icon-48 minus{f:if(condition: iteration.isFirst, else: ' hide')}"></span>
                        <span class="toggle-open sprite-icon-48 arrow-down black{f:if(condition: iteration.isFirst, then: ' hide')}"></span>
                    </div>
                </div>
            </div>
            <div class="filter-options panel-collapse panel-body collapse{f:if(condition: iteration.isFirst, then: ' in')}" id="filter-{category.uid}-options">
                <div class="options">
                    <f:for each="{category.children}" as="option">
                        <div id="category-{category.uid}-{option.uid}" class="checkbox type-select-checkbox filter-category-{option.uid}">
                            <f:form.checkbox id="filter-{category.uid}-{option.uid}" name="category[static][{category.uid}][{option.uid}]" value="1"
                                             class="input-checkbox" additionalAttributes="{data-title: '{option.title}'}"/>
                            <label for="filter-{category.uid}-{option.uid}">{option.title}</label>
                        </div>
                    </f:for>
                </div>

            </div>
        </div>
    </div>
</f:if>
</html>
