<f:layout name="Default" />

<f:section name="Content">
    <div class="nav-tabs-box with-border">
        <p><f:format.nl2br><f:translate id="brochure_form.text" /></f:format.nl2br></p>
        <f:form action="process" object="{brochure}" objectName="brochure" class="form form-step-one" additionalAttributes="{autocomplete: 'off'}">
            <div class="row">
                <div class="col-sm-6">

                    <h2><f:translate id="quote_form.destinations" /></h2>

                    <f:render
                        partial="Form/MultiSelect"
                        arguments="{
                            formObjectName: 'brochure',
                            options: destinations,
                            property: 'destinationOne',
                            value: brochure.destinationOne.uid,
                            displayLabel: '{f:translate(id: \'quote_form.choose_destination\')}'
                        }" />

                    <f:render
                            partial="Form/MultiSelect"
                            arguments="{
                            formObjectName: 'brochure',
                            options: destinations,
                            property: 'destinationTwo',
                            value: brochure.destinationTwo.uid,
                            displayLabel: '{f:translate(id: \'quote_form.choose_destination\')}'
                        }" />

                    <h2><f:translate id="quote_form.your_name" /></h2>

                    <div class="form-group">
                        <label><f:translate id="quote_form.title" /> <span class="required">*</span></label><br>
                        <fieldset class="radio-errors">
                            <div class="radio radio-inline">
                                <f:form.radio property="title" value="{f:translate(id: 'quote_form.title.1')}" id="title-1" additionalAttributes="{required: 'required'}" data="{parsley-trigger: 'change', parsley-required-message: '{f:translate(key: \'LLL:EXT:talisman_template/Resources/Private/Language/locallang.xlf:form.error.required_field\')}', parsley-errors-container: '.radio-errors', parsley-class-handler:'.radio-errors'}"  />
                                <label for="title-1"><f:translate id="quote_form.title.1" /></label>
                            </div>
                            <div class="radio radio-inline">
                                <f:form.radio property="title" value="{f:translate(id: 'quote_form.title.2')}" id="title-2" />
                                <label for="title-2"><f:translate id="quote_form.title.2" /></label>
                            </div>
                        </fieldset>
                        <f:render partial="Error" arguments="{formObjectName: 'brochure', property: 'title'}" />
                    </div>

                    <f:render
                            partial="Form/TextField"
                            arguments="{
                            property: 'initials',
                            formObjectName: 'brochure',
                            required: 1,
                            label: '{f:translate(id: \'quote_form.initials\')}'
                        }" />

                    <f:render
                            partial="Form/TextField"
                            arguments="{
                            property: 'middleName',
                            formObjectName: 'brochure',
                            label: '{f:translate(id: \'quote_form.middle_name\')}'
                        }" />

                    <f:render
                            partial="Form/TextField"
                            arguments="{
                            property: 'lastName',
                            formObjectName: 'brochure',
                            required: 1,
                            label: '{f:translate(id: \'quote_form.last_name\')}'
                        }" />

                    <f:render
                            partial="Form/TextField"
                            arguments="{
                            property: 'email',
                            formObjectName: 'brochure',
                            required: 1,
                            type: 'email',
                            label: '{f:translate(id: \'quote_form.email\')}'
                        }" />

                    <f:render
                            partial="Form/TextArea"
                            arguments="{
                            property: 'additionalInformation',
                            formObjectName: 'form',
                            rows: 10,
                            label: '{f:translate(id: \'quote_form.your_question\')}'
                        }" />

                    <div class="heard-of-talisman-answer">
                        <f:render
                                partial="Form/Select"
                                arguments="{
                                property: 'heardOfTalismanAnswer',
                                options: '{heardOfTalismanAnswers}',
                                prependOptionLabel: '{f:translate(key: \"LLL:EXT:talisman_template/Resources/Private/Language/locallang.xlf:form.choose_an_option\")}',
                                label: '{f:translate(id: \'quote_form.heard_of_talisman\')}',
                                additionalAttributes: '{data-parsley-trigger: \'change\', data-parsley-required: \'\', data-parsley-required-message: \'{f:translate(key: \"LLL:EXT:talisman_template/Resources/Private/Language/locallang.xlf:form.error.required_field\")}\', data-parsley-errors-container: \'.bootstrap-select\'}'
                        }" />

                        <div class="heard-of-talisman-other-answer" style="display: none;">
                            <f:render
                                    partial="Form/TextField"
                                    arguments="{
                                    property: 'heardOfTalismanOtherAnswer',
                                    formObjectName: 'quote'
                            }" />
                        </div>
                    </div>

                    <label><f:translate id="quote_form.newsletter" /></label>
                    <div class="form-group">
                        <div class="radio radio-inline">
                            <f:form.radio property="newsletter" value="0" id="newsletter-0" />
                            <label for="newsletter-0"><f:translate id="quote_form.newsletter.0" /></label>
                        </div>
                        <div class="radio radio-inline">
                            <f:form.radio property="newsletter" value="1" id="newsletter-1" />
                            <label for="newsletter-1"><f:translate id="quote_form.newsletter.1" /></label>
                        </div>
                        <div class="radio radio-inline">
                            <f:form.radio property="newsletter" value="2" id="newsletter-2" />
                            <label for="newsletter-2"><f:translate id="quote_form.newsletter.2" /></label>
                        </div>
                    </div>

                    <label><f:translate id="brochure_form.travelled_before" /></label>
                    <div class="form-group">
                        <div class="radio radio-inline">
                            <f:form.radio property="travelledBefore" value="1" id="travelledBefore-1" />
                            <label for="travelledBefore-1"><f:translate id="brochure_form.travelled_before.1" /></label>
                        </div>
                        <div class="radio radio-inline">
                            <f:form.radio property="travelledBefore" value="0" id="travelledBefore-0" />
                            <label for="travelledBefore-0"><f:translate id="brochure_form.travelled_before.0" /></label>
                        </div>
                    </div>

                    <div class="checkbox">
                        <f:form.checkbox property="agree" value="1" id="agree" additionalAttributes="{required: 'required'}" data="{parsley-trigger: 'change', parsley-required-message: '{f:translate(key: \'LLL:EXT:talisman_template/Resources/Private/Language/locallang.xlf:form.error.required_field\')}'}" />
                        <label for="agree"><f:translate id="quote_form.agree" /></label>
                    </div>

                    <button type="submit" class="btn btn-default btn-rubine">
                        <f:translate id="quote_form.submit" />
                    </button>

                    <span class="mandatory">* <f:translate id="form.label.mandatory" extensionName="TalismanTemplate" /></span>

                </div>
            </div>



        </f:form>
    </div>
</f:section>
