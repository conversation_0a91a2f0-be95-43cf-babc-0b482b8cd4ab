<?php
namespace Redkiwi\TalismanAjaxLogin\Controller;

use TYPO3\CMS\Core\Utility\GeneralUtility;
use TYPO3\CMS\Frontend\Controller\TypoScriptFrontendController;

/**
 * Controller: Ajax
 *
 * @package Redkiwi\TalismanAjaxLoginController
 */
class AjaxController extends \TYPO3\CMS\Extbase\Mvc\Controller\ActionController
{
    /**
     * @return string
     */
    public function loginAction()
    {
        $returnArray = [
            'redirectUrl' => '',
            'success' => false,
            'userInfo' => []
        ];

        if ($this->getTypoScriptFrontendController()->loginUser) {
            $user = $this->getTypoScriptFrontendController()->fe_user->user;
            $returnArray['redirectUrl'] = GeneralUtility::_POST('referrer');
            $returnArray['success'] = true;
            $returnArray['userInfo'] = [
                'username' => $user['username'],
                'firstName' => $user['first_name'],
                'middleName' => $user['middle_name'],
                'lastName' => $user['last_name']
            ];
        }
        return json_encode($returnArray);
    }

    /**
     * @return string
     */
    public function logoutAction()
    {
        $redirectUrl = $this->uriBuilder
            ->reset()
            ->build();

        return json_encode(['success' => true, 'redirectUrl' => $redirectUrl]);
    }

    /**
     * @return TypoScriptFrontendController
     */
    protected function getTypoScriptFrontendController()
    {
        return $GLOBALS['TSFE'];
    }
}
