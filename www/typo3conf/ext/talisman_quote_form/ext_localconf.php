<?php
if (!defined('TYPO3_MODE')) {
    die('Access denied.');
}

\TYPO3\CMS\Extbase\Utility\ExtensionUtility::configurePlugin(
    'Redkiwi.' . $_EXTKEY,
    'BrochureForm',
    ['BrochureForm' => 'show, process, finished'],
    ['BrochureForm' => 'show, process, finished'],
    \TYPO3\CMS\Extbase\Utility\ExtensionUtility::PLUGIN_TYPE_CONTENT_ELEMENT
);

\TYPO3\CMS\Extbase\Utility\ExtensionUtility::configurePlugin(
    'Redkiwi.' . $_EXTKEY,
    'QuoteForm',
    ['QuoteForm' => 'show, process, finished'],
    ['QuoteForm' => 'show, process, finished'],
    \TYPO3\CMS\Extbase\Utility\ExtensionUtility::PLUGIN_TYPE_CONTENT_ELEMENT
);


$GLOBALS['TYPO3_CONF_VARS']['SC_OPTIONS']['extbase']['commandControllers'][] = 'Redkiwi\TalismanQuoteForm\Command\EsbCommandController';
