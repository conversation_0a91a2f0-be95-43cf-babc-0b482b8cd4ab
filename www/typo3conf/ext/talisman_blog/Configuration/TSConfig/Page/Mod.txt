mod.wizards.newContentElement.wizardItems.plugins {
    elements {
        talismanblog_list {
            title = LLL:EXT:talisman_blog/Resources/Private/Language/locallang_be.xlf:plugin.list.title
            icon = EXT:talisman_blog/Resources/Public/Icons/blog-list.jpg
            description = LLL:EXT:talisman_blog/Resources/Private/Language/locallang_be.xlf:plugin.list.description
            tt_content_defValues {
                CType = list
                list_type = talismanblog_list
            }
        }

        talismanblog_popular {
            title = LLL:EXT:talisman_blog/Resources/Private/Language/locallang_be.xlf:plugin.popular.title
            icon = EXT:talisman_blog/Resources/Public/Icons/blog-popular.jpg
            description = LLL:EXT:talisman_blog/Resources/Private/Language/locallang_be.xlf:plugin.popular.description
            tt_content_defValues {
                CType = list
                list_type = talismanblog_popular
            }
        }

        talismanblog_intro {
            title = LLL:EXT:talisman_blog/Resources/Private/Language/locallang_be.xlf:plugin.intro.title
            icon = EXT:talisman_blog/Resources/Public/Icons/blog-intro.jpg
            description = LLL:EXT:talisman_blog/Resources/Private/Language/locallang_be.xlf:plugin.intro.description
            tt_content_defValues {
                CType = list
                list_type = talismanblog_intro
            }
        }

        talismanblog_comments {
            title = LLL:EXT:talisman_blog/Resources/Private/Language/locallang_be.xlf:plugin.comments.title
            icon = EXT:talisman_blog/Resources/Public/Icons/blog-comments.jpg
            description = LLL:EXT:talisman_blog/Resources/Private/Language/locallang_be.xlf:plugin.comments.description
            tt_content_defValues {
                CType = list
                list_type = talismanblog_comments
            }
        }

        talismanblog_related {
            title = LLL:EXT:talisman_blog/Resources/Private/Language/locallang_be.xlf:plugin.related.title
            icon = EXT:talisman_blog/Resources/Public/Icons/blog-related.jpg
            description = LLL:EXT:talisman_blog/Resources/Private/Language/locallang_be.xlf:plugin.related.description
            tt_content_defValues {
                CType = list
                list_type = talismanblog_related
            }
        }

        talismanblog_share {
            title = LLL:EXT:talisman_blog/Resources/Private/Language/locallang_be.xlf:plugin.share.title
            icon = EXT:talisman_blog/Resources/Public/Icons/blog-share.jpg
            description = LLL:EXT:talisman_blog/Resources/Private/Language/locallang_be.xlf:plugin.share.description
            tt_content_defValues {
                CType = list
                list_type = talismanblog_share
            }
        }
    }
}
