<?php
namespace Red<PERSON>wi\TalismanAppointment\Controller;


use TYPO3\CMS\Core\Utility\GeneralUtility;
use TYPO3\CMS\Extbase\Utility\LocalizationUtility;

/**
 * Controller: Form
 *
 * @package Redkiwi\TalismanAppointment\Controller
 */
class FormController extends \TYPO3\CMS\Extbase\Mvc\Controller\ActionController
{
    const ANSWER_DIRECT = 151;
    const ANSWER_FAMILY = 109;
    const ANSWER_MAGAZINE = 148;
    const ANSWER_NEWSPAPER = 147;
    const ANSWER_GOOGLE = 40;
    const ANSWER_OTHER = 0;

    const MONDAY = 1;
    const TUESDAY = 2;
    const WEDNESDAY = 3;
    const THURSDAY = 4;
    const FRIDAY = 5;

    const TIME_9_11 = 1;
    const TIME_11_13 = 2;
    const TIME_13_15 = 3;
    const TIME_15_17 = 4;
    const TIME_OTHER = 0;

    /**
     * @inject
     * @var \Redkiwi\TalismanQuoteForm\Service\DestinationService
     */
    protected $destinationService;

    /**
     * @param int $type
     * @return void
     */
    public function showAction($type = 0)
    {
        $destinations = $this->destinationService->getDestinations($this->settings['pages']['destinationRoot']);

        if ($this->request->getOriginalRequest() !== null) {
            switch ($this->request->getOriginalRequest()->getControllerActionName()) {
                case 'handleAppointment':
                    $type = 1;
                    break;
                case 'handlePhoneAppointment':
                    $type = 2;
            }
        }

        $timeOptions = [
            '09:00 - 10:00' => '09:00 - 10:00',
            '10:00 - 11:00' => '10:00 - 11:00',
            '11:00 - 12:00' => '11:00 - 12:00',
            '12:00 - 13:00' => '12:00 - 13:00',
            '13:00 - 14:00' => '13:00 - 14:00',
            '14:00 - 15:00' => '14:00 - 15:00',
            '15:00 - 16:00' => '15:00 - 16:00',
            '16:00 - 17:00' => '16:00 - 17:00',
        ];

        $this->view->assignMultiple([
            'destinations' => $destinations,
            'heardOfTalismanAnswers' => $this->getHeardOfTalismanAnswers(),
            'preferredReachDateAnswers' => $this->getPreferredReachDateAnswers(),
            'preferredReachTimeAnswers' => $this->getPreferredReachTimeAnswers(),
            'type' => $type,
            'timeOptions' => $timeOptions
        ]);
    }

    /**
     * @return void
     */
    public function initializeHandlePhoneAppointmentAction()
    {
        if (isset($this->arguments['phoneAppointment'])) {
            $this->arguments['phoneAppointment']
                ->getPropertyMappingConfiguration()
                ->forProperty('date')
                ->setTypeConverterOption('TYPO3\\CMS\\Extbase\\Property\\TypeConverter\\DateTimeConverter',
                    \TYPO3\CMS\Extbase\Property\TypeConverter\DateTimeConverter::CONFIGURATION_DATE_FORMAT, 'd-m-Y');
        }
    }

    /**
     * @param \Redkiwi\TalismanAppointment\Domain\DataTransfer\PhoneAppointment $phoneAppointment
     * @return void
     * @throws \TYPO3\CMS\Extbase\Mvc\Exception\StopActionException
     * @throws \TYPO3\CMS\Extbase\Mvc\Exception\UnsupportedRequestTypeException
     */
    public function handlePhoneAppointmentAction(\Redkiwi\TalismanAppointment\Domain\DataTransfer\PhoneAppointment $phoneAppointment)
    {
        $recipients = GeneralUtility::trimExplode(',', $this->settings['data']['phoneAppointmentForm']['recipientEmail']);
        $recipients = array_fill_keys($recipients, $this->settings['data']['phoneAppointmentForm']['recipientName']);

        /** @var \Keizer\KoningLibrary\Service\MailService $mailerService */
        $mailerService = GeneralUtility::makeInstance('Keizer\\KoningLibrary\\Service\\MailService');
        $mailerService
            ->setTemplateRootPaths([
                'EXT:talisman_appointment/Resources/Private/Templates/Email'
            ])
            ->setLayoutRootPaths([
                'EXT:talisman_template/Resources/Private/Layouts'
            ])
            ->sendMail(
                $recipients,
                [$this->settings['data']['phoneAppointmentForm']['senderEmail'] => $this->settings['data']['phoneAppointmentForm']['senderName']],
                $this->settings['data']['phoneAppointmentForm']['subject'],
                'PhoneAppointment',
                [],
                [
                    'fields' => $phoneAppointment,
                    'subject' => $this->settings['data']['phoneAppointmentForm']['subject']
                ]
            );

        $this->redirect('finishedPhoneAppointment');
    }

    /**
     * @param \Redkiwi\TalismanAppointment\Domain\DataTransfer\Appointment $appointment
     * @return void
     * @throws \TYPO3\CMS\Extbase\Mvc\Exception\StopActionException
     * @throws \TYPO3\CMS\Extbase\Mvc\Exception\UnsupportedRequestTypeException
     */
    public function handleAppointmentAction(\Redkiwi\TalismanAppointment\Domain\DataTransfer\Appointment $appointment)
    {
        $recipients = \TYPO3\CMS\Core\Utility\GeneralUtility::trimExplode(',', $this->settings['data']['appointmentForm']['recipientEmail']);
        $recipients = array_fill_keys($recipients, $this->settings['data']['phoneAppointmentForm']['recipientName']);

        /** @var \Keizer\KoningLibrary\Service\MailService $mailerService */
        $mailerService = \TYPO3\CMS\Core\Utility\GeneralUtility::makeInstance('Keizer\\KoningLibrary\\Service\\MailService');
        $mailerService
            ->setTemplateRootPaths([
                'EXT:talisman_appointment/Resources/Private/Templates/Email'
            ])
            ->setLayoutRootPaths([
                'EXT:talisman_template/Resources/Private/Layouts'
            ])
            ->sendMail(
                $recipients,
                [$this->settings['data']['appointmentForm']['senderEmail'] => $this->settings['data']['appointmentForm']['senderName']],
                $this->settings['data']['appointmentForm']['subject'],
                'Appointment',
                [],
                [
                    'fields' => $appointment,
                    'subject' => $this->settings['data']['appointmentForm']['subject']
                ]
            );

        $this->redirect('finishedAppointment');
    }

    /**
     * @return void
     */
    public function finishedPhoneAppointmentAction()
    {
    }

    /**
     * @return void
     */
    public function finishedAppointmentAction()
    {
    }

    /**
     * @return array
     */
    protected function getHeardOfTalismanAnswers(): array
    {
        $heardOfTalismanAnswers = [];
        $possibleAnswers = [
            self::ANSWER_FAMILY,
            self::ANSWER_NEWSPAPER,
            self::ANSWER_MAGAZINE,
            self::ANSWER_GOOGLE,
            self::ANSWER_DIRECT,
            self::ANSWER_OTHER
        ];
        foreach ($possibleAnswers as $answer) {
            $label = LocalizationUtility::translate('quote_form.heard_of_talisman.' . $answer, 'TalismanQuoteForm');
            $heardOfTalismanAnswers[$answer] = $label;
        }

        return $heardOfTalismanAnswers;
    }

    /**
     * @return array
     */
    protected function getPreferredReachDateAnswers(): array
    {
        $preferredReachDateAnswers = [];
        $possibleAnswers = [
            self::MONDAY,
            self::TUESDAY,
            self::WEDNESDAY,
            self::THURSDAY,
            self::FRIDAY,
        ];
        foreach ($possibleAnswers as $answer) {
            $label = LocalizationUtility::translate('form.date.' . $answer, 'TalismanAppointment');
            if($label) {
                $preferredReachDateAnswers[$answer] = $label;
            }
        }

        return $preferredReachDateAnswers;
    }

    /**
     * @return array
     */
    protected function getPreferredReachTimeAnswers(): array
    {
        $preferredReachTimeAnswers = [];
        $possibleAnswers = [
            self::TIME_9_11,
            self::TIME_11_13,
            self::TIME_13_15,
            self::TIME_15_17,
            self::TIME_OTHER,
        ];

        foreach ($possibleAnswers as $answer) {
            $label = LocalizationUtility::translate('form.time.' . $answer, 'TalismanAppointment');
            if($label) {
                $preferredReachTimeAnswers[$answer] = $label;
            }
        }

        return $preferredReachTimeAnswers;
    }
}

