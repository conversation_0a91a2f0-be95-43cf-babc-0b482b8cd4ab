mod.wizards.newContentElement.wizardItems.plugins {
    elements {
        talismanquoteform_quoteform {
            icon = EXT:talisman_quote_form/Resources/Public/Icons/quote_form.jpg
            title = LLL:EXT:talisman_quote_form/Resources/Private/Language/locallang_db.xlf:plugin.quote_form.title
            description = LLL:EXT:talisman_quote_form/Resources/Private/Language/locallang_db.xlf:plugin.quote_form.description
            tt_content_defValues {
                CType = talismanquoteform_quoteform
            }
        }
        talismanquoteform_brochureform {
            icon = EXT:talisman_quote_form/Resources/Public/Icons/brochure_form.jpg
            title = LLL:EXT:talisman_quote_form/Resources/Private/Language/locallang_db.xlf:plugin.brochure_form.title
            description = LLL:EXT:talisman_quote_form/Resources/Private/Language/locallang_db.xlf:plugin.brochure_form.description
            tt_content_defValues {
                CType = talismanquoteform_brochureform
            }
        }
    }
}
