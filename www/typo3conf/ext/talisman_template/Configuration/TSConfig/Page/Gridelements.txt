tx_gridelements {

    setup {
        # Container
        1 {
            title = Container
            icon = EXT:talisman_template/Resources/Public/Icons/gridelements-container.jpg
            description = Container to place content in

            config {
                colCount = 1
                rowCount = 1
                rows {
                    1 {
                        columns {
                            1 {
                                name = Content
                                colPos = 1
                            }
                        }
                    }
                }
            }

            flexformDS (
                <T3DataStructure>
                    <meta>
                        <langDisable>1</langDisable>
                    </meta>
                    <ROOT>
                        <type>array</type>
                        <el>
                            <backgroundColor>
                                <TCEforms>
                                    <label>LLL:EXT:talisman_template/Resources/Private/Language/locallang_db.xlf:gridelements.background_color</label>
                                    <config>
                                        <type>select</type>
                                        <items type="array">
                                            <numIndex index="0" type="array">
                                                <numIndex index="0">LLL:EXT:talisman_template/Resources/Private/Language/locallang_db.xlf:gridelements.background_color.white</numIndex>
                                                <numIndex index="1">white</numIndex>
                                            </numIndex>
                                            <numIndex index="1" type="array">
                                                <numIndex index="0">LLL:EXT:talisman_template/Resources/Private/Language/locallang_db.xlf:gridelements.background_color.black</numIndex>
                                                <numIndex index="1">black</numIndex>
                                            </numIndex>
                                            <numIndex index="2" type="array">
                                                <numIndex index="0">LLL:EXT:talisman_template/Resources/Private/Language/locallang_db.xlf:gridelements.background_color.pink</numIndex>
                                                <numIndex index="1">pink</numIndex>
                                            </numIndex>
                                        </items>
                                    </config>
                                </TCEforms>
                            </backgroundColor>
                            <style>
                                <TCEforms>
                                    <label>LLL:EXT:talisman_template/Resources/Private/Language/locallang_db.xlf:gridelements.style</label>
                                    <config>
                                        <type>select</type>
                                        <items type="array">
                                            <numIndex index="0" type="array">
                                                <numIndex index="0">LLL:EXT:talisman_template/Resources/Private/Language/locallang_db.xlf:gridelements.style.default</numIndex>
                                                <numIndex index="1">default</numIndex>
                                            </numIndex>
                                            <numIndex index="1" type="array">
                                                <numIndex index="0">LLL:EXT:talisman_template/Resources/Private/Language/locallang_db.xlf:gridelements.style.collection</numIndex>
                                                <numIndex index="1">collection</numIndex>
                                            </numIndex>
                                        </items>
                                    </config>
                                </TCEforms>
                            </style>
                            <spacing>
                                <TCEforms>
                                    <label>LLL:EXT:talisman_template/Resources/Private/Language/locallang_db.xlf:gridelements.spacing</label>
                                    <config>
                                        <type>select</type>
                                        <items type="array">
                                            <numIndex index="0" type="array">
                                                <numIndex index="0">LLL:EXT:talisman_template/Resources/Private/Language/locallang_db.xlf:gridelements.spacing.default</numIndex>
                                                <numIndex index="1">default</numIndex>
                                            </numIndex>
                                            <numIndex index="1" type="array">
                                                <numIndex index="0">LLL:EXT:talisman_template/Resources/Private/Language/locallang_db.xlf:gridelements.spacing.no_margin</numIndex>
                                                <numIndex index="1">no-margin</numIndex>
                                            </numIndex>
                                        </items>
                                    </config>
                                </TCEforms>
                            </spacing>
                        </el>
                    </ROOT>
                </T3DataStructure>
            )
        }

        # Columns 50x50
        2 {
            title = Columns 50x50
            icon = EXT:talisman_template/Resources/Public/Icons/gridelements-50x50.jpg
            description = Two columns of equal width

            config {
                colCount = 2
                rowCount = 1
                rows {
                    1 {
                        columns {
                            1 {
                                name = Left
                                colPos = 1
                            }
                            2 {
                                name = Right
                                colPos = 2
                            }
                        }
                    }
                }
            }

            flexformDS (
                <T3DataStructure>
                    <meta>
                        <langDisable>1</langDisable>
                    </meta>
                    <ROOT>
                        <type>array</type>
                        <el>
                            <mobileBehaviour>
                                <TCEforms>
                                    <label>LLL:EXT:talisman_template/Resources/Private/Language/locallang_db.xlf:gridelements.mobile_behaviour</label>
                                    <config>
                                        <type>select</type>
                                        <items type="array">
                                            <numIndex index="0" type="array">
                                                <numIndex index="0">LLL:EXT:talisman_template/Resources/Private/Language/locallang_db.xlf:gridelements.mobile_behaviour.below</numIndex>
                                                <numIndex index="1">below</numIndex>
                                            </numIndex>
                                            <numIndex index="1" type="array">
                                                <numIndex index="0">LLL:EXT:talisman_template/Resources/Private/Language/locallang_db.xlf:gridelements.mobile_behaviour.aside</numIndex>
                                                <numIndex index="1">aside</numIndex>
                                            </numIndex>
                                        </items>
                                    </config>
                                </TCEforms>
                            </mobileBehaviour>
                            <equalHeight>
                                <TCEforms>
                                    <label>LLL:EXT:talisman_template/Resources/Private/Language/locallang_db.xlf:gridelements.equal_height</label>
                                    <config>
                                        <type>check</type>
                                    </config>
                                </TCEforms>
                            </equalHeight>
                            <fullWidthButtons>
                                <TCEforms>
                                    <label>LLL:EXT:talisman_template/Resources/Private/Language/locallang_db.xlf:gridelements.full_width_buttons</label>
                                    <config>
                                        <type>check</type>
                                    </config>
                                </TCEforms>
                            </fullWidthButtons>
                        </el>
                    </ROOT>
                </T3DataStructure>
            )
        }

        # Columns 66x34
        4 {
            title = Columns 66x34
            description = Two columns with a bigger left column
            icon = EXT:talisman_template/Resources/Public/Icons/gridelements-66x34.jpg

            config {
                colCount = 2
                rowCount = 1
                rows {
                    1 {
                        columns {
                            1 {
                                name = Left
                                colPos = 1
                            }
                            2 {
                                name = Right
                                colPos = 2
                            }
                        }
                    }
                }
            }

            flexformDS (
                <T3DataStructure>
                    <meta>
                        <langDisable>1</langDisable>
                    </meta>
                    <ROOT>
                        <type>array</type>
                        <el>
                            <mobileBehaviour>
                                <TCEforms>
                                    <label>LLL:EXT:talisman_template/Resources/Private/Language/locallang_db.xlf:gridelements.mobile_behaviour</label>
                                    <config>
                                        <type>select</type>
                                        <items type="array">
                                            <numIndex index="0" type="array">
                                                <numIndex index="0">LLL:EXT:talisman_template/Resources/Private/Language/locallang_db.xlf:gridelements.mobile_behaviour.below</numIndex>
                                                <numIndex index="1">below</numIndex>
                                            </numIndex>
                                            <numIndex index="1" type="array">
                                                <numIndex index="0">LLL:EXT:talisman_template/Resources/Private/Language/locallang_db.xlf:gridelements.mobile_behaviour.aside</numIndex>
                                                <numIndex index="1">aside</numIndex>
                                            </numIndex>
                                        </items>
                                    </config>
                                </TCEforms>
                            </mobileBehaviour>
                            <equalHeight>
                                <TCEforms>
                                    <label>LLL:EXT:talisman_template/Resources/Private/Language/locallang_db.xlf:gridelements.equal_height</label>
                                    <config>
                                        <type>check</type>
                                    </config>
                                </TCEforms>
                            </equalHeight>
                        </el>
                    </ROOT>
                </T3DataStructure>
            )
        }

        # Columns 34x66
        5 {
            title = Columns 34x66
            description = Two columns with a bigger right column
            icon = EXT:talisman_template/Resources/Public/Icons/gridelements-34x66.jpg

            config {
                colCount = 2
                rowCount = 1
                rows {
                    1 {
                        columns {
                            1 {
                                name = Left
                                colPos = 1
                            }
                            2 {
                                name = Right
                                colPos = 2
                            }
                        }
                    }
                }
            }

            flexformDS (
                <T3DataStructure>
                    <meta>
                        <langDisable>1</langDisable>
                    </meta>
                    <ROOT>
                        <type>array</type>
                        <el>
                            <mobileBehaviour>
                                <TCEforms>
                                    <label>LLL:EXT:talisman_template/Resources/Private/Language/locallang_db.xlf:gridelements.mobile_behaviour</label>
                                    <config>
                                        <type>select</type>
                                        <items type="array">
                                            <numIndex index="0" type="array">
                                                <numIndex index="0">LLL:EXT:talisman_template/Resources/Private/Language/locallang_db.xlf:gridelements.mobile_behaviour.below</numIndex>
                                                <numIndex index="1">below</numIndex>
                                            </numIndex>
                                            <numIndex index="1" type="array">
                                                <numIndex index="0">LLL:EXT:talisman_template/Resources/Private/Language/locallang_db.xlf:gridelements.mobile_behaviour.aside</numIndex>
                                                <numIndex index="1">aside</numIndex>
                                            </numIndex>
                                        </items>
                                    </config>
                                </TCEforms>
                            </mobileBehaviour>
                            <equalHeight>
                                <TCEforms>
                                    <label>LLL:EXT:talisman_template/Resources/Private/Language/locallang_db.xlf:gridelements.equal_height</label>
                                    <config>
                                        <type>check</type>
                                    </config>
                                </TCEforms>
                            </equalHeight>
                        </el>
                    </ROOT>
                </T3DataStructure>
            )
        }

        # Accordion
        3 {
            title = Accordion
            description = Accordion content
            icon = EXT:talisman_template/Resources/Public/Icons/gridelements-accordion.jpg

            config {
                colCount = 1
                rowCount = 1
                rows {
                    1 {
                        columns {
                            1 {
                                name = Content
                                colPos = 1
                            }
                        }
                    }
                }
            }

            flexformDS (
                <T3DataStructure>
                    <meta>
                        <langDisable>1</langDisable>
                    </meta>
                    <ROOT>
                        <type>array</type>
                        <el>
                            <open>
                                <TCEforms>
                                    <label>LLL:EXT:talisman_template/Resources/Private/Language/locallang_db.xlf:gridelements.open</label>
                                    <config>
                                        <type>check</type>
                                    </config>
                                </TCEforms>
                            </open>
                        </el>
                    </ROOT>
                </T3DataStructure>
            )
        }

        # Hidden accordion content
        6 {
            title = Hidden accordion content
            description = Content that is hidden in a accordion container and can be opened with a link
            icon = EXT:talisman_template/Resources/Public/Icons/gridelements-accordion-hidden.jpg

            config {
                colCount = 1
                rowCount = 1
                rows {
                    1 {
                        columns {
                            1 {
                                name = Content
                                colPos = 1
                            }
                        }
                    }
                }
            }
        }

        # Columns 80x20
        7 {
            title = Columns 80x20
            description = Two columns with a bigger left column and two rows in the right column
            icon = EXT:talisman_template/Resources/Public/Icons/gridelements-80x20.jpg

            config {
                colCount = 2
                rowCount = 2
                rows {
                    1 {
                        columns {
                            1 {
                                name = Left
                                colPos = 1
                                rowspan = 2
                            }
                            2 {
                                name = Right bottom
                                colPos = 3
                            }
                        }
                    }
                    2 {
                        columns {
                            1 {
                                name = Right top
                                colPos = 2
                            }
                        }
                    }
                }
            }
        }

        # Columns 20x80
        8 {
            title = Columns 20x80
            description = Two columns with a bigger right column and two rows in the right column
            icon = EXT:talisman_template/Resources/Public/Icons/gridelements-20x80.jpg

            config {
                colCount = 2
                rowCount = 2
                rows {
                    1 {
                        columns {
                            1 {
                                name = Left top
                                colPos = 1
                            }
                            2 {
                                name = Right
                                rowspan = 2
                                colPos = 2
                            }
                        }
                    }
                    2 {
                        columns {
                            1 {
                                name = Left bottom
                                colPos = 3
                            }
                        }
                    }
                }
            }
        }

        # Columns 75x25
        9 {
            title = Columns 75x25
            description = Two columns with a bigger left column
            icon = EXT:talisman_template/Resources/Public/Icons/gridelements-75x25.jpg

            config {
                colCount = 2
                rowCount = 1
                rows {
                    1 {
                        columns {
                            1 {
                                name = Left
                                colPos = 1
                            }
                            2 {
                                name = Right
                                colPos = 2
                            }
                        }
                    }
                }
            }
        }

        # Columns 80
        10 {
            title = Columns 80
            description = One column
            icon = EXT:talisman_template/Resources/Public/Icons/gridelements-80.jpg

            config {
                colCount = 1
                rowCount = 1
                rows {
                    1 {
                        columns {
                            1 {
                                name = Left
                                colPos = 1
                            }
                        }
                    }
                }
            }
        }

        # Columns 80x20-2
        7 {
            title = Columns 80x20-2
            description = Two columns with a bigger left column and two rows in the right column
            icon = EXT:talisman_template/Resources/Public/Icons/gridelements-66x34.jpg

            config {
                colCount = 2
                rowCount = 2
                rows {
                    1 {
                        columns {
                            1 {
                                name = Left
                                colPos = 1
                                rowspan = 2
                            }
                            2 {
                                name = Right bottom
                                colPos = 3
                            }
                        }
                    }
                    2 {
                        columns {
                            1 {
                                name = Right top
                                colPos = 2
                            }
                        }
                    }
                }
            }
        }

        # Columns 20-2x80
        8 {
            title = Columns 20-2x80
            description = Two columns with a bigger right column and two rows in the right column
            icon = EXT:talisman_template/Resources/Public/Icons/gridelements-34x66.jpg

            config {
                colCount = 2
                rowCount = 2
                rows {
                    1 {
                        columns {
                            1 {
                                name = Left top
                                colPos = 1
                            }
                            2 {
                                name = Right
                                rowspan = 2
                                colPos = 2
                            }
                        }
                    }
                    2 {
                        columns {
                            1 {
                                name = Left bottom
                                colPos = 3
                            }
                        }
                    }
                }
            }
        }
    }
}
