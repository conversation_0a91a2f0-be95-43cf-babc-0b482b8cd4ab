/* page layout */
body {
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
}

.order-container {
    border: 1px solid gray;
    padding: 15px;
    margin-bottom: 18px;
}

h1 {
    text-align: center;
}

table {
    width: 100%;
    margin: 8px 0;
}

.items-table {
    width: 100%;
    margin: 12px 0;
}

th, td {
    border: 1px solid gray;
    padding: 5px;
}

.items-table th, .items-table td {
    border: 1px solid #999;
    padding: 7px;
}

th {
    background: #eee;
}

.items-table th {
    background: #f0f0f0;
}

.order-information {
    background: #f8f8f8;
    padding: 10px;
    margin: 12px 0;
}

button {
    background: #0066cc;
    color: white;
    border: none;
    padding: 8px 12px;
    border-radius: 4px;
    cursor: pointer;
}

/* shipping label styling */
.shipping-label-container {
    border: 2px solid black;
    padding: 12px;
    background: white;
    text-align: center;
}

.label-image {
    max-width: 100%;
    height: auto;
    border: 1px solid #ddd;
}

.label-error {
    padding: 15px;
    background: #f8f8f8;
    border: 1px solid #ccc;
    text-align: center;
}

.label-header {
    text-align: center;
    font-weight: bold;
    margin-bottom: 10px;
}

.label-info {
    margin-bottom: 6px;
}

.label-address {
    margin-bottom: 10px;
}

.barcode-area {
    text-align: center;
    margin-top: 8px;
    padding: 6px;
    background: #f5f5f5;
    border: 1px solid #ddd;
}

.barcode-display {
    font-weight: bold;
}

.tracking-code {
    font-size: 11px;
    margin-top: 4px;
}

/* PDF layout styling */
.document-header {
    margin-bottom: 20px;
}

.address-container {
    margin-bottom: 18px;
}

/* address blocks */
.addr-left {
    display: inline-block;
    width: 47%;
    vertical-align: top;
}

.addr-right {
    display: inline-block;
    width: 47%;
    vertical-align: top;
    margin-left: 3%;
}

.address-box {
    border: 1px solid #999;
    padding: 10px;
}

.shipping-label {
    margin-top: 20px;
}

/* Generate Label section */
.generate-label-container {
    border: 1px solid gray;
    padding: 15px;
    margin-bottom: 18px;
}

#status-message {
    margin-top: 20px;
    padding: 12px;
    border-radius: 3px;
    display: none;
}