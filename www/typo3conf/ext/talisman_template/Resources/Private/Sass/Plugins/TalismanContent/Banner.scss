//
// Banner styling
//
.banner {
    width: 100%;
    height: 520px;
    background-repeat: no-repeat;
    background-size: cover;
    margin-bottom: -50px;
    position: relative;
    z-index: $zindex-lighter;

    .headers {
        bottom: 86px;
        position: absolute;

        h1, h2 {
            @include custom-font-family('Nunito', 600);
            @include responsive-font-property(50);
            @include responsive-font-property(60, 'line-height');
            color: $white;
            margin: 0;
        }

        h1 {
            text-transform: uppercase;
        }

        span {
            @include custom-font-family('Nunito', 600);
            font-size: 16px;
            line-height: 22px;
            color: $white;
            margin-top: 7px;
            display: block;
        }

        .btn {
            margin-top: 30px;
        }
    }

    .gradient {
        background: linear-gradient(rgba(0, 0, 0, 0), rgba(0, 0, 0, 0.3));
        height: 520px;
        width: 100%;
    }
}

//
// Remove bottom margin on template without sub menu
//
body {

    &.no-sub-menu {

        .banner {
            margin-bottom: 0;
        }
    }
}

//
// Change text positioning on md
//
@media (min-width: $screen-md-min) and (max-width: $screen-md-max) {

    .banner {

        .headers {
            bottom: 156px;
        }
    }
}

//
// Make banner smaller on xs
//
@media (max-width: $screen-xs-max) {

    .banner {
        margin-bottom: 50px;
        height: 485px;

        .headers {
            bottom: 40px;
        }

        .gradient {
            height: 485px;
        }
    }
}
