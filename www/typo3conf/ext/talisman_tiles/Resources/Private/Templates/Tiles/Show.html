<html xmlns:f="http://typo3.org/ns/TYPO3/CMS/Fluid/ViewHelpers" data-namespace-typo3-fluid="true">

    <div class="row card-list">
         <f:for each="{tiles}" as="tile">
             <div class="{f:if(condition: '{tiles -> f:count()} == 2', then: 'col-sm-6', else: 'col-sm-4')}">
                <f:switch expression="{tile.type}">
                    <f:case value="1">
                        <f:render partial="WithPrice" arguments="{_all}" />
                    </f:case>
                    <f:case value="2">
                        <f:render partial="WithButton" arguments="{_all}" />
                    </f:case>
                    <f:case value="3">
                        <f:render partial="Video" arguments="{_all}" />
                    </f:case>
                    <f:defaultCase>
                        <f:render partial="Regular" arguments="{_all}" />
                    </f:defaultCase>
                </f:switch>
             </div>
        </f:for>
    </div>
    <div class="row">
        <div class="col-sm-12 btn-container">
            <f:if condition="{link}">
                <f:link.typolink parameter="{link}" class="btn btn-default btn-rubine">
                    {linkText}
                </f:link.typolink>
            </f:if>
        </div>
    </div>

</html>
