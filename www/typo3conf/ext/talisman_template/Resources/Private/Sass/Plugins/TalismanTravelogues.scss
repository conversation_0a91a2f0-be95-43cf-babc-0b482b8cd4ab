.talisman-travelogues {

    &.detail {

        span {

            &.author {
                @include custom-font-family('Nunito', 600);
                font-size: 17px;
                line-height: 21px;
                color: $travelogues-author;
                display: inline-block;
                margin-bottom: 29px;
            }
        }
    }

    &.form {
        margin-bottom: 120px;

        .nav-tabs-box {

            form {

                &.step-two {
                    margin-top: 60px;
                }
            }

            p {

                &.disclaimer {
                    @include custom-font-family('Nunito', 400);
                    margin-bottom: 20px;
                    margin-top: 10px;
                    font-size: 15px;
                }
            }

            label {

                &.agree {
                    margin-top: 40px;
                }
            }

            &.finished {

                p {
                    margin-bottom: 55px;
                }
            }
        }
    }

    &.list {

        &.block-margin {
            margin-bottom: 120px;

        }

        .bottom {

            span {
                font-weight: bold;
            }
        }
    }
}

//
// Mobile styling
//
@media (max-width: $screen-xs-max) {

    .talisman-travelogues {

        .step-one-buttons {

            .col-sm-6 {

                &:first-child {
                    margin-bottom: 10px;
                }
            }
        }
    }
}
