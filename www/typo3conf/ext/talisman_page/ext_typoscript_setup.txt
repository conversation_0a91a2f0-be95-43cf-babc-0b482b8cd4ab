config.tx_extbase {
    persistence {
        classes {
            Redkiwi\TalismanPage\Domain\Model\CategoryRating {
                mapping {
                    tableName = tx_talismanpage_domain_model_category_rating
                }
            }
            Redkiwi\TalismanPage\Domain\Model\Page {
                mapping {
                    tableName = pages
                    columns {
                        title.mapOnProperty = title
                        subtitle.mapOnProperty = subtitle
                        nav_hide.mapOnProperty = hideInList
                        media.mapOnProperty = images
                        author.mapOnProperty = author
                        categories.mapOnProperty = categories
                        doktype.mapOnProperty = type
                        lastUpdated.mapOnProperty = date
                        tx_talismanpage_include_in_inspiration.mapOnProperty = includeInInspiration
                        tx_talismanpage_include_in_private.mapOnProperty = includeInPrivate
                        tx_talismanpage_include_in_specials.mapOnProperty = includeInSpecials
                        tx_talismanpage_category_ratings.mapOnProperty = categoryRatings
                        tx_talismanpage_iso_code.mapOnProperty = isoCode
                        tx_talismanpage_hide_submenu.mapOnProperty = hideSubmenu
                        tx_talismanpage_example_trip_name.mapOnProperty = exampleTripName
                        tx_talismanesbconnector_latitude.mapOnProperty = latitude
                        tx_talismanesbconnector_longitude.mapOnProperty = longitude
                        tx_talismanesbconnector_component_id.mapOnProperty = componentId
                        tx_talismanblog_author.mapOnProperty = blogAuthor
                        tx_talismanblog_country.mapOnProperty = blogCountry
                        tx_talismanpage_hide_breadcrumbs.mapOnProperty = hideBreadcrumbs
                    }
                }
            }
        }
    }
}
