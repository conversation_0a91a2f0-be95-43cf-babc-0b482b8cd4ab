//
// Talisman header styling
//
.talisman-header {

    &.circled {
        background: $rubine;
        border-radius: 400px;
        text-align: center;
        width: 100%;
        height: 100%;
        display: flex;
        justify-content: center;
        align-items: center;

        h2 {
            @include custom-font-family('Nunito', 600);
            @include responsive-font-property(30);
            @include responsive-font-property(30, 'line-height');
            margin: 0;
            padding: 0;
            text-transform: uppercase;
            color: $white;
        }

        &:hover, &:focus, &:active {
            text-decoration: none;
        }
    }

    &.squared {
        background: $gold;
        display: inline-block;
        width: 100%;
        height: 100%;

        .inner {
            padding: 12px;

            h2 {
                @include custom-font-family('Nunito', 600);
                @include responsive-font-property(22);
                @include responsive-font-property(24, 'line-height');
                margin: 0 0 4px;
                text-transform: uppercase;
                display: inline-block;
                border-bottom: 2px solid $black;
            }

            h3 {
                @include custom-font-family('Nunito', 600);
                @include responsive-font-property(22);
                @include responsive-font-property(24, 'line-height');
                margin: 0;
                color: $white;
                text-transform: uppercase;
            }
        }

        &:hover, &:focus, &:active {
            text-decoration: none;
        }
    }
}

//
// Add hovers to lg
//
@media (min-width: $screen-lg-min) {

    .talisman-header {

        &.circled {

            &:hover, &:focus, &:active {
                background: $rubine-light;
            }
        }

        &.squared {

            &:hover, &:focus, &:active {
                background: $gold-light;
            }
        }
    }
}

//
// Override styling for sm and xs
//
@media (max-width: $screen-sm-max) {

    .talisman-header {

        &.squared {

            .inner {
                padding: 7px 12px;
            }
        }
    }
}
