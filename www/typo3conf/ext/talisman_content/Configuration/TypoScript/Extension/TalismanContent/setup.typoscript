lib.layoutElement {
    partialRootPaths.5 = {$plugin.tx_talismancontent.view.partialRootPath}

    layoutRootPaths.5 = {$plugin.tx_talismancontent.view.layoutRootPath}

    templateRootPaths.5 = {$plugin.tx_talismancontent.view.templateRootPath}
}

tt_content {
    talismancontent_highlightedwithmap < lib.contentElement
    talismancontent_highlightedwithmap {
        templateName = HighlightedWithMap
        dataProcessing {
            10 = Redkiwi\TalismanContent\DataProcessing\FilesWithHoverProcessor
            10 {
                references.fieldName = assets
            }
        }
    }
    talismancontent_slider < lib.contentElement
    talismancontent_slider {
        templateName = Slider
        dataProcessing < tt_content.menu_pages.dataProcessing
    }

    talismancontent_highlightedwithzoom < lib.contentElement
    talismancontent_highlightedwithzoom {
        templateName = HighlightedWithZoom
        dataProcessing {
            10 = TYPO3\CMS\Frontend\DataProcessing\FilesProcessor
            10 {
                references.fieldName = assets
            }
        }
    }

    talismancontent_quotewidget < lib.contentElement
    talismancontent_quotewidget {
        templateName = QuoteWidget
        dataProcessing {
            10 = TYPO3\CMS\Frontend\DataProcessing\FilesProcessor
            10 {
                references.fieldName = assets
            }

            20 = Redkiwi\TalismanContent\DataProcessing\QuoteWidgetProcessor
            20 {
                settings {
                    pages {
                        quote = {$pages.quote}
                        brochure = {$pages.brochure}
                    }
                }
            }
        }
    }

    talismancontent_circledheader < lib.contentElement
    talismancontent_circledheader {
        templateName = CircledHeader
    }

    talismancontent_squaredheader < lib.contentElement
    talismancontent_squaredheader {
        templateName = SquaredHeader
    }

    talismancontent_banner < lib.contentElement
    talismancontent_banner {
        templateName = Banner
        dataProcessing {
            10 = TYPO3\CMS\Frontend\DataProcessing\FilesProcessor
            10 {
                references.fieldName = assets
            }
        }
    }

    talismancontent_quote < lib.contentElement
    talismancontent_quote {
        templateName = Quote
    }

    talismancontent_button < lib.contentElement
    talismancontent_button {
        templateName = Button
    }

    talismancontent_circlemenu < lib.contentElement
    talismancontent_circlemenu {
        templateName = CircleMenu
        dataProcessing {
            10 = TYPO3\CMS\Frontend\DataProcessing\FilesProcessor
            10 {
                references.fieldName = assets
            }
        }
    }

    talismancontent_linkwidget < lib.contentElement
    talismancontent_linkwidget {
        templateName = LinkWidget
        dataProcessing {
            10 = TYPO3\CMS\Frontend\DataProcessing\FilesProcessor
            10 {
                references.fieldName = assets
            }
        }
    }

    talismancontent_videowidget < lib.contentElement
    talismancontent_videowidget {
        templateName = VideoWidget
        dataProcessing {
            10 = TYPO3\CMS\Frontend\DataProcessing\FilesProcessor
            10 {
                references.fieldName = assets
            }
        }
    }

    talismancontent_audiowidget < lib.contentElement
    talismancontent_audiowidget {
        templateName = AudioWidget
        dataProcessing {
            10 = TYPO3\CMS\Frontend\DataProcessing\FilesProcessor
            10 {
                references.fieldName = assets
            }
        }
    }

    talismancontent_streetviewwidget < lib.contentElement
    talismancontent_streetviewwidget {
        templateName = StreetviewWidget
        dataProcessing {
            10 = TYPO3\CMS\Frontend\DataProcessing\FilesProcessor
            10 {
                references.fieldName = assets
            }
        }
    }

    talismancontent_carousel < lib.contentElement
    talismancontent_carousel {
        templateName = Carousel
        dataProcessing {
            10 = TYPO3\CMS\Frontend\DataProcessing\FilesProcessor
            10 {
                references.fieldName = assets
            }
        }
    }

    talismancontent_contactwidget < lib.contentElement
    talismancontent_contactwidget {
        templateName = ContactWidget
        dataProcessing {
            20 = Redkiwi\TalismanContent\DataProcessing\ContactWidgetProcessor
            20 {
                settings {
                    pages {
                        askAQuestion = {$pages.askAQuestion}
                        phoneAppointment = {$pages.phoneAppointment}
                        visit = {$pages.visit}
                    }
                }
            }
        }
    }

    talismancontent_videolightbox < lib.contentElement
    talismancontent_videolightbox {
        templateName = VideoLightbox
        dataProcessing {
            10 = TYPO3\CMS\Frontend\DataProcessing\FilesProcessor
            10 {
                references.fieldName = assets
            }
        }
    }
}