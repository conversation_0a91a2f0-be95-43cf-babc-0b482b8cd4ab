<?php
namespace Redkiwi\TalismanEsbConnector\Domain\Model;

/**
 * Model: Country
 *
 * @package Redkiwi\TalismanPage\Domain\Model
 */
class Country extends \TYPO3\CMS\Extbase\DomainObject\AbstractEntity
{
    /** @var string */
    protected $name;

    /** @var string */
    protected $isoCode;

    /** @var int */
    protected $remoteIdentifier;

    /**
     * @return string
     */
    public function getName()
    {
        return $this->name;
    }

    /**
     * @param string $name
     * @return void
     */
    public function setName($name)
    {
        $this->name = $name;
    }

    /**
     * @return string
     */
    public function getIsoCode()
    {
        return $this->isoCode;
    }

    /**
     * @param string $isoCode
     * @return void
     */
    public function setIsoCode($isoCode)
    {
        $this->isoCode = $isoCode;
    }

    /**
     * @return int
     */
    public function getRemoteIdentifier()
    {
        return $this->remoteIdentifier;
    }

    /**
     * @param int $remoteIdentifier
     * @return void
     */
    public function setRemoteIdentifier($remoteIdentifier)
    {
        $this->remoteIdentifier = $remoteIdentifier;
    }
}
