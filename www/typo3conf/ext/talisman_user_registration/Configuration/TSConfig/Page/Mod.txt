mod.wizards.newContentElement.wizardItems.plugins {
    elements {
        talismanuserregistration_signup {
            icon = EXT:talisman_user_registration/Resources/Public/Icons/sign-up.jpg
            title = LLL:EXT:talisman_user_registration/Resources/Private/Language/locallang_be.xlf:plugin.sign_up.title
            description = LLL:EXT:talisman_user_registration/Resources/Private/Language/locallang_be.xlf:plugin.sign_up.description
            tt_content_defValues {
                CType = talismanuserregistration_signup
            }
        }

        talismanuserregistration_forgotpassword {
            icon = EXT:talisman_user_registration/Resources/Public/Icons/forgot-password.jpg
            title = LLL:EXT:talisman_user_registration/Resources/Private/Language/locallang_be.xlf:plugin.forgot_password.title
            description = LLL:EXT:talisman_user_registration/Resources/Private/Language/locallang_be.xlf:plugin.forgot_password.description
            tt_content_defValues {
                CType = talismanuserregistration_forgotpassword
            }
        }
    }
}
