<?php
if (!defined('TYPO3_MODE')) {
    die ('Access denied.');
}

\TYPO3\CMS\Extbase\Utility\ExtensionUtility::configurePlugin(
    'Redkiwi.' . $_EXTKEY,
    'List',
    ['Travelogue' => 'list'],
    [],
    \TYPO3\CMS\Extbase\Utility\ExtensionUtility::PLUGIN_TYPE_CONTENT_ELEMENT
);

\TYPO3\CMS\Extbase\Utility\ExtensionUtility::configurePlugin(
    'Redkiwi.' . $_EXTKEY,
    'Detail',
    ['Travelogue' => 'detail'],
    [],
    \TYPO3\CMS\Extbase\Utility\ExtensionUtility::PLUGIN_TYPE_CONTENT_ELEMENT
);

\TYPO3\CMS\Extbase\Utility\ExtensionUtility::configurePlugin(
    'Redkiwi.' . $_EXTKEY,
    'Form',
    ['Form' => 'stepOne, handleStepOne, stepTwo, handleStepTwo, documentForm, handleDocumentForm, manualForm, handleManualForm, finished'],
    ['Form' => 'stepOne, handleStepOne, stepTwo, handleStepTwo, documentForm, handleDocumentForm, manualForm, handleManualForm, finished'],
    \TYPO3\CMS\Extbase\Utility\ExtensionUtility::PLUGIN_TYPE_CONTENT_ELEMENT
);