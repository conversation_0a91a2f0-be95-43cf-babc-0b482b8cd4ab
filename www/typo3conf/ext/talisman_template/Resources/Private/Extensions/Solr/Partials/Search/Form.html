<html xmlns="http://www.w3.org/1999/xhtml" lang="en"
	  xmlns:f="http://typo3.org/ns/TYPO3/Fluid/ViewHelpers"
	  xmlns:s="http://typo3.org/ns/ApacheSolrForTypo3/Solr/ViewHelpers">

<f:section name="Form">

	<div class="tx-solr-search-form">

		<s:searchForm id="tx-solr-search-form-pi-results" additionalFilters="{additionalFilters}" suggestHeader="{s:translate(key:'suggest_header',default:'Top Results')}">
			<div class="row">
				<div class="col-sm-9">
					<div class="search-wrapper">
						<input type="text" class="tx-solr-q js-solr-q tx-solr-suggest tx-solr-suggest-focus form-control" name="{pluginNamespace}[q]" value="{q}" />
					</div>

					<f:if condition="{addPageAndLanguageId}">
						<input type="hidden" name="L" value="{languageUid}" />
						<input type="hidden" name="id" value="{pageUid}" />
					</f:if>
				</div>

				<div class="col-sm-3">
					<button type="submit" class="btn btn-default btn-rubine tx-solr-submit">
						<s:translate key="submit">Search</s:translate>
					</button>
				</div>
			</div>
		</s:searchForm>
	</div>

</f:section>