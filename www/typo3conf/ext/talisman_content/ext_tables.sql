#
# Table structure for table 'tt_content'
#
CREATE TABLE tt_content (
    tx_talismancontent_image_position varchar(255) DEFAULT '',
    tx_talismancontent_block_margin tinyint(4) DEFAULT '1' NOT NULL,
    tx_talismancontent_author varchar(255) DEFAULT '',
    tx_talismancontent_include_in_example_trip tinyint(4) DEFAULT '1' NOT NULL,
    tx_talismanhighlightedspecials_pages int(11) DEFAULT '0' NOT NULL
);

#
# Table structure for table 'sys_file_reference'
#
CREATE TABLE sys_file_reference (
    talisman_content_promotional_package int(11) DEFAULT 0 NOT NULL
);

#
# Table structure for table 'tx_talismanhighlightedspecials_pages_mm'
#
CREATE TABLE tx_talismanhighlightedspecials_pages_mm (
    uid_local int(11) unsigned DEFAULT '0' NOT NULL,
    uid_foreign int(11) unsigned DEFAULT '0' NOT NULL,
    sorting int(11) unsigned DEFAULT '0' NOT NULL,
    KEY uid_local (uid_local),
    KEY uid_foreign (uid_foreign)
);
