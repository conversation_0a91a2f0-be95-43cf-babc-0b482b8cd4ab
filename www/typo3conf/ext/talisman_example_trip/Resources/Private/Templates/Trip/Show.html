<f:layout name="Default" />

<f:section name="Content">
   <div class="carousel-inner" role="listbox">

      <f:for each="{slides}" as="slide">
         <f:switch expression="{slide.type}">
            <f:case value="0">
               <f:render partial="Regular" arguments="{_all}" />
            </f:case>
            <f:case value="1">
               <f:render partial="First" arguments="{_all}" />
            </f:case>
            <f:case value="2">
               <f:render partial="Last" arguments="{_all}" />
            </f:case>
         </f:switch>
      </f:for>

   </div>
</f:section>
