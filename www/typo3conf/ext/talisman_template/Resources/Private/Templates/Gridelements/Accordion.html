<div class="talisman-accordion {f:if(condition: '{data.tx_talismancontent_block_margin}', then: 'block-margin')}">
    <div class="panel panel-default">
        <div id="talisman-accordion-header-{data.uid}" role="tab" class="panel-heading panel-collapse-icons {f:if(condition: '{data.flexform_open}', else: 'collapsed')}"
            data-toggle="collapse" data-target="#talisman-accordion-content-{data.uid}">

            <div class="panel-title">
                {data.header}

                <div class="icons">
                    <f:if condition="{data.flexform_open}">
                        <f:then>
                            <span class="toggle-close sprite-icon-48 minus"></span>
                            <span class="toggle-open sprite-icon-48 arrow-down black hide"></span>
                        </f:then>
                        <f:else>
                            <span class="toggle-close sprite-icon-48 minus hide"></span>
                            <span class="toggle-open sprite-icon-48 arrow-down black"></span>
                        </f:else>
                    </f:if>
                </div>
            </div>
        </div>
        <div id="talisman-accordion-content-{data.uid}"
            class="panel-collapse panel-body collapse {f:if(condition: '{data.flexform_open}', then: 'in')}">
            {data.tx_gridelements_view_column_1 -> f:format.raw()}
        </div>
    </div>
</div>
