CREATE TABLE fe_users (
    talismanuserregistration_signup_hash varchar(32) DEFAULT '' NOT NULL,
    talismanuserregistration_confirmed int(1) DEFAULT '0' NOT NULL,
    talismanuserregistration_facebook_id varchar(255) DEFAULT '' NOT NULL,
    talismanuserregistration_google_id varchar(255) DEFAULT '' NOT NULL,
    talismanuserregistration_salutation varchar(255) DEFAULT '' NOT NULL
);

CREATE TABLE tx_talismanuserregistration_domain_model_forgotpasswordrequest (
    uid int(11) NOT NULL auto_increment,
    pid int(11) DEFAULT '0' NOT NULL,
    tstamp int(11) DEFAULT '0' NOT NULL,
    crdate int(11) DEFAULT '0' NOT NULL,
    cruser_id int(11) DEFAULT '0' NOT NULL,

    editlock tinyint(4) DEFAULT '0' NOT NULL,
    deleted tinyint(4) DEFAULT '0' NOT NULL,

    user int(11) DEFAULT '0' NOT NULL,
    request_date int(11) DEFAULT '0' NOT NULL,
    hash varchar(32) DEFAULT '' NOT NULL,

    PRIMARY KEY (uid),
    KEY parent (pid)
);
