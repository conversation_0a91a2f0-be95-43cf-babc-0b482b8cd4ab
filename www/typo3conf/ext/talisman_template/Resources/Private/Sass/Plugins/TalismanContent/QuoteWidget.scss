//
// Quote widget styling
//
.quote-widget {

    h3 {
        @include custom-font-family('Nunito', 600);
        font-size: 33px;
        margin-top: 88px;
        margin-bottom: 25px;
    }

    a.btn {
        padding: 0 112px 0 20px;
    }
}

//
// Add hovers on lg
//
@media (min-width: $screen-lg-min) {

    .quote-widget {

        a.btn {

            &:hover, &:focus, &:active {
                padding: 0 66px;
            }
        }
    }
}

//
// Change button padding and font size on md
//
@media (min-width: $screen-md-min) and (max-width: $screen-md-max) {

    .quote-widget {

        a.btn {
            padding: 0 54px 0 20px;
        }
    }
}

//
// Style adjustments for xs / sm
//
@media (max-width: $screen-sm-max) {

    .quote-widget {

        h3 {
            font-size: 28px;
        }

        .col-md-6 {

            &:first-child {
                margin-bottom: 15px;
            }

            a {
                min-width: 311px;
            }
        }
    }
}
