<f:layout name="Widget" />

<f:section name="Content">
    <div class="row">
        <div class="col-md-offset-2 col-md-8" id="talisman-newsletter">
            <f:if condition="{hasTitlePrefixEmphasizedOrTitle}">
                <h2>{f:if(condition: '{titlePrefixEmphasized}', then: '<em>{titlePrefixEmphasized}</em>')}{f:if(condition: '{hasTitlePrefixEmphasizedAndTitle}', then: ' ')}{f:if(condition: '{title}', then: '{title}')}</h2>
            </f:if>
            <f:if condition="{text}">
                <p>{text}</p>
            </f:if>
            <f:form action="handle" objectName="widget" class="form" section="talisman-newsletter">
                <div class="row">
                    <div class="col-sm-8 col-md-7 col-lg-8">
                        <f:render
                            partial="Form/TextField"
                            arguments="{
                                property: 'email',
                                formObjectName: 'widget',
                                required: 1,
                                label: '{email}'
                            }" />
                    </div>
                    <div class="col-sm-4 col-md-5 col-lg-4 text-right">
                        <button type="submit" class="btn btn-default btn-rubine full-width">{submit}</button>
                    </div>
                </div>
            </f:form>
        </div>
    </div>
</f:section>
