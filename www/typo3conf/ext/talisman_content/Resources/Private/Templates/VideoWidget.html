<html xmlns:f="http://typo3.org/ns/TYPO3/CMS/Fluid/ViewHelpers"
    xmlns:likes="http://typo3.org/ns/Redkiwi/TalismanLikes/ViewHelpers"
    data-namespace-typo3-fluid="true">
<div id="c{data.uid}" class="talisman-content-widget video {f:if(condition: '{data.tx_talismancontent_block_margin} != 0', then: 'block-margin-{data.tx_talismancontent_block_margin}')}">
    <a href="#" data-toggle="modal" data-target="#talisman-content-video-widget-{data.uid}">
        <div class="row">
            <div class="col-sm-push-4 col-sm-8 image">
                <f:if condition="{files.0}">
                    <likes:likeableImage reference="{files.0}">
                        <figure>
                            <f:image
                                image="{files.0}"
                                alt="{files.0.properties.alt}"
                                title="{files.0.properties.title}"
                                height="263c"
                                width="490c"
                                class="img-responsive"/>
                            <div class="gradient"></div>
                            <div class="image-actions">

                                {like -> f:format.raw()}

                            </div>
                            <div class="icon">
                                <span class="sprite-icon-148 play"></span>
                            </div>
                        </figure>
                    </likes:likeableImage>
                </f:if>
            </div>
            <div class="col-sm-pull-8 col-sm-4 abstract">
                <div class="inner">
                    <h2>{data.header}</h2>
                    <h3>{data.subheader -> f:format.nl2br()}</h3>
                </div>
            </div>
            <f:if condition="{data.date}">
                <div class="bottom">
                    <time>
                        <f:format.date format="%e %B %Y">{data.date}</f:format.date>
                    </time>
                </div>
            </f:if>
        </div>
    </a>
    <div class="modal fade" id="talisman-content-video-widget-{data.uid}" role="dialog">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                </div>
                <div class="modal-body">
                    <iframe data-iframe="youtube" src="{data.header_link}" frameborder="0" webkitallowfullscreen mozallowfullscreen allowfullscreen></iframe>
                </div>
            </div>
        </div>
    </div>
</div>
</html>
