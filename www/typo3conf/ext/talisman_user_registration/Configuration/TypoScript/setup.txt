plugin.tx_talismanuserregistration {
    view {
        templateRootPaths {
            10 = {$plugin.tx_talismanuserregistration.view.templateRootPath}
        }
        partialRootPaths {
            10 = {$plugin.tx_talismanuserregistration.view.partialRootPath}
            15 = EXT:talisman_template/Resources/Private/Partials
        }
        layoutRootPaths {
            10 = {$plugin.tx_talismanuserregistration.view.layoutRootPath}
        }
    }
    persistence {
        storagePid = {$plugin.tx_talismanuserregistration.persistence.storagePid}
    }
    settings {
        pages {
            login = {$pages.login}
        }
        signUp {
            userGroup = {$plugin.tx_talismanuserregistration.settings.signUp.userGroup}
        }
        forgotPassword {
            resetLinkLifeTimeInSeconds = {$plugin.tx_talismanuserregistration.settings.forgotPassword.resetLinkLifeTimeInSeconds}
        }
        notifications {
            templateRootPath = {$plugin.tx_talismanuserregistration.settings.notifications.templateRootPath}
            partialRootPath = {$plugin.tx_talismanuserregistration.settings.notifications.partialRootPath}
            layoutRootPath = {$plugin.tx_talismanuserregistration.settings.notifications.layoutRootPath}

            signUp {
                subject = {$plugin.tx_talismanuserregistration.settings.notifications.signUp.subject}
                from {
                    name = {$plugin.tx_talismanuserregistration.settings.notifications.signUp.from.name}
                    email = {$plugin.tx_talismanuserregistration.settings.notifications.signUp.from.email}
                }
            }

            forgotPassword {
                subject = {$plugin.tx_talismanuserregistration.settings.notifications.forgotPassword.subject}
                from {
                    name = {$plugin.tx_talismanuserregistration.settings.notifications.forgotPassword.from.name}
                    email = {$plugin.tx_talismanuserregistration.settings.notifications.forgotPassword.from.email}
                }
            }
        }
        facebook {
            appId = {$plugin.tx_talismanuserregistration.settings.facebook.appId}
            appSecret = {$plugin.tx_talismanuserregistration.settings.facebook.appSecret}
        }
        google {
            clientId = {$plugin.tx_talismanuserregistration.settings.google.clientId}
            clientSecret = {$plugin.tx_talismanuserregistration.settings.google.clientSecret}
        }
    }
}
