<?php

namespace Redkiwi\TalismanEsbConnector\Service\Esb;

/**
 * Service: Check client existence
 *
 * @package Redkiwi\TalismanEsbConnector\Service\Esb
 */
class CheckClientExistenceService extends AbstractConnectorService
{
    /**
     * @param array $parameters
     * @return array
     * @throws \Exception
     */
    public function makeRequest(array $parameters = [])
    {
        $checkClientExistenceRequest = $this->request('data/checkclientexistence', $parameters);
        if ($checkClientExistenceRequest['info']['http_code'] !== 200) {
            throw new \Exception('Request data/checkclientexistence failed with status code ' . $checkClientExistenceRequest['info']['http_code']);
        }

        $response = new \SimpleXMLElement($checkClientExistenceRequest['response']);
        return [
            'statusCode' => (int)$response->statusCode,
            'clientIds' => $response->additionalData->clientIds
        ];
    }
}
