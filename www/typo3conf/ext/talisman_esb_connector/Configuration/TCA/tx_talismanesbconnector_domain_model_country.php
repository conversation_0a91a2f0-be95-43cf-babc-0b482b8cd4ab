<?php
if (!defined('TYPO3_MODE')) {
    die ('Access denied.');
}

return [
    'ctrl' => [
        'title' => 'LLL:EXT:talisman_esb_connector/Resources/Private/Language/locallang_be.xlf:tx_talismanesbconnector_domain_model_country',
        'label' => 'name',
        'hideAtCopy' => true,
        'tstamp' => 'tstamp',
        'crdate' => 'crdate',
        'cruser_id' => 'cruser_id',
        'editlock' => 'editlock',
        'dividers2tabs' => true,
        'sortby' => 'sorting',
        'delete' => 'deleted',
        'searchFields' => 'uid, name',
        'rootLevel' => true,
        'enablecolumns' => [
            'disabled' => 'hidden',
        ],
        'iconfile' => 'EXT:talisman_esb_connector/Resources/Public/Icons/tx_talismanesbconnector_domain_model_country.png',
    ],
    'interface' => [
        'showRecordFieldList' => 'hidden, name, iso_code, remote_identifier'
    ],
    'types' => [
        0 => [
            'showitem' => 'hidden, name, iso_code, remote_identifier'
        ]
    ],
    'palettes' => [],
    'columns' => [
        'hidden' => [
            'exclude' => true,
            'label' => 'LLL:EXT:core/Resources/Private/Language/locallang_general.xml:LGL.hidden',
            'config' => [
                'type' => 'check',
            ]
        ],
        'name' => [
            'exclude' => false,
            'label' => 'LLL:EXT:talisman_esb_connector/Resources/Private/Language/locallang_be.xlf:tx_talismanesbconnector_domain_model_country.name',
            'config' => [
                'type' => 'input',
                'eval' => 'trim, required',
            ]
        ],
        'iso_code' => [
            'exclude' => false,
            'label' => 'LLL:EXT:talisman_esb_connector/Resources/Private/Language/locallang_be.xlf:tx_talismanesbconnector_domain_model_country.iso_code',
            'config' => [
                'type' => 'input',
                'eval' => 'trim, required',
            ]
        ],
        'remote_identifier' => [
            'exclude' => false,
            'label' => 'LLL:EXT:talisman_esb_connector/Resources/Private/Language/locallang_be.xlf:tx_talismanesbconnector_domain_model_country.remote_identifier',
            'config' => [
                'type' => 'input',
                'eval' => 'trim, required',
            ]
        ],
    ],
];
