<div class="filter-specific">
    <div class="filter-container panel panel-default">
        <div class="filter-header panel-heading panel-collapse-icons" data-toggle="collapse" data-target="#filter-type-options">
            <div class="panel-title">
                <f:translate id="likes.type" />
                <div class="icons">
                    <span class="toggle-close sprite-icon-48 minus hide"></span>
                    <span class="toggle-open sprite-icon-48 arrow-down black"></span>
                </div>
            </div>
        </div>
        <div class="filter-options panel-collapse panel-body collapse in" id="filter-type-options">
            <div class="options">
                <f:for each="{types}" as="type">
                    <div class="checkbox type-select-checkbox">
                        <f:form.checkbox id="filter-type-{type.uid}" name="types[{type.uid}]" value="1" class="input-checkbox" checked="{type.selected}" />
                        <label for="filter-type-{type.uid}">
                            <f:translate id="likes.type.{type.uid}" />
                        </label>
                    </div>
                </f:for>
            </div>
        </div>
    </div>
</div>
