@layer components {
    .wysiwyg {
        @apply overflow-x-auto;
        > * + * {
            @apply mt-4;

            &:is(ul, ol) {
                @apply mt-2;
            }
        }

        figure {
            @apply max-w-full;
        }

        &.has-background {
            a {
                @apply text-primary-900 underline hover:text-primary-700 focus-visible:text-primary-700;
            }
        }

        table {
            @apply w-full table;

            tbody {
                @apply divide-y divide-secondary;

                tr {
                    @apply divide-x divide-secondary;

                    &:last-child {
                        @apply divide-y-0;
                    }

                    td {
                        @apply p-2;
                    }
                }
            }
        }
            
    }
}
