config.tx_extbase {
    persistence {
        classes {
            Redkiwi\TalismanTemplate\Domain\Model\Category {
                mapping {
                    tableName = sys_category
                    columns {
                        parent.mapOnProperty = parent

                        # load children from uid
                        uid {
                            mapOnProperty = children
                            config {
                                type = select
                                foreign_table = sys_category
                                foreign_field = parent
                                foreign_sortby = sorting
                            }
                        }
                    }
                }
            }
        }
    }
}
