<?php

namespace Redkiwi\TalismanPage\Utility;

class DateUtility
{
    /**
     * Get UTC ISO date string
     *
     * @usage in Solr datetime object
     * @param \DateTime $dateTime
     * @return string
     */
    public static function getUTCDate($dateTime = null)
    {
        if ($dateTime instanceof \DateTime) {
            // Make sure the object is not touched..
            $date = clone $dateTime;
            $dateTime->setTimezone(new \DateTimeZone('UTC'));
            return $date->format('Y-m-d\TG:i:s\Z');
        }
        return null;
    }
}
