<?php
# PackageStates.php

# This file is maintained by TYPO3's package management. Although you can edit it
# manually, you should rather use the extension manager for maintaining packages.
# This file will be regenerated automatically if it doesn't exist. Deleting this file
# should, however, never become necessary if you use the package commands.

return [
    'packages' => [
        'core' => [
            'packagePath' => 'typo3/sysext/core/',
        ],
        'scheduler' => [
            'packagePath' => 'typo3/sysext/scheduler/',
        ],
        'extbase' => [
            'packagePath' => 'typo3/sysext/extbase/',
        ],
        'fluid' => [
            'packagePath' => 'typo3/sysext/fluid/',
        ],
        'frontend' => [
            'packagePath' => 'typo3/sysext/frontend/',
        ],
        'fluid_styled_content' => [
            'packagePath' => 'typo3/sysext/fluid_styled_content/',
        ],
        'filelist' => [
            'packagePath' => 'typo3/sysext/filelist/',
        ],
        'impexp' => [
            'packagePath' => 'typo3/sysext/impexp/',
        ],
        'form' => [
            'packagePath' => 'typo3/sysext/form/',
        ],
        'install' => [
            'packagePath' => 'typo3/sysext/install/',
        ],
        'recordlist' => [
            'packagePath' => 'typo3/sysext/recordlist/',
        ],
        'backend' => [
            'packagePath' => 'typo3/sysext/backend/',
        ],
        'reports' => [
            'packagePath' => 'typo3/sysext/reports/',
        ],
        'belog' => [
            'packagePath' => 'typo3/sysext/belog/',
        ],
        'beuser' => [
            'packagePath' => 'typo3/sysext/beuser/',
        ],
        'extensionmanager' => [
            'packagePath' => 'typo3/sysext/extensionmanager/',
        ],
        'info' => [
            'packagePath' => 'typo3/sysext/info/',
        ],
        'lowlevel' => [
            'packagePath' => 'typo3/sysext/lowlevel/',
        ],
        'redirects' => [
            'packagePath' => 'typo3/sysext/redirects/',
        ],
        'rte_ckeditor' => [
            'packagePath' => 'typo3/sysext/rte_ckeditor/',
        ],
        'seo' => [
            'packagePath' => 'typo3/sysext/seo/',
        ],
        'tstemplate' => [
            'packagePath' => 'typo3/sysext/tstemplate/',
        ],
        'talisman_template' => [
            'packagePath' => 'typo3conf/ext/talisman_template/',
        ],
        'talisman_geo' => [
            'packagePath' => 'typo3conf/ext/talisman_geo/',
        ],
        'talisman_page' => [
            'packagePath' => 'typo3conf/ext/talisman_page/',
        ],
        'talisman_map' => [
            'packagePath' => 'typo3conf/ext/talisman_map/',
        ],
        'talisman_content' => [
            'packagePath' => 'typo3conf/ext/talisman_content/',
        ],
        'talisman_accommodations' => [
            'packagePath' => 'typo3conf/ext/talisman_accommodations/',
        ],
        'talisman_esb_connector' => [
            'packagePath' => 'typo3conf/ext/talisman_esb_connector/',
        ],
        'solr' => [
            'packagePath' => 'typo3conf/ext/solr/',
        ],
        'talisman_inspiration' => [
            'packagePath' => 'typo3conf/ext/talisman_inspiration/',
        ],
        'talisman_example_trip' => [
            'packagePath' => 'typo3conf/ext/talisman_example_trip/',
        ],
        'koning_library' => [
            'packagePath' => 'typo3conf/ext/koning_library/',
        ],
        'talisman_newsletter' => [
            'packagePath' => 'typo3conf/ext/talisman_newsletter/',
        ],
        'talisman_tiles' => [
            'packagePath' => 'typo3conf/ext/talisman_tiles/',
        ],
        'rk_core' => [
            'packagePath' => 'typo3conf/ext/rk_core/',
        ],
        'rk_external_authentication' => [
            'packagePath' => 'typo3conf/ext/rk_external_authentication/',
        ],
        'talisman_blog' => [
            'packagePath' => 'typo3conf/ext/talisman_blog/',
        ],
        'talisman_experts' => [
            'packagePath' => 'typo3conf/ext/talisman_experts/',
        ],
        'talisman_highlighted_specials' => [
            'packagePath' => 'typo3conf/ext/talisman_highlighted_specials/',
        ],
        'talisman_travelogues' => [
            'packagePath' => 'typo3conf/ext/talisman_travelogues/',
        ],
        'talisman_tripadvisor' => [
            'packagePath' => 'typo3conf/ext/talisman_tripadvisor/',
        ],
        'gridelements' => [
            'packagePath' => 'typo3conf/ext/gridelements/',
        ],
        'koning_instagram' => [
            'packagePath' => 'typo3conf/ext/koning_instagram/',
        ],
        'koning_recaptcha' => [
            'packagePath' => 'typo3conf/ext/koning_recaptcha/',
        ],
        'talisman_bootstrap_carousel' => [
            'packagePath' => 'typo3conf/ext/talisman_bootstrap_carousel/',
        ],
    ],
    'version' => 5,
];
