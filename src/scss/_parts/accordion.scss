:root {
    --transitionLength: 400ms;
    --transitionTiming: cubic-bezier(0.22, 0.61, 0.36, 1);
}

.accordion-trigger-input {
    clip: rect(0 0 0 0);
    clip-path: inset(50%);
    height: 1px;
    overflow: hidden;
    position: absolute;
    white-space: nowrap;
    width: 1px;
}

.accordion-trigger-input:checked ~,
[data-expanded="true"] > {
    .accordion-animation-wrapper {
        grid-template-rows: 1fr;

        > div > .content-wrapper {
            @apply visible translate-y-0;
            transition: transform var(--transitionLength) var(--transitionTiming), visibility 200ms linear;
        }
    }
}

.accordion-animation-wrapper {
    @apply grid;
    grid-template-rows: 0fr;
    transition: grid-template-rows var(--transitionLength) var(--transitionTiming);
}

.content-wrapper {
    @apply invisible -translate-y-full;
    transition: transform var(--transitionLength) var(--transitionTiming), visibility 200ms var(--transitionLength) var(--transitionTiming);
}
