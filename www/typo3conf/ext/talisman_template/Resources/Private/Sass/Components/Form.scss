//
// General form styling
//
form {

    &.form {

        label {
            @include custom-font-family('Nunito', 400);
            color: $black;
            font-size: 15px;
            font-weight: normal;
            margin-bottom: 4px;
        }

        .form-control {
            border-color: $form-input-border;
            border-radius: 0;
            box-shadow: none;
            font-size: 15px;

            &:focus, &:active {
                box-shadow: none;
            }

            &[type="text"] {
                height: 40px;
            }

            &.parsley-error {

                border-color: $form-error-border;

            }

            &.parsley-success {

                border-color: $form-success-border;

            }

        }

        .submit-row {
            margin-top: 75px;

            .mandatory {
                @include custom-font-family('Nunito', 400);
                color: $form-mandatory;
                display: block;
                font-size: 15px;
                margin-bottom: 2px;
                margin-top: 52px;
            }
        }

        input[data-provide="datepicker"][readonly="readonly"] {
            background: $white;
        }

        .form-group {
            margin-bottom: 25px;

            &.has-error {
                margin-bottom: 0;

                .form-control {
                    background: $form-error-background;
                    border-color: $form-error-border;
                }
            }

            .col-xs-1 {
                padding: 8px 0 0 0;

                .talisman-note {
                    cursor: help;
                    font-size: 20px;
                }
            }

            a {

                &.postcode-link {
                    @include custom-font-family('Nunito', 600);
                    display: inline-block;
                    font-size: 13px;
                    margin-top: 9px;
                    text-transform: uppercase;
                }
            }
        }

        .parsley-errors-list {
            list-style: none;
            margin: 0;
            padding: 0;
        }

        .alert,
        .parsley-required,
        .parsley-type,
        .parsley-minlength {
            @include custom-font-family('Nunito', 600);
            background: $white;
            border: none;
            font-size: 13px;
            margin: 3px 0 9px;
            padding: 0;

            &.alert-danger {
                color: $form-error-message;
            }
        }

        .parsley-required,
        .parsley-type,
        .parsley-minlength {
            color: $form-error-message;

            &:before {
                content: "\f071";
                display: inline-block;
                font-family: 'FontAwesome';
                margin-right: 5px;
            }

        }

        .required {
            color: $gray-light;
        }

        .input-group {

            &.file {

                .input-group-addon {
                    background: $rubine;
                    border: 1px solid $rubine;
                    cursor: pointer;
                    overflow: hidden;
                    position: relative;

                    i {
                        color: $white;
                        font-size: 16px;
                    }
                }

                input[type="file"] {
                    background: white;
                    cursor: inherit;
                    display: block;
                    filter: alpha(opacity=0);
                    font-size: 100px;
                    min-height: 100%;
                    min-width: 100%;
                    opacity: 0;
                    outline: none;
                    position: absolute;
                    right: 0;
                    text-align: right;
                    top: 0;
                }

                input[type="text"] {
                    background: $white;
                }
            }
        }
    }

    .checkbox {

        label {

            &::after {
                color: $rubine;
            }

            &::before {
                background: $checkbox-background;
                border-radius: 0;
            }
        }

        input[type="checkbox"] {

            &:checked + label::before {
                background: $white;
            }

        }
    }

    .radio-errors,
    .checkbox-errors {

        &.parsley-error {

            label {

                color: $form-error-border;

            }

        }

        &.parsley-success {

            label {

                color: $form-success-border;

            }

        }

    }

    .radio {

        &.parsley-error {

            label {

                color: $form-error-border;

            }

        }

        &.parsley-success {

            label {

                color: $form-success-border;

            }

        }

        input[type="radio"] {

            &:checked {

                + label::after {
                    background-color: $rubine;
                }
            }
        }
    }

    .radio-inline {

        + .radio-inline {
            margin-left: 30px;
        }
    }

    .checkbox-inline {

        + .checkbox-inline {
            margin-left: 30px;
        }
    }
}

//
// Multi select
//
.multi-select {
    @include custom-font-family('Nunito', 600);
    background: $white;
    color: $black;
    cursor: pointer;
    font-size: 13px;
    height: 40px;
    line-height: 40px;
    margin-bottom: 10px;
    position: relative;

    span {
        text-transform: uppercase;

        &.label-container {
            border: 1px solid $multi-select-border;
            display: block;
            height: 40px;
            padding-left: 15px;
            padding-top: 1px;
            width: 100%;

            i {
                @include sprite-icon-48(-24, -144);
                content: "";
                height: 24px;
                position: absolute;
                right: 20px;
                top: 8px;
                width: 24px;

                &.active {
                    background-position: -24px -168px;
                    top: 9px;
                }
            }
        }
    }

    ul {
        z-index: $zindex-lighter;

        &.top-level {
            display: none;
            left: 0;
            padding: 0;
            position: absolute;
            width: 100%;

            li {
                @include custom-font-family('Nunito', 600);
                background: $white;
                border-bottom: 1px solid $multi-select-border;
                border-left: 1px solid $multi-select-border;
                border-right: 1px solid $multi-select-border;
                color: #333333;
                font-size: 13px;
                left: 0;
                list-style-type: none;
                padding-left: 15px;
                position: relative;
                width: 100%;

                &::after {
                    @include sprite-icon-48(0, -120);
                    content: "";
                    height: 24px;
                    position: absolute;
                    right: 20px;
                    top: 6px;
                    width: 24px;
                }

                &:hover, &.active {
                    background: $black;
                    border-color: $multi-select-border;
                    color: $white;
                }

                ul {
                    display: none;
                    float: left;
                    left: 100%;
                    margin-left: -1px;
                    margin-top: -1px;
                    padding-left: 0;
                    position: absolute;
                    top: 0;
                    width: 100%;

                    li {

                        &::after {
                            display: none;
                        }

                        &:first-child {
                            border-top: 1px solid $multi-select-border;
                        }
                    }
                }
            }
        }
    }

    &.compact {

        ul {

            &.top-level {

                li {

                    ul {
                        left: 50%;
                        margin-left: 1px;
                        position: absolute;
                        width: 50%;
                    }
                }
            }
        }
    }
}

//
// XS styling
//
@media (max-width: $screen-xs-max) {

    form {
        &.form {
            .submit-row {
                .mandatory {
                    margin-top: 20px;
                }
            }
        }
    }

    .multi-select {

        &:not(.compact) {

            ul {

                &.top-level {

                    li {

                        ul {
                            position: absolute;
                            left: 50%;
                            margin-left: 1px;
                            width: 50%;
                        }
                    }
                }
            }
        }
    }
}
