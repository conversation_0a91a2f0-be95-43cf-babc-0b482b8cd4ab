<T3DataStructure>
    <meta>
        <langDisable>1</langDisable>
    </meta>
    <ROOT>
        <type>array</type>
        <el>
            <settings.data.url>
                <TCEforms>
                    <label>LLL:EXT:koning_instagram/Resources/Private/Language/locallang_be.xlf:configuration.url</label>
                    <config>
                        <type>input</type>
                        <required>1</required>
                    </config>
                </TCEforms>
            </settings.data.url>
            <settings.data.max_width>
                <TCEforms>
                    <label>LLL:EXT:koning_instagram/Resources/Private/Language/locallang_be.xlf:configuration.max_width</label>
                    <config>
                        <type>input</type>
                        <required>1</required>
                        <eval>int</eval>
                        <range>
                            <lower>320</lower>
                        </range>
                        <default>320</default>
                    </config>
                </TCEforms>
            </settings.data.max_width>
            <settings.data.hide_caption>
                <TCEforms>
                    <label>LLL:EXT:koning_instagram/Resources/Private/Language/locallang_be.xlf:configuration.hide_caption</label>
                    <config>
                        <type>check</type>
                    </config>
                </TCEforms>
            </settings.data.hide_caption>
            <settings.data.omit_script>
                <TCEforms>
                    <label>LLL:EXT:koning_instagram/Resources/Private/Language/locallang_be.xlf:configuration.omit_script</label>
                    <config>
                        <type>check</type>
                    </config>
                </TCEforms>
            </settings.data.omit_script>
            <settings.data.json_callback>
                <TCEforms>
                    <label>LLL:EXT:koning_instagram/Resources/Private/Language/locallang_be.xlf:configuration.json_callback</label>
                    <config>
                        <type>input</type>
                        <eval>trim</eval>
                    </config>
                </TCEforms>
            </settings.data.json_callback>
        </el>
    </ROOT>
</T3DataStructure>
