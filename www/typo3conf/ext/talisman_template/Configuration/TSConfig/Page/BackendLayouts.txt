mod {
    web_layout {
        BackendLayouts {
            default {
                title = Default (100% width) [no sub menu]
                config {
                    backend_layout {
                        colCount = 1
                        rowCount = 2
                        rows {
                            1 {
                                columns {
                                    1 {
                                        name = Content
                                        colPos = 1
                                    }
                                }
                            }
                            2 {
                                columns {
                                    1 {
                                        name = Expert widget
                                        colPos = 3
                                    }
                                }
                            }
                        }
                    }
                }
            }
            twoColumns {
                title = Two Columns in grid [with sub menu]
                config {
                    backend_layout {
                        colCount = 2
                        rowCount = 2
                        rows {
                            1 {
                                columns {
                                    1 {
                                        name = Banner
                                        colPos = 1
                                    }
                                }
                            }
                            2 {
                                columns {
                                    1 {
                                        name = Content
                                        colPos = 2
                                    }
                                    2 {
                                        name = Right column
                                        colPos = 3
                                    }
                                }
                            }
                        }
                    }
                }
            }
            twoColumnsNoSubMenu {
                title = Two Columns in grid [no sub menu]
                config {
                    backend_layout {
                        colCount = 2
                        rowCount = 2
                        rows {
                            1 {
                                columns {
                                    1 {
                                        name = Banner
                                        colPos = 1
                                    }
                                    2 {
                                        name = Widget
                                        colPos = 4
                                    }
                                }
                            }
                            2 {
                                columns {
                                    1 {
                                        name = Content
                                        colPos = 2
                                    }
                                    2 {
                                        name = Right column
                                        colPos = 3
                                    }
                                }
                            }
                        }
                    }
                }
            }
            full {
                title = Full width in grid [no sub menu]
                config {
                    backend_layout {
                        colCount = 1
                        rowCount = 2
                        rows {
                            1 {
                                columns {
                                    1 {
                                        name = Content
                                        colPos = 2
                                    }
                                }
                            }
                            2 {
                                columns {
                                    1 {
                                        name = Expert widget
                                        colPos = 3
                                    }
                                }
                            }
                        }
                    }
                }
            }
            expertList {
                title = Template for expert list page
                config {
                    backend_layout {
                        colCount = 2
                        rowCount = 2
                        rows {
                            1 {
                                columns {
                                    1 {
                                        name = Banner
                                        colPos = 1
                                    }
                                }
                            }
                            2 {
                                columns {
                                    1 {
                                        name = Content
                                        colPos = 2
                                    }
                                    2 {
                                        name = Right column
                                        colPos = 3
                                    }
                                }
                            }
                        }
                    }
                }
            }
            exampleTrip {
                title = Fullscreen example trip slider
                config {
                    backend_layout {
                        colCount = 1
                        rowCount = 1
                        rows {
                            1 {
                                columns {
                                    1 {
                                        name = Content
                                        colPos = 1
                                    }
                                }
                            }
                        }
                    }
                }
            }
            blog {
                title = Blog
                config {
                    backend_layout {
                        colCount = 1
                        rowCount = 2
                        rows {
                            1 {
                                columns {
                                    1 {
                                        name = Banner
                                        colPos = 1
                                    }
                                }
                            }
                            2 {
                                columns {
                                    1 {
                                        name = Content
                                        colPos = 2
                                    }
                                }
                            }
                        }
                    }
                }
            }
            footer {
                title = Footer
                config {
                    backend_layout {
                        colCount = 1
                        rowCount = 1
                        rows {
                            1 {
                                columns {
                                    1 {
                                        name = Widget
                                        colPos = 1
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}
