import Chart, {ChartData} from 'chart.js/auto';

(async function () {
    const gmwChart = document.getElementById('GMW');
    const gwwawChart = document.getElementById('GWWA');

    if (gmwChart) {
        createChart(gmwChart, {
            yLabel: '%',
            type: 'line',
        });
    }

    if (gwwawChart) {
        createChart(gwwawChart, {
            yLabel: '€',
            type: 'bar',
        });
    }
})();

type ChartValue = {
    date: string;
    value: number;
};

type ChartOptions = {
    xLabel?: string;
    yLabel?: string;
    type: 'bar' | 'line';
};

function createChart(wrapper: HTMLElement, options: ChartOptions) {
    let data = JSON.parse(wrapper.dataset.chartValues ?? '[]') as ChartValue[];

    const nationalAverageData = JSON.parse(
        wrapper.dataset.chartNationalAverage ?? '[]',
    ) as ChartValue[];

    // Calculate shortest length of the two datasets to prevent fetching unprovided data
    const shortestLength = Math.min(data.length, nationalAverageData.length);

    data = equalizeDataRows(data, nationalAverageData);

    let extraOptions = {};
    if (options.type === 'bar') {
        extraOptions = {
            backgroundColor: '#5FB0B9',
        };
    }

    const datasets: ChartData['datasets'] = [
        {
            label: wrapper.dataset.chartLabel,
            data: data.slice(0, shortestLength).map((row) => row.value),
            ...extraOptions,
        },
        {
            label: 'Landelijke gemiddelde',
            data: nationalAverageData.slice(0, shortestLength).map((row) => row.value),
            backgroundColor: '#11355E',
            borderColor: '#11355E',
        },
    ];

    if (wrapper.dataset.chartXLabel) {
        options.xLabel = wrapper.dataset.chartXLabel;
    }

    new Chart(wrapper.querySelector('canvas')!, {
        type: options.type,
        data: {
            labels: data.slice(0, shortestLength).map((row) => {
                const date = new Date(row.date);
                const year = date.getFullYear();
                const month = date.getMonth() + 1;

                return `${year}-${month}`;
            }),
            datasets,
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            color: '#001731',
            borderColor: '#5FB0B9',
            backgroundColor: '#E6F5F6',
            scales: {
                y: {
                    stacked: false,
                    title: {
                        display: !!options.yLabel,
                        text: options.yLabel,
                    },
                    grid: {
                        display: false,
                    },
                },
                x: {
                    stacked: false,
                    title: {
                        display: !!options.xLabel,
                        text: options.xLabel,
                    },
                    grid: {
                        display: false,
                    },
                },
            },
        },
    });
}

function equalizeDataRows(
    data: Array<ChartValue>,
    nationalAverageData: Array<ChartValue>,
): Array<ChartValue> {
    let years = data.map((d) => {
        const date = new Date(d.date);
        return date.getFullYear();
    });

    const nationalAverageYears = nationalAverageData.map((d) => {
        const date = new Date(d.date);
        return date.getFullYear();
    });

    if (years.length === nationalAverageYears.length) {
        return data;
    }

    data.sort((a: ChartValue, b: ChartValue) =>
        new Date(a.date).getFullYear() > new Date(b.date).getFullYear()
            ? 1
            : -1,
    );

    return data;
}
