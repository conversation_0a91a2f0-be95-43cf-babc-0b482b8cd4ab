Talisman.Layout = {

    /**
     * Scroll offset of window when to collapse the nav bar brand (1 = 100%)
     *
     * @var Number
     */
    navBarBrandCollapseOffset: 0.25,

    /**
     * @var Object
     */
    screenSizes: {
        sm: 768,
        md: 992,
        lg: 1200
    },

    /**
     * Initialise site wide layout functionality
     *
     * @return void
     */
    init: function () {

        /**
         * When clicking the login menu item, toggle the login form and page overlay
         */
        $('a[data-toggle="login-form"]').click(function () {
            if ($(window).width() <= Talisman.Layout.screenSizes.sm) {
                return true;
            }
            $('#login').toggle();
            $(this).parent().toggleClass('active');
            $('#overlay').toggle();
            $('body').toggleClass('no-scroll');
            return false;
        });

        /**
         * Close login or search box when clicking on the active overlay
         */
        $('#overlay').click(function () {
            if ($('#login').is(':visible')) {
                $('nav#top-bar a[data-toggle="login-form"]').trigger('click');
            }
        });

        /**
         * On scrolling the page, check for collapsing the brand
         */
        $(window).scroll(function () {
            if ($('#main-menu').hasClass('animated')) {
                Talisman.Layout.collapseBrand();
            }
        });

        /**
         * Select styling
         */
        $('.select-picker').selectpicker({
            style: 'btn-select',
            size: 10
        });

        /**
         * When toggling the mobile menu, check what needs to happen with the navbar brand collapse mechanism
         *
         * Also toggle hamburger icon and cross icon
         */
        $('button[data-toggle="collapse"], span[data-toggle="collapse"]').click(function () {
            if ($(this).hasClass('collapsed')) {
                $('button[data-toggle="collapse"]').css('display', 'none');
                $('span[data-toggle="collapse"]').show();

                $('#main-menu').addClass('collapsed');
            } else {
                $('button[data-toggle="collapse"]').css('display', '');
                $('span[data-toggle="collapse"]').hide();

                var heightToReach = $(window).height() * Talisman.Layout.navBarBrandCollapseOffset;
                if ($(window).scrollTop() < heightToReach) {
                    $('#main-menu').removeClass('collapsed');
                } else {
                    $('#main-menu').addClass('collapsed');
                }
            }
        });

        /**
         * Row equal functionality
         */
        $('.row-equal .col-sm-6').matchHeight();

        /**
         * Anchor links
         */
        $('a[data-action="anchor"]').click(function () {
            var target = $(this).attr('href');
            if (target === '#talisman-experts' && $(window).width() < Talisman.Layout.screenSizes.md && (parseInt($('body').attr('data-page')) === 4 || parseInt($('body').attr('data-page')) === 5)) {
                $('#expert-modal').modal('show');
            } else {
                var menuHeight = Talisman.Layout.getVisibleMenuHeight() + 15;
                $('html,body').animate({scrollTop: $(target).offset().top - menuHeight}, 'slow');
            }
            return false;
        });

        /**
         * Initialise sub menu and expert widget affixes
         */
        Talisman.Layout.subMenuAffix();
        Talisman.Layout.expertWidgetAffix();
        Talisman.Layout.collapsePanelIcons();


        $('nav#main-menu .dropdown .dropdown-link').on('click', function(e) {

            $(this).siblings('.dropdown-menu').toggleClass('active');
            $(this).closest('li.dropdown').toggleClass('hover');
            return false;
        })

        /**
         * Close the Destinations dropdown menu on button click
         */
        $('nav#main-menu .dropdown-menu .btn').click(function () {
            $(this).closest('.dropdown-menu').removeClass('active');
            $(this).closest('li.dropdown').removeClass('hover');
            return false;
        });

        $('.dropdown-link').each(function(index, el) {
            if ($(window).width() >= Talisman.Layout.screenSizes.sm) {
                return true;
            }
            // if el has href, replace it with a #
            if ($(el).attr('href')) {
                $(el).attr('href', '#');
            }
        });

        /**
         * Stop play back when modals with embedded YouTube videos are closed
         */
        $('.modal').on('hidden.bs.modal', function () {
            $(this).find('iframe[data-iframe="youtube"]').attr('src', $(this).find('iframe[data-iframe="youtube"]').attr('src'));
        });

        /**
         * Take sticky header into account on page load with anchors
         */
        if (location.href.indexOf('#') !== -1) {
            var target = location.href.split('#')[1];
            // facebook callback adds #_=_ to urls
            // skip when using hashbang for inspiration plugin
            if ((target.indexOf('!') !== 0 && target !== '_=_') && $('#' + target).length > 0) {
                var menuHeight = Talisman.Layout.getVisibleMenuHeight() + 15;
                $('html,body').animate({scrollTop: $('#' + target).offset().top - menuHeight}, 'slow');
            }
        }

        /**
         * Take sticky header into account on anchor click
         */
        $('a[data-role="anchor-link"]').click(function() {
            if (location.pathname.replace(/^\//,'') == this.pathname.replace(/^\//,'')
                || location.hostname == this.hostname)
            {
                var target = $(this.hash);
                var menuHeight = Talisman.Layout.getVisibleMenuHeight() + 15;
                target = target.length ? target : $('[name=' + this.hash.slice(1) +']');
                if (target.length) {
                    $('html,body').animate({scrollTop: target.offset().top - menuHeight}, 'slow');
                    return false;
                }
            }
        });

        if (!$('#main-menu').hasClass('animated')) {
            $('#main-menu .navbar-brand').addClass('collapsed');
        }

        /**
         * Tooltips
         */
        $('[data-toggle="tooltip"]').tooltip({
            container: 'body'
        });

        /**
         * Close login box on search box click
         */
        $('.search-container input').click(function() {
            if ($('#login').is(':visible')) {
                $('nav#top-bar a[data-toggle="login-form"]').trigger('click');
            }
        });
    },

    /**
     * @return int
     */
    getVisibleMenuHeight: function () {
        var menuHeight = $('nav#main-menu').height();
        if ($('nav#top-bar').is(':visible')) {
            menuHeight += $('nav#top-bar').height();
        }
        if ($('.sub-menu').is(':visible') && $(window).width() >= Talisman.Layout.screenSizes.sm) {
            menuHeight += $('.sub-menu').height();
        }
        return menuHeight;
    },

    /**
     * @return int
     */
    getTopOffset: function() {
        var topOffset = 0;
        if ($('.sub-menu-container').length > 0) {
            topOffset = $('.sub-menu-container').offset().top;
        } else if ($('.content-wrap .banner').length > 0) {
            topOffset = $('.content-wrap .banner').offset().top + $('.content-wrap .banner').outerHeight();
        }
        if ($('nav#top-bar').is(':visible')) {
            topOffset -= $('nav#top-bar').outerHeight();
        }
        if ($('nav#main-menu').is(':visible')) {
            topOffset -= $('nav#main-menu').outerHeight();
        }
        return topOffset;
    },

    /**
     * @returns {string}
     */
    getModalTemplate: function (header, body, footer) {
        var template = '<div class="modal fade" tabindex="-1" role="dialog" aria-hidden="true"><div class="modal-dialog" role="document"><div class="modal-content">';

        if (header) {
            template += '<div class="modal-header">' +
                '<button type="button" class="close" data-dismiss="modal" aria-hidden="true">'
                + '<span class="sprite-icon-48 cross xl"></span> ' +
                '</button>' +
                '</div>';
        }

        if (body) {
            template += '<div class="modal-body"></div>';
        }

        if (footer) {
            template += '<div class="modal-footer"></div>';
        }

        template += '</div></div></div>';
        return $(template);
    },

    /**
     * Returns true when 50% of the container is in the viewport
     *
     * @param container
     * @return boolean
     */
    elementIsInViewport: function (container) {
        var scrollBottom = $(window).scrollTop() + $(window).height();
        var containerHalfwayPosition = container.offset().top + (container.height() / 2);
        return scrollBottom >= containerHalfwayPosition;
    },

    /**
     * @return void
     */
    subMenuAffix: function () {
        if ($('.sub-menu').length > 0) {
            $('.sub-menu').affix({
                offset: {
                    top: Talisman.Layout.getTopOffset
                }
            }).on('affixed.bs.affix', function() {
                Talisman.Layout.replaceMainMenuBorder();
            });

            if ($('.sub-menu').hasClass('affix')) {
                Talisman.Layout.replaceMainMenuBorder();
            }
        }
    },

    /**
     * @return void
     */
    replaceMainMenuBorder: function() {
        if ($(window).width() >= Talisman.Layout.screenSizes.sm) {
            $('nav#main-menu').addClass('white-border');
        }
    },

    /**
     * @return void
     */
    expertWidgetAffix: function () {
        if ($('.right-column').length > 0) {
            if (this.isPhone() === false) {
                $('.right-column .talisman-experts-affix').affix({
                    offset: {
                        top: Talisman.Layout.getTopOffset,
                        bottom: function () {
                            var bottomOffset = $('footer').outerHeight();
                            bottomOffset -= $('.talisman-experts.widget').outerHeight();
                            bottomOffset -= 47;
                            return bottomOffset;
                        }
                    }
                });
            }
        }
    },

    /**
     * Collapses the navbar brand when the visitor has scrolled past x% of the window height
     *
     * @return void
     */
    collapseBrand: function () {
        // Only when mobile menu is not collapsed
        if ($('#menu').attr('aria-expanded') !== 'true') {
            var heightToReach = $(window).height() * Talisman.Layout.navBarBrandCollapseOffset;
            if ($(window).scrollTop() > heightToReach) {
                $('#main-menu').addClass('collapsed');
            } else {
                $('#main-menu').removeClass('collapsed');
            }
        }
    },

    /**
     * Add generic panel collapse functionality based on accordion styling
     */
    collapsePanelIcons: function () {
        var panelLinks = $('.panel .panel-collapse-icons');
        if (panelLinks.length > 0) {
            panelLinks.each(function () {
                var panel = $(this);
                var target = $(panel.data('target'));
                if (target.length > 0) {
                    target.on('shown.bs.collapse', function () {
                        panel.find('.icons .toggle-open').toggleClass('hide', true);
                        panel.find('.icons .toggle-close').toggleClass('hide', false);

                        // Scroll to top panel based on active items in template
                        var menuHeight = $('nav#main-menu').height();
                        if ($('nav#top-bar').is(':visible')) {
                            menuHeight += $('nav#top-bar').height();
                        }
                        if ($('.sub-menu').is(':visible')) {
                            menuHeight += $('.sub-menu').height();
                        }

                        menuHeight += 40;

                        $('html,body').animate({scrollTop: panel.offset().top - menuHeight}, 'slow');
                    });

                    target.on('hidden.bs.collapse', function () {
                        panel.find('.icons .toggle-open').toggleClass('hide', false);
                        panel.find('.icons .toggle-close').toggleClass('hide', true);
                    });
                }
            });

        }
    },

    /**
     * Checks if current device is a phone based on user agent
     *
     * @return boolean
     */
    isPhone: function () {
        return (navigator.userAgent.match(/Android|iPhone|WebOS|BlackBerry|IEMobile|Opera Mini/i) && !navigator.userAgent.match(/iPod|iPad/i)) == true;
    },

    /**
     * Checks if current device is a tablet based on user agent
     *
     * @return boolean
     */
    isTablet: function () {
        return (navigator.userAgent.match(/Tablet|iPad|iPod/i) !== null)
    }
};
