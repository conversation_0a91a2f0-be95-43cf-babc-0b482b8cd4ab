//
// 24x24 sprite icons
//
.sprite-icon-48 {
    background-image: url(/typo3conf/ext/talisman_template/Resources/Public/Icons/sprite-48.png);
    background-repeat: no-repeat;
    background-size: 120px 500px;
    width: 24px;
    height: 24px;
    display: inline-block;

    &.heart {
        background-position: 0 0;
    }

    &.heart-white {
        background-position: -24px 0;
    }

    &.heart-white-outline {
        background-position: -72px 0;
    }

    &.login {
        background-position: 0 -24px;
    }

    &.search {
        background-position: 0 -48px;
    }

    &.phone-circle {
        background-position: 0 -72px;

        &.white {
            background-position: -24px -72px;
        }
    }

    &.black-cross {
        background-position: 0 -168px;
    }

    &.cross {
        background-position: -24px -168px;

        &.xl {
            background-position: -72px -168px;
        }
    }

    &.arrow-left {

        &.double {
            background-position: -68px -96px;
        }

        &.xl {
            background-position: -72px -120px;
        }
    }

    &.arrow-right {

        &.double {
            background-position: -96px -96px;
        }

        &.xl {
            background-position: -48px -120px;
        }
    }

    &.info {
        background-position: 0 -216px;

        &.white {
            background-position: -24px -216px;
        }
    }

    &.phone {
        background-position: 0 -192px;

        &.white {
            background-position: -24px -192px;
        }
    }

    &.enlarge {
        background-position: 0 -240px;
    }

    &.minus {
        background-position: -48px -146px;
    }

    &.arrow-down {

        &.black {
            background-position: -24px -146px;
        }
    }

    &.user {
        background-position: 0 -264px;
    }

    &.details {
        background-position: -24px -264px;
    }

    &.checklist {
        background-position: -48px -264px;
    }

    &.send {
        background-position: -72px -264px;
    }

    &.marker {
        background-position: 0 -288px;

        &.white {
            background-position: -48px -288px;
        }

        &.black {
            background-position: -24px -288px;
        }
    }

    &.toggle {
        background-position: 0 -312px;

        &.black {
            background-position: -24px -312px;
        }

        &.close-toggle {
            background-position: -48px -312px;
        }
    }

    &.text {
        background-position: 0 -336px;
    }

    &.check {
        background-position: -24px -336px;
        &.pink {
            background-position: -54px -340px;
        }
    }
}

@mixin sprite-icon-48($x, $y) {
    background-image: url(/typo3conf/ext/talisman_template/Resources/Public/Icons/sprite-48.png);
    background-repeat: no-repeat;
    background-size: 120px 500px;
    background-position: $x + px $y + px;
}
