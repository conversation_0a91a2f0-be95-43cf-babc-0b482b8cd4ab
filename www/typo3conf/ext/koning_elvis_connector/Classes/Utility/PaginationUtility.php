<?php
namespace Keizer\KoningElvisConnector\Utility;

/**
 * Utility: Pagination
 *
 * @package Keizer\KoningElvisonnector\Utility
 */
class PaginationUtility
{
    /**
     * @param int $total
     * @param int $assetsPerPage
     * @param int $start
     * @return array
     */
    static public function getAssetsPagination($total, $assetsPerPage, $start)
    {
        $pagination = [
            'total' => 0,
            'pages' => 0,
            'previousButton' => [
                'show' => false,
                'start' => 0
            ],
            'nextButton' => [
                'show' => false,
                'start' => 0
            ],
            'showing' => [
                'from' => 0,
                'to' => 0
            ]
        ];

        $pagination['total'] = $total;
        $pagination['pages'] = (int) ceil($pagination['total'] / $assetsPerPage);
        $pagination['showing']['from'] = $start + 1;
        if ($pagination['pages'] > 1) {
            if (($start + 1) > $assetsPerPage) {
                $pagination['previousButton']['show'] = true;
                $pagination['previousButton']['start'] = $start - $assetsPerPage;
            }

            if ($pagination['total'] <= ($start + $assetsPerPage)) {
                $pagination['showing']['to'] = $pagination['total'];
            } else {
                $pagination['showing']['to'] = $start + $assetsPerPage;
            }

            if ($pagination['total'] > ($start + $assetsPerPage)) {
                $pagination['nextButton']['show'] = true;
                $pagination['nextButton']['start'] = $start + $assetsPerPage;
            }
        } elseif ($pagination['pages'] === 1) {
            $pagination['showing']['to'] = $pagination['total'];
        }
        return $pagination;
    }
}
