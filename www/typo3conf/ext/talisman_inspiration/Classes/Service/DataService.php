<?php
namespace Redkiwi\TalismanInspiration\Service;

use ApacheSolrForTypo3\Solr\Domain\Search\Query\Query;
use ApacheSolrForTypo3\Solr\Domain\Search\Query\QueryBuilder;
use ApacheSolrForTypo3\Solr\Search;
use Redkiwi\TalismanInspiration\Usage\InspirationSolr;
use Redkiwi\TalismanInspiration\Utility\ConfigurationUtility;
use TYPO3\CMS\Core\Resource\File;
use TYPO3\CMS\Core\Resource\ProcessedFile;
use TYPO3\CMS\Core\Utility\GeneralUtility;

/**
 * Service: Data Retrieval
 *
 * @package Redkiwi\TalismanInspiration\Service
 */
class DataService implements \TYPO3\CMS\Core\SingletonInterface
{

    use InspirationSolr;

    /**
     * @param integer $type
     * @param array $filter
     * @param integer $page
     * @param integer $itemsPerPage
     * @return array
     * @throws \Exception
     */
    public function getIndexedPages($type, array $filter = [], $page, $itemsPerPage)
    {
        /** @var QueryBuilder $queryBuilder */
        $queryBuilder = $this->getObjectManager()->get(QueryBuilder::class);
        
        // Define by type
        switch ($type) {
            case ConfigurationUtility::TYPE_INSPIRATIONS:
                // Make sure inspiration pages are requested
                $searchQuery = $queryBuilder->newSearchQuery('is_inspirational_b:true');
                break;
            case ConfigurationUtility::TYPE_SPECIALS:
                // Make sure inspiration pages are requested
                $searchQuery = $queryBuilder->newSearchQuery('is_special_b:true');
                break;
            case ConfigurationUtility::TYPE_PRIVATE:
                // Make sure private pages are requested
                $searchQuery = $queryBuilder->newSearchQuery('is_private_b:true');
                break;
            default:
                $searchQuery = $queryBuilder->newSearchQuery('*:*');
        }

        $filterArray = [];
        if (is_array($filter['static'])) {
            foreach ($filter['static'] as $category => $value) {
                $filterArray[] = 'category_' . $category . '_is:' . (int)$value;
            }
        }

        /** @var Query $query */
        $query = $searchQuery
            ->useResultsPerPage($itemsPerPage)
            ->useFilterArray($filterArray)
            ->usePage($page)
            ->getQuery();

        /** @var Search $search */
        $search = $this->getObjectManager()->get(Search::class);
        $response = $search->search($query, ($page - 1) * $itemsPerPage, $itemsPerPage)->getRawResponse();

        if (empty($response)) {
            // Safe error when no items returned
            $results = [
                'error' => [
                    'severity' => 'info',
                    'header' => 'Geen resultaten gevonden met de opgegeven filters',
                    'message' => ''
                ]
            ];
            
            return $results;
        }

        $data = json_decode($response, true);

        $to = (count($data['response']['docs']) < $itemsPerPage) ? count($data['response']['docs']) : $itemsPerPage;
                
        $output['information'] = [
            'total' => $data['response']['numFound'],
            'per_page' => $itemsPerPage,
            'current_page' => $page,
            'last_page' => ceil($data['response']['numFound'] / $itemsPerPage),
            'from' => $data['response']['start'],
            'to' => $data['response']['start'] + (int)$to
        ];

        // Make sure current_page cannot exceed last_page
        $output['information']['current_page'] = min((int)$output['information']['current_page'], $output['information']['last_page']);

        foreach ($data['response']['docs'] as $page) {
            $output['items'][] = $page;
        }

        foreach ($output['items'] as $key => $item) {
            // Return specific detail for item
            $output['items'][$key] = $this->mapInspirationData($item);
        }

        return $output;
    }

    /**
     * General mapping of inspiration solr data
     *
     * @param array $record
     * @return array
     */
    protected function mapInspirationData($record)
    {
        $imageUri = $record['visual_s'] ?: 'EXT:talisman_inspiration/Resources/Public/Images/no-image-fallback.jpg';
        // Generate image based on original visual
        try {
            $image = $this->getImageService()->getImage($imageUri, null, null);
        } catch (\Exception $e) {
            $image = null;
        }
        if ($image instanceof File) {
            $processingInstructions = [
                'width' => '750c',
                'height' => '750c',
            ];
            $processedImage = $this->getImageService()->applyProcessingInstructions($image, $processingInstructions);

            if ($processedImage instanceof ProcessedFile) {
                $imageUri = $this->getImageService()->getImageUri($processedImage, true);
            }
        } else {
            $imageUri = '/' . $imageUri;
        }

        return [
            'uid' => $record['uid_i'],
            'link' => $record['link_s'],
            'title' => $record['title_s'],
            'reference' => $record['like_reference_i'],
            'image' => $imageUri,
            'subtitle' => $record['subtitle_s'],
        ];
    }

    /**
     * @return \TYPO3\CMS\Extbase\Service\ImageService
     */
    protected function getImageService()
    {
        return $this->getObjectManager()->get(\TYPO3\CMS\Extbase\Service\ImageService::class);
    }

    /**
     * @return \TYPO3\CMS\Extbase\Object\ObjectManagerInterface
     */
    protected function getObjectManager()
    {
        return GeneralUtility::makeInstance(\TYPO3\CMS\Extbase\Object\ObjectManager::class);
    }
}
