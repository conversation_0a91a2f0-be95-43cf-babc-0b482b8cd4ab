<?php
if (!defined('TYPO3_MODE')) {
    die ('Access denied.');
}

return [
    'ctrl' => [
        'title' => 'LLL:EXT:talisman_example_trip/Resources/Private/Language/locallang_be.xlf:tx_talismanexampletrip_domain_model_price',
        'label' => 'title',
        'hideAtCopy' => true,
        'tstamp' => 'tstamp',
        'crdate' => 'crdate',
        'cruser_id' => 'cruser_id',
        'editlock' => 'editlock',
        'dividers2tabs' => true,
        'hideTable' => true,
        'sortby' => 'sorting',
        'delete' => 'deleted',
        'searchFields' => 'uid, title',
        'enablecolumns' => [
            'disabled' => 'hidden',
            'starttime' => 'starttime',
            'endtime' => 'endtime',
        ],
        'iconfile' => 'EXT:talisman_example_trip/Resources/Public/Icons/tx_talismanexampletrip_domain_model_price.png',
    ],
    'interface' => [
        'showRecordFieldList' => 'hidden, title, promotional_package, description'
    ],
    'types' => [
        0 => [
            'showitem' => 'title;;access, promotional_package, description'
        ]
    ],
    'palettes' => [
        'access' => [
            'showitem' => 'hidden, starttime, endtime',
            'canNotCollapse' => false
        ]
    ],
    'columns' => [
        'hidden' => [
            'exclude' => true,
            'label' => 'LLL:EXT:core/Resources/Private/Language/locallang_general.xlf:LGL.hidden',
            'config' => [
                'type' => 'check',
            ]
        ],
        'starttime' => [
            'exclude' => true,
            'l10n_mode' => 'mergeIfNotBlank',
            'label' => 'LLL:EXT:core/Resources/Private/Language/locallang_general.xlf:LGL.starttime',
            'config' => [
                'type' => 'input',
                'size' => '10',
                'max' => '20',
                'eval' => 'datetime',
            ]
        ],
        'endtime' => [
            'exclude' => true,
            'l10n_mode' => 'mergeIfNotBlank',
            'label' => 'LLL:EXT:core/Resources/Private/Language/locallang_general.xlf:LGL.endtime',
            'config' => [
                'type' => 'input',
                'size' => '8',
                'max' => '20',
                'eval' => 'datetime',
            ],
        ],
        'title' => [
            'exclude' => false,
            'l10n_mode' => 'mergeIfNotBlank',
            'label' => 'LLL:EXT:talisman_example_trip/Resources/Private/Language/locallang_be.xlf:tx_talismanexampletrip_domain_model_price.title',
            'config' => [
                'type' => 'input',
                'eval' => 'trim, required',
                'size' => 30,
            ]
        ],
        'description' => [
            'exclude' => false,
            'l10n_mode' => 'mergeIfNotBlank',
            'label' => 'LLL:EXT:talisman_example_trip/Resources/Private/Language/locallang_be.xlf:tx_talismanexampletrip_domain_model_price.description',
            'config' => [
                'type' => 'text',
                'eval' => 'trim',
            ]
        ],
        'promotional_package' => [
            'exclude' => false,
            'l10n_mode' => 'mergeIfNotBlank',
            'label' => 'LLL:EXT:talisman_example_trip/Resources/Private/Language/locallang_be.xlf:tx_talismanexampletrip_domain_model_price.promotional_package',
            'config' => [
                'type' => 'group',
                'internal_type' => 'db',
                'allowed' => 'tx_talismanesbconnector_domain_model_promotional_package',
                'wizards' => [
                    'suggest' => [
                        'type' => 'suggest'
                    ]
                ]
            ],
        ],
    ]
];
