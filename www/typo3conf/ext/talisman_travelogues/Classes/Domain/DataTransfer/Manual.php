<?php
namespace Redkiwi\TalismanTravelogues\Domain\DataTransfer;

/**
 * Data transfer: Manual
 *
 * @package Redkiwi\TalismanTravelogues\Domain\DataTransfer
 */
class Manual
{
    /**
     * @var string
     * @validate NotEmpty
     */
    protected $destinations;

    /**
     * @var string
     * @validate NotEmpty
     */
    protected $title;

    /**
     * @var boolean
     * @validate Boolean(is=true)
     */
    protected $agree;

    /**
     * @var array
     * @validate \Redkiwi\TalismanTemplate\Validation\Validator\MultipleFileValidator(allowedMimeTypes="image/jpeg,image/png")
     * @TYPO3\CMS\Extbase\Annotation\ORM\Lazy
     */
    protected $images;

    /**
     * @var string
     * @validate NotEmpty
     */
    protected $travelogue;

    /**
     * @return string
     */
    public function getDestinations()
    {
        return $this->destinations;
    }

    /**
     * @param string $destinations
     * @return void
     */
    public function setDestinations($destinations)
    {
        $this->destinations = $destinations;
    }

    /**
     * @return string
     */
    public function getTitle()
    {
        return $this->title;
    }

    /**
     * @param string $title
     * @return void
     */
    public function setTitle($title)
    {
        $this->title = $title;
    }

    /**
     * @return boolean
     */
    public function isAgree()
    {
        return $this->agree;
    }

    /**
     * @param boolean $agree
     * @return void
     */
    public function setAgree($agree)
    {
        $this->agree = $agree;
    }

    /**
     * @return array
     */
    public function getImages()
    {
        return $this->images;
    }

    /**
     * @param array $images
     * @return void
     */
    public function setImages($images)
    {
        $this->images = $images;
    }

    /**
     * @return string
     */
    public function getTravelogue()
    {
        return $this->travelogue;
    }

    /**
     * @param string $travelogue
     * @return void
     */
    public function setTravelogue($travelogue)
    {
        $this->travelogue = $travelogue;
    }
}
