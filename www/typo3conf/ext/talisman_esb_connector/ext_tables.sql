#
# Table structure for table 'pages'
#
CREATE TABLE pages (
    tx_talismanesbconnector_geo_tree_id int(11) DEFAULT '0' NOT NULL,
    tx_talismanesbconnector_service_id int(11) DEFAULT '0' NOT NULL,
    tx_talismanesbconnector_component_id int(11) DEFAULT '0' NOT NULL,
    tx_talismanesbconnector_latitude decimal(11,7) DEFAULT '0.0000000' NOT NULL,
    tx_talismanesbconnector_longitude decimal(11,7) DEFAULT '0.0000000' NOT NULL,

    INDEX geotree (tx_talismanesbconnector_geo_tree_id),
    INDEX service (tx_talismanesbconnector_service_id)
);

#
# Table structure for table 'tx_talismanesbconnector_domain_model_country'
#
CREATE TABLE tx_talismanesbconnector_domain_model_country (
    uid int(11) NOT NULL auto_increment,
    pid int(11) DEFAULT '0' NOT NULL,
    tstamp int(11) DEFAULT '0' NOT NULL,
    crdate int(11) DEFAULT '0' NOT NULL,
    cruser_id int(11) DEFAULT '0' NOT NULL,
    sorting int(10) DEFAULT '0' NOT NULL,
    editlock tinyint(4) DEFAULT '0' NOT NULL,
    deleted tinyint(4) DEFAULT '0' NOT NULL,
    hidden tinyint(4) DEFAULT '0' NOT NULL,
    name tinytext,
    iso_code varchar(2) DEFAULT '' NOT NULL,
    remote_identifier int(11) DEFAULT '0' NOT NULL,

    PRIMARY KEY (uid),
    KEY parent (pid)
);

#
# Table structure for table 'tx_talismanesbconnector_domain_model_promotional_package'
#
CREATE TABLE tx_talismanesbconnector_domain_model_promotional_package (
    uid int(11) NOT NULL auto_increment,
    pid int(11) DEFAULT '0' NOT NULL,
    tstamp int(11) DEFAULT '0' NOT NULL,
    crdate int(11) DEFAULT '0' NOT NULL,
    cruser_id int(11) DEFAULT '0' NOT NULL,
    sorting int(10) DEFAULT '0' NOT NULL,
    editlock tinyint(4) DEFAULT '0' NOT NULL,
    deleted tinyint(4) DEFAULT '0' NOT NULL,
    hidden tinyint(4) DEFAULT '0' NOT NULL,
    name tinytext,
    price tinytext,
    remote_identifier int(11) DEFAULT '0' NOT NULL,

    PRIMARY KEY (uid),
    KEY parent (pid)
);
