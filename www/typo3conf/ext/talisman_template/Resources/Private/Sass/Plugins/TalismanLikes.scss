.talisman-likes-container {
    position: relative;
}

.talisman-like-modal {
    .modal-dialog {
        margin: 100px auto;
        width: 350px;
    }

    .modal-content {
        position: relative;
        border-radius: 0;

        textarea.form-control {
            resize: vertical;
            min-height: 100px;
            box-shadow: none;
            border-color: $form-input-border;

            &:hover, &:focus, &:active {
                box-shadow: none;
                border-color: $form-input-border;
            }
        }
    }

    .modal-header {
        border: 0;

        .close {
            position: absolute;
            opacity: 1;
            right: 0;
            top: 0;
            margin-top: 0;
            background-color: rgba($black, .7);
            padding: 5px 7px;

            &:hover {
                background-color: $black;
            }
        }
    }

    .modal-body {
        .title {
            @include custom-font-family('Nunito', 600);
            font-size: $font-size-h4;
            text-transform: uppercase;
            margin-bottom: 30px;

            small {
                display: block;
                text-transform: initial;
            }
        }
    }

    .modal-footer {
        border: 0;

        .cancel {
            color: $black;
            padding-left: 0;
            padding-right: 0;

            &:hover {
                text-decoration: underline;
            }
        }

        .btn-rubine {
            transition: all 0.3s ease 0s;
            position: relative;
            overflow: hidden;

            &::after {
                @include sprite-icon-48(-24, -2);
                transition: margin 0.3s ease 0s;
                height: 24px;
                width: 24px;
                position: absolute;
                top: 6px;
                content: "";
                right: 20px;
            }

            @media (min-width: $screen-lg-min) {

                &:hover, &:focus, &:active {
                    padding: 8px 30px 6px 30px;

                    &::after {
                        margin-right: -50px;
                    }
                }
            }
        }
    }
}

.talisman-likes {
    padding-top: 50px;

    .items-row {
        margin-bottom: 70px;
    }

    .talisman-login {
        margin-bottom: 130px;

        a {
            width: 100%;

            @media (max-width: $screen-xs-max) {
                margin-bottom: 15px;
            }
        }
    }

    a {

        &.page-link {
            @include image-hover-zoom-effect;
            display: block;
        }
    }

    .file-reference-container {
        overflow: hidden;
        margin-bottom: 30px;
    }

    figure {
        position: relative;

        figcaption {
            color: $white;
            position: absolute;
            bottom: 15px;
            left: 20px;
            right: 20px;

            h4 {
                font-size: 21px;
                line-height: 21px;
                @include custom-font-family('Nunito', 600);
                text-transform: uppercase;
                margin: 0;
            }

            span {
                @include custom-font-family('Nunito', 400);
                font-size: 15px;
                line-height: 18px;
            }
        }

        &.active-remove {

            .moodboard-entry {

                &.remove {
                    background: $gold;
                }
            }
        }

        .remove-like-container, .edit-like-container {
            position: absolute;
            top: 50px;
            bottom: 15px;
            left: 15px;
            right: 15px;
            z-index: $zindex-heavy - 1;
            color: $white;
            display: none;
        }

        .actions {
            position: absolute;
            bottom: 0;
            width: 100%;
        }

        .edit-like-container {

            .links {
                margin-left: 21px;
                margin-right: 21px;
                margin-top: 10px;

                span {
                    @include custom-font-family('Nunito', 600);
                    color: $white;
                    text-transform: uppercase;
                    font-size: 13px;

                    @media (min-width: $screen-lg-min) {

                        &:hover, &:active, &:focus {
                            text-decoration: underline;
                        }
                    }
                }
            }

            textarea {
                height: 110px;

                &.form-control {
                    box-shadow: none;
                    border-color: $form-input-border;

                    &:hover, &:focus, &:active {
                        box-shadow: none;
                        border-color: $form-input-border;
                    }
                }
            }

            .btn-default {
                padding: 0 92px 0 20px;
                width: 100%;

                &::after {
                    background-position: -24px -336px;
                }

                @media (min-width: $screen-lg-min) {

                    &:hover, &:active, &:focus {
                        padding: 0 72px 0 40px;
                    }
                }
            }
        }

        .remove-like-container {

            .cancel-link {
                font-size: 13px;
                text-transform: uppercase;
                @include custom-font-family('Nunito', 600);
                margin-left: 21px;
                color: $white;
                text-decoration: none;
                margin-bottom: 15px;
                display: block;

                &:hover, &:active, &:focus {
                    text-decoration: underline;
                }
            }

            h4 {
                font-size: 21px;
                line-height: 21px;
                @include custom-font-family('Nunito', 600);
                text-transform: uppercase;
                margin: 0;
            }

            .btn-default {
                padding: 0 58px 0 20px;
                width: 100%;

                &::after {
                    background-position: -72px 0;
                }

                @media (min-width: $screen-lg-min) {

                    &:hover, &:active, &:focus {
                        padding: 0 48px 0 40px;

                        &::after {
                            margin-right: -50px;
                        }
                    }
                }
            }
        }

        .gradient {
            background: $black;
            opacity: 0.8;
            position: absolute;
            top: 0;
            bottom: 0;
            left: 0;
            right: 0;
            z-index: $zindex-light;
            display: none;
        }

        .moodboard-entry {

            &.edit {
                right: 39px;

                &:hover, &:active, &:focus {

                    span {
                        background-position: 0 -312px;

                        &.close-toggle {
                            background-position: -48px -312px;
                        }
                    }
                }
            }
        }

        .icon {
            background: rgba(0, 0, 0, 0.6);
            border-radius: 50%;
            width: 74px;
            height: 74px;
            position: absolute;;
            margin: 0 auto;
            z-index: $zindex-lighter;
            text-align: center;
            left: 50%;
            top: 50%;
            transform: translate(-50%, -50%);
        }
    }
}

@media (max-width: $screen-xs-max) {

    .talisman-likes {

        figure {
            margin: 0 auto;
        }
    }
}
