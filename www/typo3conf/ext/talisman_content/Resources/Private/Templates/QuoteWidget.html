<div id="c{data.uid}" class="quote-widget">
    <div class="row">
        <div class="col-sm-4">
            <f:if condition="{files.0}">
                <f:image image="{files.0}" alt="{files.0.properties.alt}" title="{files.0.properties.title}" class="img-responsive" />
            </f:if>
        </div>
        <div class="col-sm-offset-1 col-sm-7">
            <h2>{data.header -> f:format.nl2br()}</h2>
            <h3>{data.subheader -> f:format.nl2br()}</h3>
            <div class="row">
                <div class="col-md-6">
                    <f:if condition="{brochurePageUid}">
                        <f:link.typolink parameter="{brochurePageUid} _blank" class="btn btn-default btn-black">
                            <f:translate id="quote_widget.request_brochure" extensionName="TalismanContent" />
                        </f:link.typolink>
                    </f:if>
                </div>
                <div class="col-md-6">
                    <f:if condition="{quotePageUid}">
                        <f:link.typolink parameter="{quotePageUid} _blank" class="btn btn-default btn-black">
                            <f:translate id="quote_widget.request_quote" extensionName="TalismanContent" />
                        </f:link.typolink>
                    </f:if>
                </div>
            </div>
        </div>
    </div>
</div>
