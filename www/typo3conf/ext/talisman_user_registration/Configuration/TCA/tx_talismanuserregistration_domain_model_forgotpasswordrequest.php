<?php
if (!defined('TYPO3_MODE')) {
    die ('Access denied.');
}

return [
    'ctrl' => [
        'title' => 'LLL:EXT:talisman_user_registration/Resources/Private/Language/locallang_be.xlf:tx_talismanuserregistration_domain_model_forgotpasswordrequest',
        'label' => 'user',
        'hideAtCopy' => true,
        'tstamp' => 'tstamp',
        'crdate' => 'crdate',
        'cruser_id' => 'cruser_id',
        'editlock' => 'editlock',
        'dividers2tabs' => true,
        'hideTable' => false,
        'delete' => 'deleted',
        'iconfile' => 'EXT:talisman_user_registration/Resources/Public/Icons/tx_talismanuserregistration_domain_model_forgotpasswordrequest.png',
    ],
    'interface' => [
        'showRecordFieldList' => 'user, request_date, hash'
    ],
    'types' => [
        0 => [
            'showitem' => 'user, request_date, hash'
        ]
    ],
    'palettes' => [],
    'columns' => [
        'user' => [
            'exclude' => false,
            'label' => 'LLL:EXT:talisman_user_registration/Resources/Private/Language/locallang_be.xlf:tx_talismanuserregistration_domain_model_forgotpasswordrequest.user',
            'config' => [
                'type' => 'group',
                'internal_type' => 'db',
                'allowed' => 'fe_users',
                'readOnly' => 1
            ],
        ],
        'request_date' => [
            'exclude' => false,
            'label' => 'LLL:EXT:talisman_user_registration/Resources/Private/Language/locallang_be.xlf:tx_talismanuserregistration_domain_model_forgotpasswordrequest.request_date',
            'config' => [
                'type' => 'input',
                'eval' => 'datetime',
                'readOnly' => 1
            ]
        ],
        'hash' => [
            'exclude' => false,
            'label' => 'LLL:EXT:talisman_user_registration/Resources/Private/Language/locallang_be.xlf:tx_talismanuserregistration_domain_model_forgotpasswordrequest.hash',
            'config' => [
                'type' => 'input',
                'readOnly' => 1
            ]
        ],
    ],
];
