<?php
if (!defined('TYPO3_MODE')) {
    die ('Access denied.');
}

\TYPO3\CMS\Extbase\Utility\ExtensionUtility::configurePlugin(
    'Redkiwi.' . $_EXTKEY,
    'SignUp',
    [
        'SignUp' => 'show, handleSignUp, confirmationMailSent, confirm, activate, finished',
        'Facebook' => 'facebookLogin, handleFacebookLogin, facebookError',
        'Google' => 'googleLogin, handleGoogleLogin, googleError'
    ],
    [
        'SignUp' => 'show, handleSignUp, confirmationMailSent, confirm, activate, finished',
        'Facebook' => 'facebookLogin, handleFacebookLogin, facebookError',
        'Google' => 'googleLogin, handleGoogleLogin, googleError'
    ],
    \TYPO3\CMS\Extbase\Utility\ExtensionUtility::PLUGIN_TYPE_CONTENT_ELEMENT
);

\TYPO3\CMS\Extbase\Utility\ExtensionUtility::configurePlugin(
    'Redkiwi.' . $_EXTKEY,
    'ForgotPassword',
    [
        'ForgotPassword' => 'show, handleForm, mailSent, changePassword, updatePassword'
    ],
    [
        'ForgotPassword' => 'show, handleForm, mailSent, changePassword, updatePassword'
    ],
    \TYPO3\CMS\Extbase\Utility\ExtensionUtility::PLUGIN_TYPE_CONTENT_ELEMENT
);
