<?php
namespace Redkiwi\TalismanPage\Domain\Repository;

use Redkiwi\TalismanTemplate\Domain\Repository\BaseRepository;
use TYPO3\CMS\Core\Utility\GeneralUtility;
use TYPO3\CMS\Extbase\Persistence\Exception\InvalidQueryException;
use TYPO3\CMS\Extbase\Persistence\QueryInterface;
use TYPO3\CMS\Extbase\Persistence\QueryResultInterface;

/**
 * Repository: Page
 *
 * @package Redkiwi\TalismanPage\Domain\Repository
 */
class PageRepository extends BaseRepository
{

    /** @var array */
    protected $defaultOrderings = [
        'sorting' => QueryInterface::ORDER_ASCENDING
    ];

    /**
     * @return void
     */
    public function initializeObject()
    {
        $querySettings = GeneralUtility::makeInstance('TYPO3\\CMS\\Extbase\\Persistence\\Generic\\Typo3QuerySettings');

        /** @var \TYPO3\CMS\Extbase\Persistence\Generic\Typo3QuerySettings $querySettings */
        $querySettings->setRespectStoragePage(false);
        $this->setDefaultQuerySettings($querySettings);
    }

    /**
     * @param int $pid
     * @param int $offset
     * @param int $limit
     * @param int $author
     * @param int $country
     * @return array|QueryResultInterface
     */
    public function findByPidAndBlogAuthorAndBlogCountry($pid, $offset, $limit, $author = null, $country = null)
    {
        $query = $this->createQuery();
        $constraints = [];
        $constraints[] = $query->equals('pid', $pid);
        if ($author !== null) {
            $constraints[] = $query->equals('blogAuthor.uid', (int)$author);
        }
        if ($country !== null) {
            $constraints[] = $query->equals('blogCountry.uid', (int)$country);
        }

        return $query
            ->setOffset($offset)
            ->setLimit((int) $limit)
            ->matching($query->logicalAnd($constraints))
            ->execute();
    }

    /**
     * @param int $pid
     * @param int $author
     * @param int $country
     * @return int
     */
    public function countByPidAndBlogAuthorAndBlogCountry($pid, $author = null, $country = null)
    {
        $query = $this->createQuery();
        $constraints = [];
        $constraints[] = $query->equals('pid', $pid);
        if ($author !== null) {
            $constraints[] = $query->equals('blogAuthor.uid', (int)$author);
        }
        if ($country !== null) {
            $constraints[] = $query->equals('blogCountry.uid', (int)$country);
        }

        return $query
            ->matching($query->logicalAnd($constraints))
            ->count();
    }

    /**
     * @param int $type
     * @param int $pid
     * @param boolean $hideInList
     * @param array $categories
     * @param int $offset
     * @param int $limit
     * @return QueryInterface|array
     * @throws InvalidQueryException
     */
    public function findByTypeAndPidAndHideInListAndCategories($type, $pid, $hideInList, array $categories = [], $offset = 0, $limit = 24)
    {
        $query = $this->createQuery();
        $constraints = [];
        $constraints[] = $query->equals('pid', $pid);
        $constraints[] = $query->equals('hideInList', $hideInList);
        $constraints[] = $query->equals('type', $type);
        if (!empty($categories)) {
            array_walk($categories, 'intval');
            $constraints[] = $query->in('categories.uid', $categories);
        }
        return $query
            ->setOffset($offset)
            ->setLimit($limit)
            ->matching($query->logicalAnd($constraints))
            ->execute();
    }

    /**
     * Find all inspirational needed indexes
     *
     * @return array|QueryResultInterface
     */
    public function findInspirationIndex()
    {
        $query = $this->createQuery();
        $constraints = [
            $query->logicalOr([
                $query->equals('includeInInspiration', true),
                $query->equals('includeInSpecials', true),
            ])
        ];

        return $query->matching($query->logicalAnd($constraints))->execute();
    }

    /**
     * @param int $type
     * @param int $pid
     * @param int $hideInList
     * @return int
     */
    public function countByTypePidAndHideInList($type, $pid, $hideInList)
    {
        $query = $this->createQuery();
        $constraints = [];
        $constraints[] = $query->equals('pid', $pid);
        $constraints[] = $query->equals('hideInList', $hideInList);
        $constraints[] = $query->equals('type', $type);
        return $query->matching($query->logicalAnd($constraints))->execute()->count();
    }

    /**
     * @param int $type
     * @param int $pid
     * @param int $hideInList
     * @param array $categories
     * @return int
     * @throws InvalidQueryException
     */
    public function countByTypeAndPidAndHideInListAndCategories($type, $pid, $hideInList, array $categories = [])
    {
        $query = $this->createQuery();
        $constraints = [];
        $constraints[] = $query->equals('pid', $pid);
        $constraints[] = $query->equals('hideInList', $hideInList);
        $constraints[] = $query->equals('type', $type);
        if (!empty($categories)) {
            array_walk($categories, 'intval');
            $constraints[] = $query->in('categories.uid', $categories);
        }
        return $query->matching($query->logicalAnd($constraints))->execute()->count();
    }

    /**
     * @param int $pid
     * @param int $offset
     * @param int $limit
     * @return array|QueryResultInterface
     */
    public function findByPid($pid, $offset = 0, $limit = 10) {
        $query = $this->createQuery();
        $constraints = [];
        $constraints[] = $query->equals('pid', $pid);

        return $query
            ->setOffset($offset)
            ->setLimit((int) $limit)
            ->matching($query->logicalAnd($constraints))
            ->execute();
    }

    /**
     * @param int $pid
     * @param int $limit
     * @return array|QueryResultInterface
     */
    public function findPopularByPid($pid, $limit = 10) {
        $query = $this->createQuery();
        $constraints = [];
        $constraints[] = $query->equals('pid', $pid);

        return $query
            ->setLimit((int) $limit)
            ->matching($query->logicalAnd($constraints))
            ->execute();
    }

    /**
     * @return array|QueryResultInterface
     */
    public function findPagesForDestinationsMap() {
        $query = $this->createQuery();
        $constraints = [];
        $constraints[] = $query->logicalNot($query->equals('isoCode', ''));
        $query->setOrderings(['sorting' => QueryInterface::ORDER_ASCENDING]);
        return $query->matching($query->logicalAnd($constraints))->execute();
    }

    /**
     * @param array $uidList
     * @return array|QueryResultInterface
     * @throws InvalidQueryException
     */
    public function findByUidList(array $uidList)
    {
        $query = $this->createQuery();
        $constraints = [];
        $constraints[] = $query->in('uid', $uidList);
        return $query->matching($query->logicalAnd($constraints))->execute();
    }

    /**
     * @param $pageId
     * @return mixed[]
     */
    public function findPagesWithMarkerLocations($pageId)
    {
        /** @var QueryBuilder $queryBuilder */
        $queryBuilder = $this->getQueryBuilderForTable('pages');

        return $queryBuilder
            ->select(
                'p.uid',
                'p.abstract',
                'p.tx_talismanesbconnector_latitude',
                'p.tx_talismanesbconnector_longitude',
                'sf.identifier',
                'sf.storage',
                'sfr.title',
                'sfr.description',
                'sfr.uid as image_reference_uid'
            )
            ->from('pages', 'p')
            ->leftJoin('p', 'sys_file_reference', 'sfr', 'sfr.uid_foreign = p.uid')
            ->leftJoin('sfr', 'sys_file', 'sf', 'sf.uid = sfr.uid_local')
            ->where(
                $queryBuilder->expr()->eq('p.pid', $pageId),
                $queryBuilder->expr()->neq('p.tx_talismanesbconnector_latitude', '0.0000000'),
                $queryBuilder->expr()->neq('p.tx_talismanesbconnector_longitude', '0.0000000'),
                $queryBuilder->expr()->eq('sfr.deleted', 0),
                $queryBuilder->expr()->eq('sfr.tablenames', $queryBuilder->createNamedParameter('pages', \PDO::PARAM_STR)
                ))
            ->execute()->fetchAll();
    }
}
