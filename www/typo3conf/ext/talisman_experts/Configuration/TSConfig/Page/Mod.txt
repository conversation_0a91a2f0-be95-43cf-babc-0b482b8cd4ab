mod.wizards.newContentElement.wizardItems.plugins {
    elements {
        talismanexperts_widget {
            icon = EXT:talisman_experts/Resources/Public/Icons/widget.jpg
            title = LLL:EXT:talisman_experts/Resources/Private/Language/locallang_be.xlf:plugin.widget.title
            description = LLL:EXT:talisman_experts/Resources/Private/Language/locallang_be.xlf:plugin.widget.description
            tt_content_defValues {
                CType = talismanexperts_widget
            }
        }
        talismanexperts_list {
            icon = EXT:talisman_experts/Resources/Public/Icons/list.jpg
            title = LLL:EXT:talisman_experts/Resources/Private/Language/locallang_be.xlf:plugin.list.title
            description = LLL:EXT:talisman_experts/Resources/Private/Language/locallang_be.xlf:plugin.list.description
            tt_content_defValues {
                CType = talismanexperts_list
            }
        }
        talismanexperts_slider {
            icon = EXT:talisman_experts/Resources/Public/Icons/slider.jpg
            title = LLL:EXT:talisman_experts/Resources/Private/Language/locallang_be.xlf:plugin.slider.title
            description = LLL:EXT:talisman_experts/Resources/Private/Language/locallang_be.xlf:plugin.slider.description
            tt_content_defValues {
                CType = talismanexperts_slider
            }
        }
    }
}
