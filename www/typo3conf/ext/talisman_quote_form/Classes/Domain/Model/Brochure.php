<?php
namespace Redkiwi\TalismanQuoteForm\Domain\Model;

use Redkiwi\TalismanQuoteForm\Utility\EsbUtility;
use TYPO3\CMS\Extbase\Utility\LocalizationUtility;

/**
 * Model: Brochure
 *
 * @package Redkiwi\TalismanQuoteForm\Domain\Model
 */
class Brochure extends \TYPO3\CMS\Extbase\DomainObject\AbstractEntity
{
    /** @var string */
    protected $additionalInformation;

    /** @var \Redkiwi\TalismanPage\Domain\Model\Page */
    protected $destinationOne;

    /** @var \Redkiwi\TalismanPage\Domain\Model\Page */
    protected $destinationTwo;

    /** @var \Redkiwi\TalismanPage\Domain\Model\Page */
    protected $destinationThree;

    /** @var boolean */
    protected $unsure;

    /** @var string */
    protected $themes;

    /** @var string */
    protected $title;

    /** @var string */
    protected $initials;

    /** @var string */
    protected $middleName;

    /** @var string */
    protected $lastName;

    /** @var string */
    protected $email;

    /** @var string */
    protected $phone;

    /** @var string */
    protected $zip;

    /** @var string */
    protected $houseNumber;

    /** @var string */
    protected $houseNumberAddendum;

    /** @var string */
    protected $street;

    /** @var string */
    protected $city;

    /** @var \Redkiwi\TalismanEsbConnector\Domain\Model\Country */
    protected $country;

    /** @var bool */
    protected $brochurePerPost;

    /** @var bool */
    protected $newsletter;

    /** @var bool */
    protected $magazine;

    /** @var string */
    protected $gclid;

    /** @var string */
    protected $utmSource;

    /** @var string */
    protected $utmMedium;

    /** @var string */
    protected $utmCampaign;

    /** @var boolean */
    protected $queueForEsb;

    /** @var boolean */
    protected $sentToEsb;

    /** @var string */
    protected $heardOfTalismanAnswer;

    /** @var string */
    protected $heardOfTalismanOtherAnswer;

    /** @var bool */
    protected $travelledBefore;

    /**
     * @return string
     */
    public function getAdditionalInformation()
    {
        return $this->additionalInformation;
    }

    /**
     * @param string $additionalInformation
     * @return void
     */
    public function setAdditionalInformation($additionalInformation)
    {
        $this->additionalInformation = $additionalInformation;
    }

    /**
     * @return \Redkiwi\TalismanPage\Domain\Model\Page
     */
    public function getDestinationOne()
    {
        return $this->destinationOne;
    }

    /**
     * @param \Redkiwi\TalismanPage\Domain\Model\Page $destinationOne
     * @return void
     */
    public function setDestinationOne(\Redkiwi\TalismanPage\Domain\Model\Page $destinationOne = null)
    {
        $this->destinationOne = $destinationOne;
    }

    /**
     * @return \Redkiwi\TalismanPage\Domain\Model\Page
     */
    public function getDestinationTwo()
    {
        return $this->destinationTwo;
    }

    /**
     * @param \Redkiwi\TalismanPage\Domain\Model\Page $destinationTwo
     * @return void
     */
    public function setDestinationTwo(\Redkiwi\TalismanPage\Domain\Model\Page $destinationTwo = null)
    {
        $this->destinationTwo = $destinationTwo;
    }

    /**
     * @return \Redkiwi\TalismanPage\Domain\Model\Page
     */
    public function getDestinationThree()
    {
        return $this->destinationThree;
    }

    /**
     * @param \Redkiwi\TalismanPage\Domain\Model\Page $destinationThree
     * @return void
     */
    public function setDestinationThree(\Redkiwi\TalismanPage\Domain\Model\Page $destinationThree = null)
    {
        $this->destinationThree = $destinationThree;
    }

    /**
     * @return string
     */
    public function getTitle()
    {
        return $this->title;
    }

    /**
     * @param string $title
     * @return void
     */
    public function setTitle($title)
    {
        $this->title = $title;
    }

    /**
     * @return string
     */
    public function getInitials()
    {
        return $this->initials;
    }

    /**
     * @param string $initials
     * @return void
     */
    public function setInitials($initials)
    {
        $this->initials = $initials;
    }

    /**
     * @return string
     */
    public function getMiddleName()
    {
        return $this->middleName;
    }

    /**
     * @param string $middleName
     * @return void
     */
    public function setMiddleName($middleName)
    {
        $this->middleName = $middleName;
    }

    /**
     * @return string
     */
    public function getLastName()
    {
        return $this->lastName;
    }

    /**
     * @param string $lastName
     * @return void
     */
    public function setLastName($lastName)
    {
        $this->lastName = $lastName;
    }

    /**
     * @return string
     */
    public function getEmail()
    {
        return $this->email;
    }

    /**
     * @param string $email
     * @return void
     */
    public function setEmail($email)
    {
        $this->email = $email;
    }

    /**
     * @return string
     */
    public function getPhone()
    {
        return $this->phone;
    }

    /**
     * @param string $phone
     * @return void
     */
    public function setPhone($phone)
    {
        $this->phone = $phone;
    }

    /**
     * @return string
     */
    public function getZip()
    {
        if (strlen($this->zip) === 6) {
            return substr($this->zip, 0, 4) . ' ' . substr($this->zip, 4, 2);
        }
        return $this->zip;
    }

    /**
     * @param string $zip
     * @return void
     */
    public function setZip($zip)
    {
        $this->zip = $zip;
    }

    /**
     * @return string
     */
    public function getHouseNumber()
    {
        return $this->houseNumber;
    }

    /**
     * @param string $houseNumber
     * @return void
     */
    public function setHouseNumber($houseNumber)
    {
        $this->houseNumber = $houseNumber;
    }

    /**
     * @return string
     */
    public function getStreet()
    {
        return $this->street;
    }

    /**
     * @param string $street
     * @return void
     */
    public function setStreet($street)
    {
        $this->street = $street;
    }

    /**
     * @return string
     */
    public function getCity()
    {
        return $this->city;
    }

    /**
     * @param string $city
     * @return void
     */
    public function setCity($city)
    {
        $this->city = $city;
    }

    /**
     * @return \Redkiwi\TalismanEsbConnector\Domain\Model\Country
     */
    public function getCountry()
    {
        return $this->country;
    }

    /**
     * @param \Redkiwi\TalismanEsbConnector\Domain\Model\Country $country
     * @return void
     */
    public function setCountry(\Redkiwi\TalismanEsbConnector\Domain\Model\Country $country)
    {
        $this->country = $country;
    }

    /**
     * @return boolean
     */
    public function isBrochurePerPost()
    {
        return $this->brochurePerPost;
    }

    /**
     * @param boolean $brochurePerPost
     * @return void
     */
    public function setBrochurePerPost($brochurePerPost)
    {
        $this->brochurePerPost = $brochurePerPost;
    }

    /**
     * @return bool
     */
    public function getNewsletter()
    {
        return $this->newsletter;
    }

    /**
     * @param bool $newsletter
     * @return void
     */
    public function setNewsletter($newsletter)
    {
        $this->newsletter = $newsletter;
    }

    /**
     * @return bool
     */
    public function getMagazine()
    {
        return $this->magazine;
    }

    /**
     * @param bool $magazine
     * @return void
     */
    public function setMagazine($magazine)
    {
        $this->magazine = $magazine;
    }

    /**
     * @return string
     */
    public function getGclid()
    {
        return $this->gclid;
    }

    /**
     * @param string $gclid
     * @return void
     */
    public function setGclid($gclid)
    {
        $this->gclid = $gclid;
    }

    /**
     * @return string
     */
    public function getThemes()
    {
        return $this->themes;
    }

    /**
     * @param string $themes
     * @return void
     */
    public function setThemes($themes)
    {
        $this->themes = $themes;
    }

    /**
     * @return string
     */
    public function getHouseNumberAddendum()
    {
        return $this->houseNumberAddendum;
    }

    /**
     * @param string $houseNumberAddendum
     * @return void
     */
    public function setHouseNumberAddendum($houseNumberAddendum)
    {
        $this->houseNumberAddendum = $houseNumberAddendum;
    }

    /**
     * @return string
     */
    public function getUtmSource()
    {
        return $this->utmSource;
    }

    /**
     * @param string $utmSource
     * @return void
     */
    public function setUtmSource($utmSource)
    {
        $this->utmSource = $utmSource;
    }

    /**
     * @return string
     */
    public function getUtmMedium()
    {
        return $this->utmMedium;
    }

    /**
     * @param string $utmMedium
     * @return void
     */
    public function setUtmMedium($utmMedium)
    {
        $this->utmMedium = $utmMedium;
    }

    /**
     * @return string
     */
    public function getUtmCampaign()
    {
        return $this->utmCampaign;
    }

    /**
     * @param string $utmCampaign
     * @return void
     */
    public function setUtmCampaign($utmCampaign)
    {
        $this->utmCampaign = $utmCampaign;
    }

    /**
     * @return boolean
     */
    public function isQueueForEsb()
    {
        return $this->queueForEsb;
    }

    /**
     * @param boolean $queueForEsb
     * @return void
     */
    public function setQueueForEsb($queueForEsb)
    {
        $this->queueForEsb = $queueForEsb;
    }

    /**
     * @return boolean
     */
    public function isSentToEsb()
    {
        return $this->sentToEsb;
    }

    /**
     * @param boolean $sentToEsb
     * @return void
     */
    public function setSentToEsb($sentToEsb)
    {
        $this->sentToEsb = $sentToEsb;
    }

    /**
     * @return array
     */
    public function getComponentIds()
    {
        $idList = [];
        if ($this->destinationOne !== null) {
            $idList[] = $this->destinationOne->getComponentId();
        }
        if ($this->destinationTwo !== null) {
            $idList[] = $this->destinationTwo->getComponentId();
        }
        if ($this->destinationThree !== null) {
            $idList[] = $this->destinationThree->getComponentId();
        }
        return $idList;
    }

    /**
     * @return array
     */
    public function getDestinationTitles()
    {
        $titleList = [];
        if ($this->destinationOne !== null) {
            $titleList[] = $this->destinationOne->getTitle();
        }
        if ($this->destinationTwo !== null) {
            $titleList[] = $this->destinationTwo->getTitle();
        }
        return $titleList;
    }

    /**
     * @return string
     */
    public function getTravelStudioGender()
    {
        if ($this->title === 'Dhr.') {
            return 'male';
        }
        return 'female';
    }

    /**
     * @return string
     */
    public function getNoteText()
    {
        $lines = [];
        $lines[] = LocalizationUtility::translate('quote_form.destinations', 'TalismanQuoteForm') . ': ' . implode(', ', $this->getDestinationTitles());
        $lines[] = LocalizationUtility::translate('quote_form.details', 'TalismanQuoteForm') . ': ' . $this->getTitle() . ' ' . $this->getInitials() . ' ' . $this->getMiddleName() . ' ' . $this->getLastName();
        $lines[] = LocalizationUtility::translate('quote_form.email', 'TalismanQuoteForm') . ': ' . $this->getEmail();
        $lines[] = LocalizationUtility::translate('quote_form.your_question', 'TalismanQuoteForm') . ': ' . $this->getAdditionalInformation();
        $lines[] = LocalizationUtility::translate('quote_form.heard_of_talisman', 'TalismanQuoteForm') . ': ' . $this->getHeardOfTalismanAnswer() . ' ' . $this->getHeardOfTalismanOtherAnswer();
        $lines[] = LocalizationUtility::translate('brochure_form.travelled_before', 'TalismanQuoteForm') . ': ' . ($this->isTravelledBefore() ? 'ja' : 'nee');
        $lines[] = LocalizationUtility::translate('quote_form.newsletter', 'TalismanQuoteForm') . ': ' . ($this->getNewsletter() ? 'ja' : 'nee');
        $lines[] = 'GCLID: ' . $this->getGclid();
        $lines[] = 'UTM Source: ' . $this->getUtmSource();
        $lines[] = 'UTM Medium: ' . $this->getUtmMedium();
        $lines[] = 'UTM Campaign: ' . $this->getUtmCampaign();
        return implode("\n", $lines);
    }

    /**
     * @return string
     */
    public function getHeardOfTalismanAnswer()
    {
        return $this->heardOfTalismanAnswer;
    }

    /**
     * @param string $heardOfTalismanAnswer
     * @return void
     */
    public function setHeardOfTalismanAnswer($heardOfTalismanAnswer)
    {
        $this->heardOfTalismanAnswer = $heardOfTalismanAnswer;
    }

    /**
     * @return string
     */
    public function getHeardOfTalismanOtherAnswer()
    {
        return $this->heardOfTalismanOtherAnswer;
    }

    /**
     * @param string $heardOfTalismanOtherAnswer
     * @return void
     */
    public function setHeardOfTalismanOtherAnswer($heardOfTalismanOtherAnswer)
    {
        $this->heardOfTalismanOtherAnswer = $heardOfTalismanOtherAnswer;
    }

    /**
     * @return boolean
     */
    public function isTravelledBefore()
    {
        return $this->travelledBefore;
    }

    /**
     * @param boolean $travelledBefore
     * @return void
     */
    public function setTravelledBefore($travelledBefore)
    {
        $this->travelledBefore = $travelledBefore;
    }

    /**
     * @return bool
     */
    public function isUnsure()
    {
        return $this->unsure;
    }

    /**
     * @param bool $unsure
     * @return void
     */
    public function setUnsure($unsure)
    {
        $this->unsure = $unsure;
    }

    /**
     * @return string
     */
    public function getSourceTypeName()
    {
        return EsbUtility::getSourceTypeName($this->gclid, $this->utmSource, $this->heardOfTalismanAnswer);
    }
}
