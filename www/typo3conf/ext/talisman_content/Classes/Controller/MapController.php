<?php
namespace Redkiwi\TalismanContent\Controller;

use Redkiwi\TalismanContent\Service\DestinationService;
use Redkiwi\TalismanPage\Domain\Repository\PageRepository;

/**
 * Controller: Map
 *
 * @package Redkiwi\TalismanDestinations\Controller
 */
class MapController extends \TYPO3\CMS\Extbase\Mvc\Controller\ActionController
{
    /**
     * @var \Redkiwi\TalismanContent\Service\DestinationService
     */
    protected $destinationService;

    /**
     * @var \Redkiwi\TalismanPage\Domain\Repository\PageRepository
     */
    protected $pageRepository;

    public function __construct(DestinationService $destinationService, PageRepository $pageRepository)
    {
        parent::__construct();
        $this->destinationService = $destinationService;
        $this->pageRepository = $pageRepository;
    }

    /**
     * @return void
     */
    public function showAction()
    {
        $destinations = $this->pageRepository->findPagesForDestinationsMap()->toArray();

        $isoLinkMap = [];
        $destinationsGeoChart = [];

        /** @var \Redkiwi\TalismanPage\Domain\Model\Page $destination */
        foreach ($destinations as $destination) {
            $isoLinkMap[$destination->getIsoCode()] = $this->uriBuilder
                ->reset()
                ->setTargetPageUid($destination->getUid())
                ->setCreateAbsoluteUri(true)
                ->build();

            $destinationsGeoChart[] = [
                'title' => $destination->getTitle(),
                'isoCode' => $destination->getIsoCode()
            ];
        }

        $this->view->assignMultiple([
            'isoLinkMap' => json_encode($isoLinkMap),
            'javaScriptData' => json_encode($this->destinationService->renderJavaScriptDataArray($destinationsGeoChart))
        ]);
    }
}
