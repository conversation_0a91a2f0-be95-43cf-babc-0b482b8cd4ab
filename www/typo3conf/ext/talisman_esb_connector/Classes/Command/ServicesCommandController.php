<?php
namespace Redkiwi\TalismanEsbConnector\Command;

use Redkiwi\TalismanEsbConnector\Utility\ConfigurationUtility;

/**
 * Command: Services
 *
 * @package Redkiwi\TalismanEsbConnector\Command
 */
class ServicesCommandController extends \TYPO3\CMS\Extbase\Mvc\Controller\CommandController
{
    /**
     * @var \Redkiwi\TalismanEsbConnector\Service\Esb\ServicesService
     * @inject
     */
    protected $esbServicesService;

    /**
     * @var \Redkiwi\TalismanEsbConnector\Service\TYPO3\ServicesService
     * @inject
     */
    protected $typo3ServicesService;

    /**
     * @return bool
     * @throws \Exception
     */
    public function syncAccommodationsCommand()
    {
        return $this->getServices('accommodation');
    }

    /**
     * @return bool
     * @throws \Exception
     */
    public function syncExcursionsCommand()
    {
        return $this->getServices('excursion');
    }

    /**
     * @return bool
     * @throws \Exception
     */
    public function syncToursCommand()
    {
        return $this->getServices('tour');
    }

    /**
     * @param string $type
     * @return bool
     * @throws \Exception
     */
    protected function getServices($type)
    {
        if (ConfigurationUtility::isValid()) {
            $services = $this->esbServicesService->makeRequest(['type' => $type]);
            $this->typo3ServicesService->syncServices($services, $type);
            return true;
        } else {
            throw new \Exception('Configuration invalid. Check Extension Manager');
        }
    }
}
