<?php
error_reporting(E_ALL);
ini_set('display_errors', 1);
require_once 'vendor/autoload.php';

use Dompdf\Dompdf;

// test order data
$order = [
    'number' => '#958201',
    'billing_address' => [
        'companyname' => null,
        'name' => '<PERSON>',
        'street' => 'Daltonstraat',
        'housenumber' => '65',
        'address_line_2' => '',
        'zipcode' => '3316GD',
        'city' => 'Dordrecht',
        'country' => 'NL',
        'email' => '<EMAIL>',
        'phone' => '0101234567',
    ],
    'delivery_address' => [
        'companyname' => '',
        'name' => '<PERSON>',
        'street' => 'Daltonstraat',
        'housenumber' => '65',
        'address_line_2' => '',
        'zipcode' => '3316GD',
        'city' => 'Dordrecht',
        'country' => 'NL',
    ],
    'items' => [
        [
            'qty' => 2,
            'name' => 'Jeans - Black - 36',
            'sku' => 69205,
            'ean' => '8710552295268',
        ],
        [
            'qty' => 1,
            'name' => 'Sjaal - Rood Oranje',
            'sku' => 25920,
            'ean' => '3059943009097',
        ]
    ]
];

// API credentials
$user = '<EMAIL>';
$password = '4QJW9yh94PbTcpJGdKz6egwH';

$companyId = '9e606e6b-44a4-4a4e-a309-cc70ddd3a103';
$brandId = 'e41c8d26-bdfd-4999-9086-e5939d67ae28';

$productCombinationId = 3;

/**
 * @param $endpoint
 * @param string $method
 * @param $payload
 * @return array|bool|string
 * Sends HTTP requests to the API and returns the response
 */
function call_qls_api($endpoint, string $method = 'GET', $payload = null)
{
    global $user, $password;

    $curl = curl_init();
    curl_setopt($curl, CURLOPT_URL, 'https://api.pakketdienstqls.nl' . $endpoint);
    curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($curl, CURLOPT_HTTPAUTH, CURLAUTH_BASIC);
    curl_setopt($curl, CURLOPT_USERPWD, $user . ':' . $password);
    curl_setopt($curl, CURLOPT_TIMEOUT, 30);

    if ($method === 'POST' && $payload) {
        curl_setopt($curl, CURLOPT_POST, true);
        curl_setopt($curl, CURLOPT_HTTPHEADER, array('Content-Type: application/json'));
        curl_setopt($curl, CURLOPT_POSTFIELDS, json_encode($payload));
    }

    $response = curl_exec($curl);
    $http_code = curl_getinfo($curl, CURLINFO_HTTP_CODE);
    $redirect_url = curl_getinfo($curl, CURLINFO_REDIRECT_URL);
    curl_close($curl);

    // basic error handling
    if ($response === false || $http_code >= 400) {
        throw new RuntimeException("API call failed. HTTP code: $http_code, Response: " . ($response ?: 'false'));
    }

    // for 302 responses, return the redirect URL so we can extract shipment ID
    if ($http_code === 302 && $redirect_url) {
        return ['redirect_url' => $redirect_url];
    }

    return $response;
}


/**
 * @return array|mixed
 * Creating a new shipment via the API
 */
function create_shipment()
{
    global $order, $companyId, $brandId, $productCombinationId;

    // build the shipment information for the API
    $shipment_data = array(
        'product_id' => 2,
        'product_combination_id' => $productCombinationId,
        'brand_id' => $brandId,
        'reference' => $order['number'],
        'weight' => 550,
        'cod_amount' => 0,
        'receiver_contact' => array(
            'name' => $order['delivery_address']['name'],
            'companyname' => $order['delivery_address']['companyname'] ?: '',
            'street' => $order['delivery_address']['street'],
            'housenumber' => $order['delivery_address']['housenumber'],
            'postalcode' => $order['delivery_address']['zipcode'],
            'locality' => $order['delivery_address']['city'],
            'country' => $order['delivery_address']['country'],
            'email' => $order['billing_address']['email'],
        )
    );

    try {
        $response = call_qls_api("/companies/$companyId/shipments", 'POST', $shipment_data);
    } catch (Exception $e) {
        throw new RuntimeException("QLS shipment creation failed: " . $e->getMessage());
    }

    // check if we got a successful redirect (302)
    if (isset($response['redirect_url'])) {
        // extract shipment ID from redirect URL
        $url_parts = explode('/', $response['redirect_url']);
        $shipment_id = end($url_parts);
        error_log("Extracted shipment ID from redirect URL: " . $shipment_id);
        error_log("Full redirect URL: " . $response['redirect_url']);
        return ['id' => $shipment_id];
    }

    $result = json_decode($response, true);

    // something went wrong with the response
    if (isset($result['errors']) || !isset($result['data']['id'])) {
        throw new RuntimeException("QLS shipment creation failed");
    }

    return $result['data'];
}

/**
 * @param $shipment_id
 * @return array|mixed
 * Retrieves shipment data from the API
 */
function get_shipment_data($shipment_id)
{
    global $companyId;

    // probeer verschillende endpoints om shipment data op te halen
    $endpoints = [
        "/companies/$companyId/shipments/$shipment_id",
        "/companies/$companyId/shipments/$shipment_id/label"
    ];

    foreach ($endpoints as $endpoint) {
        try {
            $response = call_qls_api($endpoint);
            $result = json_decode($response, true);

            if ($result && isset($result['data'])) {
                return $result['data'];
            }
        } catch (Exception $e) {
            continue; // probeer volgende endpoint
        }
    }

    throw new RuntimeException("Could not retrieve shipment data from QLS API");
}



// Convert HTML label to image using GD library
function convert_label_to_image($shipment_data, $shipment_id)
{
    // Create image with GD
    $width = 400;
    $height = 300;
    $image = imagecreate($width, $height);

    // Colors
    $white = imagecolorallocate($image, 255, 255, 255);
    $black = imagecolorallocate($image, 0, 0, 0);
    $gray = imagecolorallocate($image, 200, 200, 200);

    // Fill background
    imagefill($image, 0, 0, $white);

    // Draw border
    imagerectangle($image, 5, 5, $width-6, $height-6, $black);

    // Add text
    $font_size = 3;
    $y_pos = 20;

    // Header
    $product_name = $shipment_data['product']['name'] ?? 'DHL Pakje';
    imagestring($image, 5, 20, $y_pos, $product_name, $black);
    $y_pos += 30;

    // Reference
    $reference = $shipment_data['reference'] ?? '';
    imagestring($image, $font_size, 20, $y_pos, "Referentie: " . $reference, $black);
    $y_pos += 20;

    // Shipment ID
    imagestring($image, $font_size, 20, $y_pos, "Zending ID: " . $shipment_id, $black);
    $y_pos += 30;

    // Address
    $receiver = $shipment_data['receiver_contact'] ?? [];
    imagestring($image, $font_size, 20, $y_pos, "Bezorgen aan:", $black);
    $y_pos += 20;

    if (!empty($receiver)) {
        imagestring($image, $font_size, 20, $y_pos, $receiver['name'] ?? '', $black);
        $y_pos += 15;

        $address = ($receiver['street'] ?? '') . ' ' . ($receiver['housenumber'] ?? '');
        imagestring($image, $font_size, 20, $y_pos, $address, $black);
        $y_pos += 15;

        $city = ($receiver['postalcode'] ?? '') . ' ' . ($receiver['locality'] ?? '');
        imagestring($image, $font_size, 20, $y_pos, $city, $black);
        $y_pos += 30;
    }

    // Barcode area
    $barcode = $shipment_data['barcode'] ?? '';
    imagefilledrectangle($image, 20, $y_pos, $width-20, $y_pos+40, $gray);
    imagestring($image, 4, 30, $y_pos+5, $barcode, $black);
    imagestring($image, 2, 30, $y_pos+25, "Track & Trace: " . $barcode, $black);

    // Convert to base64
    ob_start();
    imagepng($image);
    $image_data = ob_get_contents();
    ob_end_clean();

    imagedestroy($image);

    return base64_encode($image_data);
}


/**
 * @param $shipment_id
 * @return string
 * Generates HTML for the shipping label
 */
function generate_label_html($shipment_id): string
{
    try {
        // Stap 1: Haal verzendlabel data op van QLS API
        $shipment_data = get_shipment_data($shipment_id);

        // Stap 2: Converteer naar afbeelding (PDF-naar-afbeelding conversie)
        $label_image_base64 = convert_label_to_image($shipment_data, $shipment_id);

        if ($label_image_base64) {
            // Stap 3: Embed afbeelding in HTML voor combinatie met pakbon
            return '<div class="shipping-label-container">
                        <img src="data:image/png;base64,' . $label_image_base64 . '"
                             alt="QLS Verzendlabel" class="label-image" />
                    </div>';
        }
    } catch (Exception $e) {
        throw new RuntimeException("Error generating shipping label: " . $e->getMessage());
    }

    return '<div class="label-error">Kon verzendlabel niet genereren.</div>';
}

/**
 * @param $billing
 * @param $delivery
 * @return string
 * Builds the address section of the PDF
 */
function build_address_section($billing, $delivery): string
{
    return '<div class="address-container">
        <div class="addr-left">
            <div class="address-box">
                <h3>Factuuradres</h3>
                ' . htmlspecialchars($billing['name']) . '<br>
                ' . htmlspecialchars($billing['street'] . ' ' . $billing['housenumber']) . '<br>
                ' . htmlspecialchars($billing['zipcode'] . ' ' . $billing['city']) . '<br>
                ' . htmlspecialchars($billing['email']) . '
            </div>
        </div>
        <div class="addr-right">
            <div class="address-box">
                <h3>Bezorgadres</h3>
                ' . htmlspecialchars($delivery['name']) . '<br>
                ' . htmlspecialchars($delivery['street'] . ' ' . $delivery['housenumber']) . '<br>
                ' . htmlspecialchars($delivery['zipcode'] . ' ' . $delivery['city']) . '
            </div>
        </div>
    </div>';
}

/**
 * @param $items
 * @return string
 * Builds the item table for the PDF
 */
function build_items_table($items): string
{
    $table = '<table class="items-table">
        <thead>
            <tr>
                <th>Aantal</th>
                <th>Artikel</th>
                <th>SKU</th>
                <th>EAN</th>
            </tr>
        </thead>
        <tbody>';

    foreach ($items as $item) {
        $table .= '<tr>
            <td>' . $item['qty'] . '</td>
            <td>' . htmlspecialchars($item['name']) . '</td>
            <td>' . htmlspecialchars($item['sku']) . '</td>
            <td>' . htmlspecialchars($item['ean']) . '</td>
        </tr>';
    }

    $table .= '</tbody></table>';
    return $table;
}

/**
 * @param $label_html
 * @return string|null
 * Builds the PDF document from the HTML content
 */
function build_pdf_document($label_html): ?string
{
    global $order;
    $billing = $order['billing_address'];
    $delivery = $order['delivery_address'];
    $order_num = $order['number'];

    $css_content = file_get_contents('style.css');

    $html_content = '<!DOCTYPE html>
<html lang="nl">
<head>
    <meta charset="UTF-8">
    <title>QLS Verzendlabel Generator</title>
    <style>' . $css_content . '</style>
</head>
<body>
    <div class="document-header">
        <h1>PAKBON - ' . htmlspecialchars($order_num) . '</h1>
    </div>

    ' . build_address_section($billing, $delivery) . '

    ' . build_items_table($order['items']) . '

    <div class="shipping-label">
        <h3>Verzendlabel</h3>
        ' . $label_html . '
    </div>
</body>
</html>';

    $dompdf = new Dompdf();
    $dompdf->loadHtml($html_content);
    $dompdf->setPaper('A4');
    $dompdf->render();

    return $dompdf->output();
}

// checks if the form was submitted or not
$error_message = '';
if ($_POST) {
    error_log("Form submitted with POST data: " . print_r($_POST, true));
    try {
        error_log("Attempting to create shipment...");
        $shipment_result = create_shipment();
        error_log("Shipment result: " . print_r($shipment_result, true));

        if ($shipment_result && isset($shipment_result['id'])) {
            error_log("Generating label for shipment ID: " . $shipment_result['id']);
            $label_html = generate_label_html($shipment_result['id']);
            error_log("Label HTML generated, length: " . strlen($label_html));

            error_log("Building PDF document...");
            $pdf_content = build_pdf_document($label_html);
            error_log("PDF content generated, length: " . strlen($pdf_content));

            // create filename
            $orderRef = str_replace('#', '', $order['number']);
            $timestamp = date('Y-m-d_H-i-s');
            $pdf_filename = "pakbon_{$orderRef}_{$timestamp}.pdf";

            // send PDF to browser
            header('Content-Type: application/pdf');
            header('Content-Disposition: attachment; filename="' . $pdf_filename . '"');
            echo $pdf_content;
            exit;
        }
    } catch (Exception $e) {
        $error_message = 'Kon geen verzendlabel aanmaken. Fout: ' . $e->getMessage();
        error_log("Error generating shipping label: " . $e->getMessage());
    }
}
?>
<!doctype html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>QLS Verzendlabel Generator</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
<h1>QLS Verzendlabel Generator</h1>

<div>
    <h2>Bestelling <?= htmlspecialchars($order['number']) ?></h2>

    <div class="order-info">
        <p><strong>Klant:</strong> <?= htmlspecialchars($order['delivery_address']['name']) ?></p>
        <p><strong>Bezorgadres:</strong>
            <?= htmlspecialchars($order['delivery_address']['street'] . ' ' . $order['delivery_address']['housenumber']) ?>
            ,
            <?= htmlspecialchars($order['delivery_address']['zipcode'] . ' ' . $order['delivery_address']['city']) ?>
        </p>
    </div>

    <table>
        <thead>
        <tr>
            <th>Aantal</th>
            <th>Artikel</th>
            <th>SKU</th>
            <th>EAN</th>
        </tr>
        </thead>
        <tbody>
        <?php foreach ($order['items'] as $item): ?>
            <tr>
                <td><?= $item['qty'] ?></td>
                <td><?= htmlspecialchars($item['name']) ?></td>
                <td><?= htmlspecialchars($item['sku']) ?></td>
                <td><?= htmlspecialchars($item['ean']) ?></td>
            </tr>
        <?php endforeach; ?>
        </tbody>
    </table>
</div>

<div>
    <h2>Verzendlabel Genereren</h2>

    <div>
        <p>Verzending via DHL Pakje. Na het klikken op de knop wordt er een PDF gemaakt met daarop de pakbon
            en de verzendlabel.</p>

        <?php if ($error_message): ?>
            <div id="status-message">
                <?= htmlspecialchars($error_message) ?>
            </div>
        <?php endif; ?>

        <form method="post">
            <input type="hidden" name="product_id" value="1">
            <button type="submit">Genereer Pakbon + Label</button>
        </form>
    </div>
</div>
</body>
</html>
