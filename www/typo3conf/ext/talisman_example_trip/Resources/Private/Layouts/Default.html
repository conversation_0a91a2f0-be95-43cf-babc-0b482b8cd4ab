<div class="top-bar">

    <!-- Regular screen top bar -->
    <div class="hidden-xs">
        <div class="carousel-navigator">
            <ol class="carousel-indicators" data-target="#talisman-example-trip">
                <f:for each="{slides}" as="slide" iteration="s">
                    <f:if condition="{slide.visual.uid}">
                        <li data-target="#talisman-example-trip" data-slide-to="{s.index}" class="{f:if(condition: s.isFirst, then: 'active')}">
                            <div class="thumbnail-container">
                                <f:image
                                    image="{slide.visual}"
                                    width="50"
                                    height="30" />
                            </div>
                        </li>
                    </f:if>
                </f:for>
            </ol>
        </div>
        <div class="back-link-container text-right pull-right">
            <f:link.page pageUid="{parentId}" class="back-link btn btn-default btn-gold">
                <f:translate id="example_trip.back" />
            </f:link.page>
        </div>
    </div>

    <!-- Mobile top bar -->
    <div class="visible-xs">
        <div class="container">
            <div class="row">
                <div class="col-xs-4">
                    <ul class="list-inline icon-menu">
                        <f:if condition="{experts.0} !== false">
                            <li>
                                <f:link.typolink parameter="{experts.0.uid}">
                                    <span class="sprite-icon-48 info white"></span>
                                </f:link.typolink>
                            </li>
                        </f:if>
                        <li>
                            <a href="tel:{settings.telephoneNumber}">
                                <span class="sprite-icon-48 phone white"></span>
                            </a>
                        </li>
                    </ul>
                </div>
                <div class="col-xs-8 text-right">
                    <f:link.page pageUid="{parentId}" class="back-link btn btn-default btn-gold">
                        <f:translate id="example_trip.back" />
                    </f:link.page>
                </div>
            </div>
        </div>
    </div>
</div>

<div id="talisman-example-trip" class="carousel slide" data-ride="carousel" data-interval="false" data-wrap="false">

    <f:render section="Content" />

    <a class="left carousel-control hidden-xs hidden" href="#talisman-example-trip" role="button" data-slide="prev">
        <span class="sr-only"><f:translate id="example_trip.previous" /></span>
    </a>

    <a class="right carousel-control hidden-xs" href="#talisman-example-trip" role="button" data-slide="next">
        <span class="sr-only"><f:translate id="example_trip.next" /></span>
    </a>
</div>

<div class="modal fade" id="expert-modal" tabindex="-1" role="dialog">
    <div class="expert-container">
        <f:render partial="Expert" arguments="{_all}" />
    </div>
</div>

<div class="page-content-container"></div>
