<?php


if (!defined('TYPO3_MODE')) {
    die('Access denied.');
}

\TYPO3\CMS\Extbase\Utility\ExtensionUtility::registerPlugin(
    'talisman_experts',
    'Widget',
    'LLL:EXT:talisman_experts/Resources/Private/Language/locallang_be.xlf:plugin.widget.title',
    'EXT:talisman_experts/Resources/Public/Icons/widget.jpg'
);

\TYPO3\CMS\Extbase\Utility\ExtensionUtility::registerPlugin(
    'talisman_experts',
    'List',
    'LLL:EXT:talisman_experts/Resources/Private/Language/locallang_be.xlf:plugin.list.title',
    'EXT:talisman_experts/Resources/Public/Icons/list.jpg'
);

\TYPO3\CMS\Extbase\Utility\ExtensionUtility::registerPlugin(
    'talisman_experts',
    'Slider',
    'LLL:EXT:talisman_experts/Resources/Private/Language/locallang_be.xlf:plugin.slider.title',
    'EXT:talisman_experts/Resources/Public/Icons/slider.jpg'
);

$GLOBALS['TCA']['tt_content']['types']['talismanexperts_widget'] = [
    'showitem' => '
         --palette--;LLL:EXT:frontend/Resources/Private/Language/locallang_ttc.xml:palette.general;general, pi_flexform,
      --div--;LLL:EXT:frontend/Resources/Private/Language/locallang_ttc.xml:tabs.appearance,
         --palette--;LLL:EXT:frontend/Resources/Private/Language/locallang_ttc.xml:palette.frames;frames,
      --div--;LLL:EXT:frontend/Resources/Private/Language/locallang_ttc.xml:tabs.access,
         --palette--;LLL:EXT:frontend/Resources/Private/Language/locallang_ttc.xml:palette.visibility;visibility,
         --palette--;LLL:EXT:frontend/Resources/Private/Language/locallang_ttc.xml:palette.access;access,
      --div--;LLL:EXT:frontend/Resources/Private/Language/locallang_ttc.xml:tabs.extended
'];

$GLOBALS['TCA']['tt_content']['types']['talismanexperts_list'] = [
    'showitem' => '
         --palette--;LLL:EXT:frontend/Resources/Private/Language/locallang_ttc.xml:palette.general;general,
      --div--;LLL:EXT:frontend/Resources/Private/Language/locallang_ttc.xml:tabs.appearance,
         --palette--;LLL:EXT:frontend/Resources/Private/Language/locallang_ttc.xml:palette.frames;frames,
      --div--;LLL:EXT:frontend/Resources/Private/Language/locallang_ttc.xml:tabs.access,
         --palette--;LLL:EXT:frontend/Resources/Private/Language/locallang_ttc.xml:palette.visibility;visibility,
         --palette--;LLL:EXT:frontend/Resources/Private/Language/locallang_ttc.xml:palette.access;access,
      --div--;LLL:EXT:frontend/Resources/Private/Language/locallang_ttc.xml:tabs.extended
'];

$GLOBALS['TCA']['tt_content']['types']['talismanexperts_slider'] = [
    'showitem' => '
         --palette--;LLL:EXT:frontend/Resources/Private/Language/locallang_ttc.xml:palette.general;general,
         pi_flexform, 
         --palette--;LLL:EXT:frontend/Resources/Private/Language/locallang_ttc.xlf:palette.header;simple_header_with_subheader,
      --div--;LLL:EXT:frontend/Resources/Private/Language/locallang_ttc.xml:tabs.appearance,
         --palette--;LLL:EXT:frontend/Resources/Private/Language/locallang_ttc.xml:palette.frames;frames,
      --div--;LLL:EXT:frontend/Resources/Private/Language/locallang_ttc.xml:tabs.access,
         --palette--;LLL:EXT:frontend/Resources/Private/Language/locallang_ttc.xml:palette.visibility;visibility,
         --palette--;LLL:EXT:frontend/Resources/Private/Language/locallang_ttc.xml:palette.access;access,
      --div--;LLL:EXT:frontend/Resources/Private/Language/locallang_ttc.xml:tabs.extended
'];

\TYPO3\CMS\Core\Utility\ExtensionManagementUtility::addPiFlexFormValue(
    '*',
    'FILE:EXT:talisman_experts/Configuration/FlexForm/Widget.xml',
    'talismanexperts_widget'
);

\TYPO3\CMS\Core\Utility\ExtensionManagementUtility::addPiFlexFormValue(
    '*',
    'FILE:EXT:talisman_experts/Configuration/FlexForm/Slider.xml',
    'talismanexperts_slider'
);