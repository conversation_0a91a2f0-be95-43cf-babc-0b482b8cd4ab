<?php
namespace <PERSON><PERSON>wi\TalismanExperts\Controller;

use Red<PERSON><PERSON>\TalismanExperts\Domain\Repository\ExpertRepository;
use TYPO3\CMS\Core\Error\Exception;
use TYPO3\CMS\Core\Utility\GeneralUtility;
use TYPO3\CMS\Frontend\Controller\TypoScriptFrontendController;

/**
 * Controller: Widget
 *
 * @package Redkiwi\TalismanExperts\Controller
 */
class WidgetController extends \TYPO3\CMS\Extbase\Mvc\Controller\ActionController
{
    /** @var \Redkiwi\TalismanExperts\Domain\Repository\ExpertRepository */
    protected $expertRepository;

    /**
     * @param ExpertRepository $expertRepository
     */
    public function __construct(ExpertRepository $expertRepository)
    {
        parent::__construct();
        $this->expertRepository = $expertRepository;
    }

    /**
     * @return void
     * @throws \TYPO3\CMS\Extbase\Persistence\Exception\InvalidQueryException
     */
    public function showAction()
    {
        if (isset($this->settings['data']['experts'])) {
            $experts = $this->expertRepository->findByUidList(GeneralUtility::trimExplode(',', $this->settings['data']['experts']));
        } else {
            $experts = $this->expertRepository->findByPid($this->settings['pages']['storage']['experts']);
        }
        $this->view->assignMultiple([
            'experts' => $experts,
            'contentUid' => $this->configurationManager->getContentObject()->getFieldVal('uid'),
            'renderModal' =>
                (int)$this->getTypoScriptFrontendController()->id === (int) $this->settings['pages']['inspiration'] ||
                (int)$this->getTypoScriptFrontendController()->id === (int) $this->settings['pages']['specials']
        ]);

        $rootLine = $this->getTypoScriptFrontendController()->rootLine;

        if (isset($rootLine[1]['uid']) && $rootLine[1]['uid'] == 9) {
            $this->view->assign('pageId', $this->configurationManager->getContentObject()->getFieldVal('pid'));
        }
    }

    /**
     * @throws Exception
     * @return void
     */
    public function listAction()
    {
        if (!empty($this->settings['pages']['storage']['experts'])) {
            $experts = $this->expertRepository->findByPid($this->settings['pages']['storage']['experts']);
            $this->view->assignMultiple([
                'experts' => $experts,
                'contentUid' => $this->configurationManager->getContentObject()->getFieldVal('uid')
            ]);
        } else {
            throw new Exception('Experts storage page not set.', 1563458557993);
        }
    }

    /**
     * @return TypoScriptFrontendController
     */
    protected function getTypoScriptFrontendController()
    {
        return $GLOBALS['TSFE'];
    }
}
