<?php
namespace Redkiwi\TalismanContent\DataProcessing;

use TYPO3\CMS\Frontend\ContentObject\ContentObjectRenderer;
use TYPO3\CMS\Frontend\ContentObject\DataProcessorInterface;

/**
 * Data processor for quote widget
 *
 * @package Redkiwi\TalismanContent\DataProcessing
 */
class QuoteWidgetProcessor implements DataProcessorInterface
{
    /**
     * @param ContentObjectRenderer $cObj
     * @param array $contentObjectConfiguration
     * @param array $processorConfiguration
     * @param array $processedData
     * @return array
     */
    public function process(
        ContentObjectRenderer $cObj,
        array $contentObjectConfiguration,
        array $processorConfiguration,
        array $processedData
    )
    {
        if (isset($processorConfiguration['settings.']['pages.']['quote'])) {
            $processedData['quotePageUid'] = (int) $processorConfiguration['settings.']['pages.']['quote'];
        }
        if (isset($processorConfiguration['settings.']['pages.']['brochure'])) {
            $processedData['brochurePageUid'] = (int) $processorConfiguration['settings.']['pages.']['brochure'];
        }
        return $processedData;
    }
}
