<?php
namespace Red<PERSON>wi\TalismanInspiration\Command;

use ApacheSolrForTypo3\Solr\Access\Rootline;
use ApacheSolrForTypo3\Solr\Domain\Search\ApacheSolrDocument\Builder;
use ApacheSolrForTypo3\Solr\System\Solr\Service\SolrWriteService;
use Redkiwi\TalismanInspiration\Usage\InspirationSolr;
use Redkiwi\TalismanPage\Domain\Model\Page;
use Redkiwi\TalismanPage\Domain\Repository\PageRepository;
use TYPO3\CMS\Core\Database\ConnectionPool;
use TYPO3\CMS\Core\Database\DatabaseConnection;
use TYPO3\CMS\Core\Database\Query\QueryBuilder;
use TYPO3\CMS\Core\Exception\SiteNotFoundException;
use TYPO3\CMS\Core\Routing\SiteMatcher;
use TYPO3\CMS\Core\Utility\GeneralUtility;
use TYPO3\CMS\Frontend\ContentObject\ContentObjectRenderer;

/**
 * Command controller: Data
 *
 * @package Redkiwi\TalismanInspiration\Command
 */
class DataCommandController extends \TYPO3\CMS\Extbase\Mvc\Controller\CommandController
{
    use InspirationSolr;

    /** @var ConnectionPool */
    protected $connectionPool;

    /** @var ContentObjectRenderer */
    protected $contentObjectRenderer;

    /** @var Builder */
    protected $documentBuilder;

    /** @var PageRepository */
    protected $pageRepository;

    /** @var Rootline */
    protected $rootLine;

    /** @var SiteMatcher */
    protected $siteMatcher;

    /**
     * DataCommandController constructor.
     *
     * @param ConnectionPool $connectionPool
     * @param ContentObjectRenderer $contentObjectRenderer
     * @param Builder $documentBuilder
     * @param PageRepository $pageRepository
     * @param SiteMatcher $siteMatcher
     */
    public function __construct(ConnectionPool $connectionPool, ContentObjectRenderer $contentObjectRenderer, Builder $documentBuilder, PageRepository $pageRepository, SiteMatcher $siteMatcher)
    {
        parent::__construct();
        $this->connectionPool = $connectionPool;
        $this->contentObjectRenderer = $contentObjectRenderer;
        $this->documentBuilder = $documentBuilder;
        $this->pageRepository = $pageRepository;
        $this->rootLine = GeneralUtility::makeInstance(Rootline::class, /** @scrutinizer ignore-type */ '');
        $this->siteMatcher = $siteMatcher;
    }

    /**
     * Updates the solr index with the inspiration data. Use for initial setup of the inspiration solr index (existing pages which are not added by the PageFieldUpdateHook).
     *
     * @return bool
     * @throws \Exception
     */
    public function indexAllCommand(): bool
    {
        /** @var SolrWriteService $solrWriteService */
        $solrWriteService = $this->getSolrConnection()->getWriteService();

        $pages = $this->pageRepository->findInspirationIndex();
        $updated = 0;
        $errors = 0;

        /** @var \Redkiwi\TalismanPage\Domain\Model\Page $page */
        foreach ($pages as $page) {
            [$updated, $errors] = $this->addDocument($page, $solrWriteService, $updated, $errors);
        }
        $solrWriteService->commit();
        $this->outputLine('Added %d document(s) to the Solr index with %d error(s)', [$updated, $errors]);
        return true;
    }

    /**
     * Pushes changes made in the page tree to solr. Use this as scheduler task (run once every 5 mins)
     *
     * @return bool
     * @throws \Exception
     */
    public function queueRunnerCommand(): bool
    {
        /** @var SolrWriteService $solrWriteService */
        $solrWriteService = $this->getSolrConnection()->getWriteService();

        $rows = $this->connectionPool
            ->getQueryBuilderForTable('tx_talismaninspiration_solr_queue')
            ->select('page_id', 'action')
            ->from('tx_talismaninspiration_solr_queue')
            ->setMaxResults(10)
            ->execute()
            ->fetchAll();

        $deleted = 0;
        $updated = 0;
        $errors = 0;

        /** @var array $row */
        foreach ($rows as $row) {
            if ($row['action'] === 'delete') {
                $response = $solrWriteService->deleteByQuery('uid_i:' . $row['page_id']);
                if ($response->getHttpStatus() === 200) {
                    $deleted++;
                }
            } else {
                if ($row['action'] === 'update') {
                    /** @var Page $page */
                    $page = $this->pageRepository->findByUid($row['page_id']);

                    [$updated, $errors] = $this->addDocument($page, $solrWriteService, $updated, $errors);
                }
            }

            /** @var QueryBuilder $deleteQueryBuilder */
            $deleteQueryBuilder = $this->connectionPool
                ->getQueryBuilderForTable('tx_talismaninspiration_solr_queue');

            $deleteQueryBuilder
                ->delete('tx_talismaninspiration_solr_queue')
                ->where($deleteQueryBuilder->expr()->eq('page_id', $deleteQueryBuilder->createNamedParameter($row['page_id'], \PDO::PARAM_INT)))
                ->execute();
        }
        $solrWriteService->commit();
        $this->outputLine('Added/updated %d document(s) with %s error(s) and removed %d document(s) from the solr index', [$updated, $errors, $deleted]);
        return true;
    }

    /**
     * @param Page $page
     * @param SolrWriteService $solrWriteService
     * @param int $updated
     * @param int $errors
     * @return array
     * @throws SiteNotFoundException
     */
    protected function addDocument(Page $page, SolrWriteService $solrWriteService, int $updated, int $errors): array
    {
        $document = null;
        try {
            $document = $this->documentBuilder->fromRecord(
                $page->_getProperties(),
                'pages',
                $this->getRootPageUid($page->getUid()),
                $this->rootLine
            );
        } catch (SiteNotFoundException $e) {
        }
        if($document !== null) {
            /** @var Page $page */
            foreach ($page->getInspirationFields() as $field => $value) {
                $document->setField($field, $value);
            }
            $document->setField('link_s', $this->getLink($page->getUid()));

            $response = $solrWriteService->addDocuments([$document]);
            if ($response->getHttpStatus() === 200) {
                $updated++;
            } else {
                $errors++;
            }
        } else {
            $errors++;
        }

        return [$updated, $errors];
    }

    /**
     * Generate link based on current page information
     *
     * @param integer $pageId
     * @return string
     */
    protected function getLink(int $pageId): string
    {
        return $this->contentObjectRenderer->typoLink_URL(['parameter' => $pageId]);
    }

    /**
     * @param int $pageUid
     * @return int
     */
    protected function getRootPageUid(int $pageUid): int
    {
        try {
            return $this->siteMatcher->matchByPageId($pageUid)->getRootPageId();
        } catch (SiteNotFoundException $e) {
        }

        // on any errors, fallback to page ID 1
        return 1;
    }
}