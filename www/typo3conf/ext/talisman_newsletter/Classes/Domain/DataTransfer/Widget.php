<?php
namespace Redkiwi\TalismanNewsletter\Domain\DataTransfer;

/**
 * Data transfer: Widget
 *
 * @package Redkiwi\TalismanNewsletter\Domain\DataTransfer
 */
class Widget
{
    /**
     * @var string
     * @validate NotEmpty, EmailAddress
     */
    protected $email;

    /**
     * @return string
     */
    public function getEmail()
    {
        return $this->email;
    }

    /**
     * @param string $email
     * @return void
     */
    public function setEmail($email)
    {
        $this->email = $email;
    }
}
