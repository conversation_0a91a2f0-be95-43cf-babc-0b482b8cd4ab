module.exports = function (grunt) {
    const sass = require('sass');
    require('time-grunt')(grunt);

    grunt.initConfig({
        directories: {
            extension: '../../',
            bootstrap: 'node_modules/bootstrap-sass/assets/',
            bootstrapSlider: 'node_modules/bootstrap-slider/dist/',
            bootstrapSelect: 'node_modules/bootstrap-select/dist/',
            bootstrapDatepicker: 'node_modules/bootstrap-datepicker/dist/',
            matchHeight: 'node_modules/jquery-match-height/dist/',
            jquery: 'node_modules/jquery/dist/',
            swiper: 'node_modules/swiper/',
            assets: '../Resources/Private/',
            compress: '../Resources/Public/'
        },
        extensions: {
            solr: '../../solr'
        },
        files: {
            sass: {
                main: '<%= directories.assets %>Sass/Main.scss'
            },
            javascript: {
                footer: [
                    '<%= directories.jquery %>jquery.js',
                    '<%= directories.bootstrap %>javascripts/bootstrap.js',
                    '<%= directories.bootstrapSlider %>bootstrap-slider.js',
                    '<%= directories.bootstrapSelect %>js/bootstrap-select.js',
                    '<%= directories.bootstrapDatepicker %>js/bootstrap-datepicker.js',
                    '<%= directories.matchHeight %>jquery.matchHeight.js',
                    '<%= directories.jquerySticky %>jquery.sticky.js',
                    '<%= directories.swiper %>dist/js/swiper.min.js',

                    '<%= directories.assets %>JavaScript/Library/jquery-ui.js',
                    '<%= directories.assets %>JavaScript/Library/jquery.mobile.custom.js',
                    '<%= directories.assets %>JavaScript/Library/markerclusterer.js',
                    '<%= directories.assets %>JavaScript/Library/infobox.js',
                    '<%= directories.assets %>JavaScript/Library/parsley.min.js',

                    '<%= directories.assets %>JavaScript/Main.js',
                    '<%= directories.assets %>JavaScript/Functions.js',
                    '<%= directories.assets %>JavaScript/Layout.js',
                    '<%= directories.assets %>JavaScript/Form.js',

                    '<%= directories.assets %>JavaScript/Extensions/FeedbackCompanyWidget.js',
                    '<%= directories.assets %>JavaScript/Extensions/Gridelements.js',
                    '<%= directories.assets %>JavaScript/Extensions/TalismanAjaxLogin.js',
                    '<%= directories.assets %>JavaScript/Extensions/TalismanAppointment.js',
                    '<%= directories.assets %>JavaScript/Extensions/TalismanBlog.js',
                    '<%= directories.assets %>JavaScript/Extensions/TalismanBootstrapCarousel.js',
                    '<%= directories.assets %>JavaScript/Extensions/KoningPostcode.js',
                    '<%= directories.assets %>JavaScript/Extensions/TalismanAccommodations.js',
                    '<%= directories.assets %>JavaScript/Extensions/TalismanQuoteForm.js',
                    '<%= directories.assets %>JavaScript/Extensions/TalismanTravelogues.js',
                    '<%= directories.assets %>JavaScript/Extensions/TalismanLikes.js',
                    '<%= directories.assets %>JavaScript/Extensions/TalismanInspiration.js',
                    '<%= directories.assets %>JavaScript/Extensions/TalismanMap.js',
                    '<%= directories.assets %>JavaScript/Extensions/TalismanDestinations.js',
                    '<%= directories.assets %>JavaScript/Extensions/TalismanExampleTrip.js',
                    '<%= directories.assets %>JavaScript/Extensions/Solr.js',
                    '<%= directories.assets %>JavaScript/Extensions/Swiper.js',
                    '<%= extensions.solr %>/Resources/JavaScript/EidSuggest/suggest.js'
                ]
            }
        },
        sass: {
            files: {
                '<%= directories.compress %>Css/Compiled.css': '<%= files.sass.main %>'
            },
            development: {
                options: {
                    implementation: sass,
                    sourceComments: true,
                    sourceMap: false,
                    outputStyle: 'expanded'
                },
                files: '<%= sass.files %>'
            },
            default: {
                options: {
                    implementation: sass,
                    outputStyle: 'compressed'
                },
                files: '<%= sass.files %>'
            }
        },
        concat: {
            footer: {
                src: '<%= files.javascript.footer %>',
                dest: '<%= directories.compress %>JavaScript/Footer.js'
            }
        },
        uglify: {
            options: {
                compress: {
                    drop_console: true
                }
            },
            build: {
                files: {
                    '<%= directories.compress %>JavaScript/Footer.js': '<%= directories.compress %>JavaScript/Footer.js'
                }
            }
        },
        watch: {
            options: {
                atBegin: true
            },
            sass: {
                files: [
                    '<%= directories.assets %>Sass/**/*.scss'
                ],
                tasks: ['sass:development']
            },
            javascript: {
                files: [
                    '<%= files.javascript.footer %>'
                ],
                tasks: ['concat:footer']
            }
        }
    });

    grunt.loadNpmTasks('grunt-sass');
    grunt.loadNpmTasks('grunt-contrib-concat');
    grunt.loadNpmTasks('grunt-contrib-uglify');
    grunt.loadNpmTasks('grunt-contrib-watch');

    grunt.registerTask('development', ['sass:development', 'concat']);
    grunt.registerTask('default', ['sass:default', 'concat', 'uglify']);
};
