<?php
namespace Redkiwi\TalismanInspiration\Install\Updates;

use Redkiwi\TalismanTemplate\Domain\Model\Category;
use Redkiwi\TalismanPage\Domain\Model\CategoryRating;
use TYPO3\CMS\Core\Database\ConnectionPool;
use TYPO3\CMS\Core\Database\Query\QueryBuilder;
use TYPO3\CMS\Core\Utility\GeneralUtility;
use TYPO3\CMS\Install\Updates\UpgradeWizardInterface;

class InspirationCategoryValidation implements UpgradeWizardInterface
{
    /**
     * Return the identifier for this wizard
     * This should be the same string as used in the ext_localconf class registration
     *
     * @return string
     */
    public function getIdentifier(): string {
        return 'InspirationCategoryValidation';
    }

    /**
     * Return the speaking name of this wizard
     *
     * @return string
     */
    public function getTitle(): string {
        return 'Validate & Update inspiration & specials categories per page';
    }

    /**
     * Return the description for this wizard
     *
     * @return string
     */
    public function getDescription(): string {
        return 'Validate & Update inspiration & specials categories per page';
    }

    /**
     * Execute the update
     *
     * Called when a wizard reports that an update is necessary
     *
     * @return bool
     */
    public function executeUpdate(): bool {
        $changes = 0;
        try {
            foreach ($this->getPageRepository()->findInspirationIndex() as $page) {
                $update = false;

                /**
                 * @var \Redkiwi\TalismanPage\Domain\Model\Page $page
                 * @var \Redkiwi\TalismanTemplate\Domain\Model\Category $category
                 * @var \Redkiwi\TalismanPage\Domain\Model\CategoryRating $categoryRating
                 * @var \TYPO3\CMS\Extbase\Persistence\ObjectStorage $categories
                 * @var \TYPO3\CMS\Extbase\Persistence\ObjectStorage $categoryRatings
                 */
                $categories = $this->getObjectManager()->get(\TYPO3\CMS\Extbase\Persistence\ObjectStorage::class);
                $categoryRatings = $this->getObjectManager()->get(\TYPO3\CMS\Extbase\Persistence\ObjectStorage::class);

                // Validate each category if its still accurate
                foreach ($page->getCategories() as $category) {
                    if ($category->getPid() === Category::FOLDER_RATEABLE) {
                        // It should be migrated to the CategoryRating and removed from this page
                        $categoryRating = $this->getObjectManager()->get(CategoryRating::class);
                        $categoryRating->setPid($page->getPid());
                        $categoryRating->setCategory($category);
                        // Set default rating for migration to max rating
                        $categoryRating->setRating(CategoryRating::RATING_MAXIMUM);

                        // Store
                        $categoryRatings->attach($categoryRating);
                        $update = true;
                    } else {
                        $categories->attach($category);
                    }
                }

                foreach ($page->getCategoryRatings() as $categoryRating) {
                    if ($categoryRating->isValid()) {
                        if ($categoryRating->getCategory()->getPid() !== Category::FOLDER_RATEABLE) {
                            // Should be migrated to default page category
                            $categories->attach($categoryRating->getCategory());
                            $update = true;
                        } else {
                            $categoryRatings->attach($categoryRating);
                        }
                    } else {
                        $update = true;
                    }
                }

                if ($update === true) {
                    $page->setCategories($categories);
                    $page->setCategoryRatings($categoryRatings);
                    $changes++;
                    $this->getPageRepository()->update($page);
                }
            }
            if ($changes > 0) {
                // Persist!
                $customMessages[] = 'Please invoke the reference index for total (' . $changes . ') changes.';
            } else {
                $customMessages[] = 'No changes made';
            }
            $this->getPersistenceManager()->persistAll();

        } catch (\Exception $e) {

        }

        // Always validate category rating tables
        $this->validateCategoryRatingTable();
        return true;
    }

    /**
     * Is an update necessary?
     *
     * Is used to determine whether a wizard needs to be run.
     * Check if data for migration exists.
     *
     * @return bool
     */
    public function updateNecessary(): bool {
        // @todo Figure out why this always returns true (before Upgrade to TYPO3 v9, this used to be checkForUpdate() which also returned true)
        return true;
    }

    /**
     * Returns an array of class names of Prerequisite classes
     *
     * This way a wizard can define dependencies like "database up-to-date" or
     * "reference index updated"
     *
     * @return string[]
     */
    public function getPrerequisites(): array {
        return [];
    }

    /**
     * Persistence manager could not work with mm_table and stored the uid in the mm location
     * This just reinserts the right table setup
     *
     * @return void
     */
    protected function validateCategoryRatingTable()
    {
        /** @var QueryBuilder $selectQueryBuilder */
        $selectQueryBuilder = $this->getQueryBuilder();
        $invalidRecords = $selectQueryBuilder->select('uid', 'page_id', 'category', 'rating')
            ->from(CategoryRating::TABLE)
            ->where(
                $selectQueryBuilder->expr()->gt('category', 1),
                $selectQueryBuilder->expr()->eq('deleted', 0)
            )
            ->execute()
            ->fetchAll();

        if($invalidRecords) {
            foreach ($invalidRecords as $row) {

                /** @var QueryBuilder $countQueryBuilder */
                $countQueryBuilder = $this->getQueryBuilder();
                $mmRecords = $countQueryBuilder
                    ->count('uid_local')
                    ->from(CategoryRating::MM_CATEGORY_TABLE)
                    ->where(
                        $countQueryBuilder->expr()->eq('uid_foreign', $countQueryBuilder->createNamedParameter($row['uid'], \PDO::PARAM_INT)),
                        $countQueryBuilder->expr()->eq('tablenames', $countQueryBuilder->createNamedParameter(CategoryRating::TABLE, \PDO::PARAM_STR)),
                        $countQueryBuilder->expr()->eq('fieldname', $countQueryBuilder->createNamedParameter('category', \PDO::PARAM_STR))
                    )
                    ->execute()
                    ->fetchColumn(0);

                if ((int)$mmRecords === 0) {

                    /** @var QueryBuilder $insertQueryBuilder */
                    $insertQueryBuilder = $this->getQueryBuilder();
                    $insertQueryBuilder
                        ->insert(CategoryRating::MM_CATEGORY_TABLE)
                        ->values([
                            'uid_local' => $row['category'],
                            'uid_foreign' => $row['uid'],
                            'tablenames' => CategoryRating::TABLE,
                            'fieldname' => 'category',
                        ])
                        ->execute();

                    /** @var QueryBuilder $updateQueryBuilder */
                    $updateQueryBuilder = $this->getQueryBuilder();
                    $updateQueryBuilder
                        ->update(CategoryRating::TABLE)
                        ->where(
                            $updateQueryBuilder->expr()->eq('uid',$countQueryBuilder->createNamedParameter($row['uid'], \PDO::PARAM_INT))
                        )
                        ->set('tstamp', time())
                        ->set('category', 1)
                        ->execute();
                }
            }
        }
    }

    /**
     * @return \TYPO3\CMS\Extbase\Object\ObjectManagerInterface
     */
    protected function getObjectManager()
    {
        return \TYPO3\CMS\Core\Utility\GeneralUtility::makeInstance(\TYPO3\CMS\Extbase\Object\ObjectManager::class);
    }

    /**
     * @return \Redkiwi\TalismanPage\Domain\Repository\PageRepository
     */
    protected function getPageRepository()
    {
        return $this->getObjectManager()->get(\Redkiwi\TalismanPage\Domain\Repository\PageRepository::class);
    }

    /**
     * @return \TYPO3\CMS\Extbase\Persistence\PersistenceManagerInterface
     */
    protected function getPersistenceManager()
    {
        return $this->getObjectManager()->get(\TYPO3\CMS\Extbase\Persistence\Generic\PersistenceManager::class);
    }

    /**
     * @return QueryBuilder
     */
    private function getQueryBuilder()
    {
        return GeneralUtility::makeInstance(ConnectionPool::class)->getQueryBuilderForTable(CategoryRating::TABLE);
    }
}
