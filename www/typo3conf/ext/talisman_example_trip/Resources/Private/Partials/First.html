<div class="slides slide slide-first item active">

    <!-- Regular screen first slide -->
    <div class="hidden-xs">
        <div class="carousel-timeline">
            <div class="row">
                <div class="col-sm-6">
                    <f:link.typolink parameter="{settings.pages.root}">
                        <img src="/typo3conf/ext/talisman_template/Resources/Public/Images/logo.svg" class="talisman-logo" width="169" height="84">
                    </f:link.typolink>
                </div>
                <div class="col-sm-6 pull-right">
                    <div class="day-container">
                        <div class="container-fluid">
                            <div class="row no-gutter">
                                <div class="col-sm-10 col-sm-offset-2">
                                    {slide.dayText}
                                    <div class="day-wrapper">
                                        <div class="circle"></div><hr>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="carousel-slide">
            <div class="row">
                <div class="col-sm-6">
                    {slide.description -> f:format.html()}
                    <f:if condition="{settings.data.map}">
                        <div class="map">
                            <img src="/{settings.data.map}" class="img-responsive" />
                        </div>
                    </f:if>
                </div>
            </div>
            <div class="promo-wrapper">
                <div class="promo-title">
                    <f:format.nl2br>{slide.title}</f:format.nl2br>
                </div>
                <div class="promo-subtitle">
                    <f:format.nl2br>{slide.subtitle}</f:format.nl2br>
                </div>
            </div>
            <f:if condition="{slide.visual.uid}">
                <div class="background-image" style="background-image: url('/{slide.visual.originalResource.publicUrl}'); background-position: {slide.visualPosition};"></div>
            </f:if>
        </div>
    </div>

    <!-- Mobile first slide -->
    <div class="visible-xs">
        <div class="mobile-slide-title">
            <div class="container">
                <div class="mobile-title">
                    <f:format.nl2br>{slide.title}</f:format.nl2br>
                </div>
                <f:if condition="{slide.subtitle}">
                    <div class="mobile-subtitle">
                        <f:format.nl2br>{slide.subtitle}</f:format.nl2br>
                    </div>
                </f:if>
            </div>
        </div>
        <div class="carousel-slide">

            <div class="container">

                {slide.description -> f:format.html()}

                <div class="swipe">
                    <img src="/typo3conf/ext/talisman_template/Resources/Public/Images/swipe.gif" width="100" height="130" />
                </div>

            </div>
        </div>
    </div>

</div>
