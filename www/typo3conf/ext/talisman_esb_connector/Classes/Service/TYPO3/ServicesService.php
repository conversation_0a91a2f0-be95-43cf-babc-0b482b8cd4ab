<?php
namespace Redkiwi\TalismanEsbConnector\Service\TYPO3;

use Redkiwi\TalismanPage\Domain\Model\Page;

/**
 * Service: Services
 *
 * @package Redkiwi\TalismanEsbConnector\TYPO3\Service
 */
class ServicesService
{
    /** @var array */
    protected $geoTreeIdToCountryPageUidMapping = [];

    /** @var array */
    protected $countryPageUidToAccommodationPageUidMapping = [];

    /** @var array */
    protected $countryPageUidToNewStorageFolderPageUidMapping = [];

    /**
     * @param array $services
     * @param string $type
     * @return void
     */
    public function syncServices(array $services, $type)
    {
        $handledTravelStudioServiceIds = [];
        foreach ($services as $service) {
            $countryPageUid = $this->getServiceCountry($service['travelStudioGeoTreeId']);
            $handledTravelStudioServiceIds[] = $service['travelStudioId'];
            if ($countryPageUid !== false) {
                switch ($type) {
                    case 'accommodation':
                        $this->saveAccommodations($service, $countryPageUid);
                        break;
                    case 'excursion':
                        $this->saveToursAndExcursions(Page::DOKTYPE_EXCURSION, $service, $countryPageUid);
                        break;
                    case 'tour':
                        $this->saveToursAndExcursions(Page::DOKTYPE_TOUR, $service, $countryPageUid);
                        break;
                }
            }
        }

        if (!empty($handledTravelStudioServiceIds)) {
            $dokType = null;
            switch ($type) {
                case 'accommodation':
                    $dokType = Page::DOKTYPE_ACCOMMODATION;
                    break;
                case 'excursion':
                    $dokType = PAGE::DOKTYPE_EXCURSION;
                    break;
                case 'tour':
                    $dokType = Page::DOKTYPE_TOUR;
                    break;
            }
            $this->disableServices($dokType, $handledTravelStudioServiceIds);
        }
    }

    /**
     * Disable all services that weren't found in the response, but are found locally in TYPO3
     *
     * @param int $dokType
     * @param array $excludeTravelStudioServiceIdList
     * @return void
     */
    protected function disableServices($dokType, $excludeTravelStudioServiceIdList)
    {
        $this->getDatabaseConnection()->exec_UPDATEquery(
            'pages',
            'doktype = ' . (int) $dokType . ' AND tx_talismanesbconnector_service_id NOT IN(' . implode(',', $excludeTravelStudioServiceIdList) . ') AND deleted = 0 AND hidden = 0',
            [
                'hidden' => 1
            ]
        );
    }

    /**
     * Tours and Excursions are saved in the "Nieuw" folder under Country => Te doen => Nieuw
     *
     * Unless they already exist
     *
     * @param int $dokType
     * @param array $service
     * @param int $countryPageUid
     * @return void
     */
    protected function saveToursAndExcursions($dokType, $service, $countryPageUid)
    {
        $this->getCountryPageUidToNewStorageFolderPageUidMapping($countryPageUid);

        $servicePage = $this->getServicePageByServiceId($service['travelStudioId']);
        if ($servicePage !== false) {
            $fields = array_merge(
                $this->getServicePageFields(
                    $service,
                    $this->countryPageUidToNewStorageFolderPageUidMapping[$countryPageUid],
                    $dokType
                ), $this->getStandardPageFields()
            );
            unset($fields['crdate']);
            unset($fields['hidden']);
            unset($fields['pid']);

            $this->getDatabaseConnection()->exec_UPDATEquery(
                'pages',
                'uid = ' . (int) $servicePage['uid'],
                $fields
            );
        } else {
            if ($this->countryPageUidToNewStorageFolderPageUidMapping[$countryPageUid] !== false) {
                $fields = array_merge(
                    $this->getServicePageFields(
                        $service,
                        $this->countryPageUidToNewStorageFolderPageUidMapping[$countryPageUid],
                        $dokType
                    ), $this->getStandardPageFields()
                );

                $this->getDatabaseConnection()->exec_INSERTquery(
                    'pages',
                    $fields
                );
            }
        }
    }

    /**
     * Select child pages of $countryPageUid, then find the "Nieuw" storage page and save it in internal array
     *
     * @param int $countryPageUid
     * @return void
     */
    protected function getCountryPageUidToNewStorageFolderPageUidMapping($countryPageUid)
    {
        if (!isset($this->countryPageUidToNewStorageFolderPageUidMapping[$countryPageUid])) {
            $childPagesOfCountry = $this->getDatabaseConnection()->exec_SELECTgetRows(
                'uid',
                'pages',
                'pid = ' . (int) $countryPageUid
            );

            $pids = [];
            foreach ($childPagesOfCountry as $childPageOfCountry) {
                $pids[] = $childPageOfCountry['uid'];
            }

            $storagePage = $this->getDatabaseConnection()->exec_SELECTgetSingleRow(
                'uid',
                'pages',
                'title = \'Nieuw\' AND doktype = 254 AND pid IN(' . implode(',', $pids) . ') AND deleted = 0'
            );

            if (is_array($storagePage)) {
                $this->countryPageUidToNewStorageFolderPageUidMapping[$countryPageUid] = $storagePage['uid'];
            } else {
                $this->countryPageUidToNewStorageFolderPageUidMapping[$countryPageUid] = false;
            }
        }
    }

    /**
     * Accommodations pages are saved under "Accommodaties" under the Country page
     *
     * @param array $accommodation
     * @param int $countryPageUid
     * @return void
     */
    protected function saveAccommodations($accommodation, $countryPageUid)
    {
        $this->getCountryPageUidToAccommodationPageUidMapping($countryPageUid);

        $servicePage = $this->getServicePageByServiceId($accommodation['travelStudioId']);
        if ($servicePage !== false) {
            $fields = array_merge(
                $this->getServicePageFields(
                    $accommodation,
                    $this->countryPageUidToAccommodationPageUidMapping[$countryPageUid],
                    Page::DOKTYPE_ACCOMMODATION
                ), $this->getStandardPageFields()
            );
            unset($fields['crdate']);
            unset($fields['hidden']);

            $this->getDatabaseConnection()->exec_UPDATEquery(
                'pages',
                'uid = ' . (int) $servicePage['uid'],
                $fields
            );
        } else {
            if ($this->countryPageUidToAccommodationPageUidMapping[$countryPageUid] !== false) {
                $fields = array_merge(
                    $this->getServicePageFields(
                        $accommodation,
                        $this->countryPageUidToAccommodationPageUidMapping[$countryPageUid],
                        Page::DOKTYPE_ACCOMMODATION
                    ), $this->getStandardPageFields()
                );

                $this->getDatabaseConnection()->exec_INSERTquery(
                    'pages',
                    $fields
                );
            }
        }
    }

    /**
     * Select the Accommodaties child page of $countryPageUid and save it in internal array
     *
     * @param int $countryPageUid
     * @return void
     */
    protected function getCountryPageUidToAccommodationPageUidMapping($countryPageUid)
    {
        if (!isset($this->countryPageUidToAccommodationPageUidMapping[$countryPageUid])) {
            $accommodationPage = $this->getDatabaseConnection()->exec_SELECTgetSingleRow(
                'uid',
                'pages',
                'pid = ' . (int) $countryPageUid . ' AND title = \'Accommodaties\' AND deleted = 0'
            );
            if (is_array($accommodationPage)) {
                $this->countryPageUidToAccommodationPageUidMapping[$countryPageUid] = $accommodationPage['uid'];
            } else {
                $this->countryPageUidToAccommodationPageUidMapping[$countryPageUid] = false;
            }
        }
    }

    /**
     * @param int $travelStudioGeoTreeId
     * @return bool|int
     */
    protected function getServiceCountry($travelStudioGeoTreeId)
    {
        if (!isset($this->geoTreeIdToCountryPageUidMapping[$travelStudioGeoTreeId])) {
            $country = $this->getDatabaseConnection()->exec_SELECTgetSingleRow(
                'uid',
                'pages',
                'tx_talismanesbconnector_geo_tree_id = ' . (int) $travelStudioGeoTreeId . ' AND deleted = 0'
            );
            if (is_array($country)) {
                $this->geoTreeIdToCountryPageUidMapping[$travelStudioGeoTreeId] = $country['uid'];
            } else {
                $this->geoTreeIdToCountryPageUidMapping[$travelStudioGeoTreeId] = false;
            }
        }
        return $this->geoTreeIdToCountryPageUidMapping[$travelStudioGeoTreeId];
    }

    /**
     * @param int $serviceId
     * @return array|bool
     */
    protected function getServicePageByServiceId($serviceId)
    {
        $page = $this->getDatabaseConnection()->exec_SELECTgetSingleRow(
            'uid',
            'pages',
            'tx_talismanesbconnector_service_id = ' . (int) $serviceId . ' AND deleted = 0'
        );
        return (is_array($page)) ? $page : false;
    }

    /**
     * @return array
     */
    protected function getStandardPageFields()
    {
        return [
            'crdate' => time(),
            'tstamp' => time(),
            'perms_user' => 31,
            'perms_group' => 31,
            'perms_userid' => 1,
            'perms_groupid' => 1,
            'hidden' => 1
        ];
    }

    /**
     * @param array $service
     * @param int $pid
     * @param int $dokType
     * @return array
     */
    protected function getServicePageFields(array $service, $pid, $dokType)
    {
        return [
            'pid' => $pid,
            'doktype' => $dokType,
            'title' => $service['longName'],
            'tx_talismanesbconnector_service_id' => $service['travelStudioId'],
            'tx_talismanesbconnector_latitude' => $service['latitude'],
            'tx_talismanesbconnector_longitude' => $service['longitude'],
        ];
    }

    /**
     * @return \TYPO3\CMS\Core\Database\DatabaseConnection
     */
    protected function getDatabaseConnection()
    {
        return $GLOBALS['TYPO3_DB'];
    }
}
