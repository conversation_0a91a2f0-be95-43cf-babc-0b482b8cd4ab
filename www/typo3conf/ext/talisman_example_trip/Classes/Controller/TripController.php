<?php
namespace <PERSON><PERSON>wi\TalismanExampleTrip\Controller;

use Red<PERSON>wi\TalismanExampleTrip\Domain\Repository\SlideRepository;
use Redkiwi\TalismanExperts\Domain\Repository\ExpertRepository;
use TYPO3\CMS\Frontend\Controller\TypoScriptFrontendController;

/**
 * Controller: Trip
 *
 * @package Redkiwi\TalismanExampleTrip\Controller
 */
class TripController extends \TYPO3\CMS\Extbase\Mvc\Controller\ActionController
{
    /** @var SlideRepository */
    protected $slideRepository;

    /** * @var ExpertRepository */
    protected $expertRepository;

    public function __construct(SlideRepository $slideRepository, ExpertRepository $expertRepository)
    {
        parent::__construct();
        $this->slideRepository = $slideRepository;
        $this->expertRepository = $expertRepository;
    }

    /**
     * @return void
     */
    public function showAction()
    {
        $expert = false;
        if (isset($this->settings['data']['expert'])) {
            $expert = $this->expertRepository->findByUid($this->settings['data']['expert']);
        }

        $currentId = (int) $this->configurationManager->getContentObject()->getFieldVal('uid');
        $header = $this->configurationManager->getContentObject()->getFieldVal('header');

        $this->view->assignMultiple([
            'experts' => [$expert],
            'slides' => $this->slideRepository->findByContentId($currentId),
            'parentId' => $this->getTypoScriptFrontendController()->page['pid'],
            'header' => $header,
            'pageId' => $this->getTypoScriptFrontendController()->id
        ]);
    }

    /**
     * @return TypoScriptFrontendController
     */
    protected function getTypoScriptFrontendController()
    {
        return $GLOBALS['TSFE'];
    }
}
