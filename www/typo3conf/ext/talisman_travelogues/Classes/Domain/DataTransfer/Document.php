<?php
namespace Redkiwi\TalismanTravelogues\Domain\DataTransfer;

/**
 * Data transfer: Document
 *
 * @package Redkiwi\TalismanTravelogues\Domain\DataTransfer
 */
class Document
{
    /**
     * @var string
     * @validate NotEmpty
     */
    protected $destinations;

    /**
     * @var string
     * @validate NotEmpty
     */
    protected $title;

    /**
     * @var boolean
     * @validate Boolean(is=true)
     */
    protected $agree;

    /**
     * @var array
     * @validate \Redkiwi\TalismanTemplate\Validation\Validator\SingleFileValidator(allowedMimeTypes="text/plain,application/msword,application/pdf,application/vnd.openxmlformats-officedocument.wordprocessingml.document", required=true)
     */
    protected $document;

    /**
     * @return string
     */
    public function getDestinations()
    {
        return $this->destinations;
    }

    /**
     * @param string $destinations
     * @return void
     */
    public function setDestinations($destinations)
    {
        $this->destinations = $destinations;
    }

    /**
     * @return string
     */
    public function getTitle()
    {
        return $this->title;
    }

    /**
     * @param string $title
     * @return void
     */
    public function setTitle($title)
    {
        $this->title = $title;
    }

    /**
     * @return boolean
     */
    public function isAgree()
    {
        return $this->agree;
    }

    /**
     * @param boolean $agree
     * @return void
     */
    public function setAgree($agree)
    {
        $this->agree = $agree;
    }

    /**
     * @return array
     */
    public function getDocument()
    {
        return $this->document;
    }

    /**
     * @param array $document
     * @return void
     */
    public function setDocument($document)
    {
        $this->document = $document;
    }
}
