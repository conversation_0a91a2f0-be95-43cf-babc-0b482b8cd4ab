<?php
namespace Red<PERSON>wi\TalismanAskAQuestion\Controller;

use Redki<PERSON>\TalismanQuoteForm\Controller\AbstractFormController;
use TYPO3\CMS\Core\Utility\GeneralUtility;

/**
 * Controller: Form
 *
 * @package Redkiwi\TalismanAskAQuestion\Controller
 */
class FormController extends AbstractFormController
{

    /**
     * @return void
     */
    public function showAction()
    {
        $this->view->assign('heardOfTalismanAnswers', $this->getHeardOfTalismanAnswers());
    }

    /**
     * @param \Redkiwi\TalismanAskAQuestion\Domain\DataTransfer\Form $form
     * @return void
     * @throws \TYPO3\CMS\Extbase\Mvc\Exception\StopActionException
     * @throws \TYPO3\CMS\Extbase\Mvc\Exception\UnsupportedRequestTypeException
     */
    public function handleAction(\Redkiwi\TalismanAskAQuestion\Domain\DataTransfer\Form $form)
    {
        $form->loadTrackingValues($_COOKIE);

        $recipients = GeneralUtility::trimExplode(',', $this->settings['data']['recipientEmail']);
        $recipients = array_fill_keys($recipients, $this->settings['data']['recipientName']);

        /** @var \Keizer\KoningLibrary\Service\MailService $mailerService */
        $mailerService = GeneralUtility::makeInstance('Keizer\\KoningLibrary\\Service\\MailService');
        $mailerService
            ->setTemplateRootPaths([
                'EXT:talisman_ask_a_question/Resources/Private/Templates/Email'
            ])
            ->setLayoutRootPaths([
                'EXT:talisman_template/Resources/Private/Layouts'
            ])
            ->sendMail(
                $recipients,
                [$this->settings['data']['senderEmail'] => $this->settings['data']['senderName']],
                $this->settings['data']['subject'],
                'FormNotification',
                [],
                [
                    'fields' => $form,
                    'subject' => $this->settings['data']['subject']
                ]
            );

        $this->redirect('finished');
    }

    /**
     * @return void
     */
    public function finishedAction()
    {
    }
}
